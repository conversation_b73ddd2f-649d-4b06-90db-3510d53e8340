import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  bool _autoDownloadEnabled = false;
  bool _offlineModeEnabled = false;
  String _selectedLanguage = 'English';
  String _selectedQuality = 'HD';

  final List<String> _languages = ['English', 'Hindi', 'Bengali'];
  final List<String> _qualities = ['SD', 'HD', 'Full HD'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Settings
            _buildSectionHeader('Account'),
            _buildSettingsSection([
              _buildSettingsTile(
                'Edit Profile',
                'Update your personal information',
                Icons.person,
                () => _editProfile(),
              ),
              _buildSettingsTile(
                'Change Password',
                'Update your account password',
                Icons.lock,
                () => _changePassword(),
              ),
              _buildSettingsTile(
                'Privacy Settings',
                'Manage your privacy preferences',
                Icons.privacy_tip,
                () => _privacySettings(),
              ),
            ]),

            const SizedBox(height: 24),

            // App Preferences
            _buildSectionHeader('App Preferences'),
            _buildSettingsSection([
              _buildSwitchTile(
                'Push Notifications',
                'Receive notifications for new content',
                Icons.notifications,
                _notificationsEnabled,
                (value) => setState(() => _notificationsEnabled = value),
              ),
              _buildSwitchTile(
                'Dark Mode',
                'Use dark theme for the app',
                Icons.dark_mode,
                _darkModeEnabled,
                (value) => setState(() => _darkModeEnabled = value),
              ),
              _buildDropdownTile(
                'Language',
                'Select your preferred language',
                Icons.language,
                _selectedLanguage,
                _languages,
                (value) => setState(() => _selectedLanguage = value!),
              ),
            ]),

            const SizedBox(height: 24),

            // Download & Storage
            _buildSectionHeader('Download & Storage'),
            _buildSettingsSection([
              _buildSwitchTile(
                'Auto Download',
                'Automatically download new content',
                Icons.download,
                _autoDownloadEnabled,
                (value) => setState(() => _autoDownloadEnabled = value),
              ),
              _buildDropdownTile(
                'Video Quality',
                'Default video download quality',
                Icons.high_quality,
                _selectedQuality,
                _qualities,
                (value) => setState(() => _selectedQuality = value!),
              ),
              _buildSwitchTile(
                'Offline Mode',
                'Enable offline content access',
                Icons.offline_bolt,
                _offlineModeEnabled,
                (value) => setState(() => _offlineModeEnabled = value),
              ),
              _buildSettingsTile(
                'Manage Downloads',
                'View and manage downloaded content',
                Icons.folder_special,
                () => _manageDownloads(),
              ),
              _buildSettingsTile(
                'Clear Cache',
                'Free up storage space',
                Icons.cleaning_services,
                () => _clearCache(),
              ),
            ]),

            const SizedBox(height: 24),

            // Learning Preferences
            _buildSectionHeader('Learning'),
            _buildSettingsSection([
              _buildSettingsTile(
                'Study Reminders',
                'Set daily study reminders',
                Icons.alarm,
                () => _studyReminders(),
              ),
              _buildSettingsTile(
                'Progress Sync',
                'Sync progress across devices',
                Icons.sync,
                () => _progressSync(),
              ),
              _buildSettingsTile(
                'Bookmark Settings',
                'Manage bookmark preferences',
                Icons.bookmark_border,
                () => _bookmarkSettings(),
              ),
            ]),

            const SizedBox(height: 24),

            // Support & About
            _buildSectionHeader('Support & About'),
            _buildSettingsSection([
              _buildSettingsTile(
                'Help & FAQ',
                'Get answers to common questions',
                Icons.help_outline,
                () => _helpFaq(),
              ),
              _buildSettingsTile(
                'Contact Support',
                'Get help from our support team',
                Icons.support_agent,
                () => _contactSupport(),
              ),
              _buildSettingsTile(
                'Rate App',
                'Share your feedback on app stores',
                Icons.star_rate,
                () => _rateApp(),
              ),
              _buildSettingsTile(
                'Terms of Service',
                'Read our terms and conditions',
                Icons.description,
                () => _termsOfService(),
              ),
              _buildSettingsTile(
                'Privacy Policy',
                'Read our privacy policy',
                Icons.policy,
                () => _privacyPolicy(),
              ),
              _buildSettingsTile(
                'About',
                'App version and information',
                Icons.info_outline,
                () => _aboutApp(),
              ),
            ]),

            const SizedBox(height: 32),

            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _logout,
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // App Version
            Center(
              child: Text(
                'Version 1.0.0',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildSettingsSection(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSettingsTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: AppColors.primary, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
        ),
      ),
      trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
      onTap: onTap,
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: AppColors.primary, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: AppColors.primary, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: options.map((String option) {
          return DropdownMenuItem<String>(
            value: option,
            child: Text(option),
          );
        }).toList(),
      ),
    );
  }

  // Action methods
  void _editProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit Profile - Coming Soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _changePassword() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change Password - Coming Soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _privacySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Privacy Settings - Coming Soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _manageDownloads() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Manage Downloads - Coming Soon!'),
        backgroundColor: AppColors.secondary,
      ),
    );
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Are you sure you want to clear the app cache? This will free up storage space.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache cleared successfully!'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _studyReminders() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Study Reminders - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _progressSync() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Progress Sync - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _bookmarkSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Bookmark Settings - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _helpFaq() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Help & FAQ - Coming Soon!'),
        backgroundColor: AppColors.accent,
      ),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact Support - Coming Soon!'),
        backgroundColor: AppColors.accent,
      ),
    );
  }

  void _rateApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rate App - Coming Soon!'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _termsOfService() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Terms of Service - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _privacyPolicy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Privacy Policy - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _aboutApp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Student Learning App'),
            SizedBox(height: 8),
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text('Developed by: Augment Code'),
            SizedBox(height: 8),
            Text('© 2024 All rights reserved'),
            SizedBox(height: 16),
            Text('A comprehensive learning platform for competitive exam preparation.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout from your account?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Go back to previous screen
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Logged out successfully'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}