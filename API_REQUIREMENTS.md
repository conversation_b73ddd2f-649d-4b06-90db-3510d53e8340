# 🚀 **UTKRISHTA APP - API REQUIREMENTS DOCUMENT**

## 📋 **PROJECT OVERVIEW**

This document outlines the API requirements for the Utkrishta Educational App based on the implemented Flutter frontend. The app is designed for competitive exam preparation with features like video classes, PDFs, tests, live classes, and progress tracking.

### **Current Implementation Status:**

- ✅ **Authentication System**: Complete with OTP-based login/registration
- ✅ **Dashboard System**: User stats, activities, courses, announcements
- ✅ **Course Management**: Enrollment, progress tracking, recommendations
- ✅ **Exam System**: Categories, popular exams, filtering
- ✅ **State Management**: Provider pattern with real-time updates
- ✅ **UI Components**: Complete responsive design with animations

---

## 🔐 **PHASE 1: AUTHENTICATION & USER MANAGEMENT APIs**

### **1.1 Send OTP for Login/Registration**

**Endpoint:** `POST /api/auth/send-otp`

**Purpose:** Send OTP to user's phone number for login or registration

**Request Body:**

```json
{
  "phone": "+************"
}
```

**Response:**

```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "phone": "+************",
    "expiresIn": 600
  }
}
```

**Business Logic:**

- Validate phone number format (Indian mobile numbers)
- Generate 6-digit OTP
- Store OTP in cache/database with 10-minute expiry
- Send OTP via SMS service (Twilio/AWS SNS)
- Rate limiting: Max 3 OTP requests per phone per hour
- Return masked phone number in response

---

### **1.2 Verify OTP & Complete Authentication**

**Endpoint:** `POST /api/auth/verify-otp`

**Purpose:** Verify OTP and complete login/registration process

**Request Body:**

```json
{
  "phone": "+************",
  "otp": "123456",
  "name": "John Doe",
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "deviceType": "mobile",
    "platform": "android",
    "version": "12"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "user": {
      "id": "user_123",
      "name": "John Doe",
      "phone": "+************",
      "email": null,
      "role": "student",
      "isActive": true,
      "examPreference": [],
      "subscription": {
        "isActive": false,
        "plan": null,
        "expiresAt": null,
        "purchasedCourses": []
      },
      "preferences": {
        "language": "hindi",
        "notifications": {
          "newClass": true,
          "liveClass": true,
          "testReminder": true,
          "announcements": true
        },
        "theme": "light"
      },
      "createdAt": "2024-01-15T10:30:00Z",
      "lastLoginAt": "2024-01-15T10:30:00Z"
    },
    "token": "jwt_access_token_here",
    "refreshToken": "jwt_refresh_token_here",
    "isNewUser": true
  }
}
```

**Business Logic:**

- Verify OTP against stored value
- Check if user exists by phone number
- If new user: Create user record with basic info
- If existing user: Update last login timestamp
- Generate JWT access token (24h expiry) and refresh token (7d expiry)
- Store device info for security tracking
- Return complete user profile with tokens

---

### **1.3 Exam Preference Selection (New User Flow)**

**Endpoint:** `PUT /api/user/exam-preferences`

**Purpose:** Allow new users to select their exam preferences during onboarding

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "examPreferences": ["bpsc", "ssc", "railway"]
}
```

**Response:**

```json
{
  "success": true,
  "message": "Exam preferences updated successfully",
  "data": {
    "user": {
      "id": "user_123",
      "examPreference": ["bpsc", "ssc", "railway"],
      "updatedAt": "2024-01-15T10:35:00Z"
    }
  }
}
```

**Business Logic:**

- Validate exam IDs against available exams
- Update user's exam preferences
- Trigger recommendation engine to prepare personalized content
- Return updated user object

---

### **1.4 Get Available Exams for Selection**

**Endpoint:** `GET /api/exams/categories`

**Purpose:** Get list of available exam categories for user selection

**Response:**

```json
{
  "success": true,
  "message": "Exam categories retrieved successfully",
  "data": {
    "categories": [
      {
        "id": "bpsc",
        "name": "BPSC",
        "fullName": "Bihar Public Service Commission",
        "description": "State civil services examination for Bihar",
        "logoUrl": "https://cdn.example.com/bpsc-logo.png",
        "examCount": 15,
        "order": 1,
        "isPopular": true
      },
      {
        "id": "ssc",
        "name": "SSC",
        "fullName": "Staff Selection Commission",
        "description": "Central government jobs examination",
        "logoUrl": "https://cdn.example.com/ssc-logo.png",
        "examCount": 25,
        "order": 2,
        "isPopular": true
      }
    ]
  }
}
```

**Business Logic:**

- Return all active exam categories
- Include popularity metrics
- Sort by order field
- Include course count for each exam

---

### **1.5 User Profile Management**

**Endpoint:** `GET /api/user/profile`

**Purpose:** Get current user's complete profile

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "User profile retrieved successfully",
  "data": {
    "user": {
      "id": "user_123",
      "name": "John Doe",
      "phone": "+************",
      "email": "<EMAIL>",
      "role": "student",
      "isActive": true,
      "examPreference": ["bpsc", "ssc"],
      "subscription": {
        "isActive": true,
        "plan": "premium",
        "expiresAt": "2024-12-31T23:59:59Z",
        "purchasedCourses": ["course_1", "course_2"]
      },
      "preferences": {
        "language": "hindi",
        "notifications": {
          "newClass": true,
          "liveClass": true,
          "testReminder": true,
          "announcements": true
        },
        "theme": "light"
      },
      "stats": {
        "totalCoursesEnrolled": 5,
        "totalCoursesCompleted": 2,
        "totalVideosWatched": 45,
        "totalTestsAttempted": 12,
        "totalTestsCompleted": 8,
        "averageTestScore": 78.5,
        "totalStudyHours": 120,
        "currentStreak": 7,
        "longestStreak": 15,
        "overallProgress": 65.5
      },
      "createdAt": "2024-01-15T10:30:00Z",
      "lastLoginAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

---

### **1.6 Update User Profile**

**Endpoint:** `PUT /api/user/profile`

**Purpose:** Update user profile information

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "name": "John Doe Updated",
  "email": "<EMAIL>",
  "examPreference": ["bpsc", "ssc", "railway"],
  "preferences": {
    "language": "english",
    "notifications": {
      "newClass": true,
      "liveClass": false,
      "testReminder": true,
      "announcements": true
    },
    "theme": "dark"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "user_123",
      "name": "John Doe Updated",
      "email": "<EMAIL>",
      "examPreference": ["bpsc", "ssc", "railway"],
      "preferences": {
        "language": "english",
        "notifications": {
          "newClass": true,
          "liveClass": false,
          "testReminder": true,
          "announcements": true
        },
        "theme": "dark"
      },
      "updatedAt": "2024-01-15T11:00:00Z"
    }
  }
}
```

---

### **1.7 Refresh Token**

**Endpoint:** `POST /api/auth/refresh-token`

**Purpose:** Refresh expired access token using refresh token

**Request Body:**

```json
{
  "refreshToken": "jwt_refresh_token_here"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "token": "new_jwt_access_token",
    "refreshToken": "new_jwt_refresh_token",
    "expiresIn": 86400
  }
}
```

---

### **1.8 Logout**

**Endpoint:** `POST /api/auth/logout`

**Purpose:** Logout user and invalidate tokens

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "deviceId": "device_unique_id"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

**Business Logic:**

- Invalidate access and refresh tokens
- Remove device from user's active devices
- Clear any cached user data
- Log logout activity

---

## 🔧 **TECHNICAL REQUIREMENTS**

### **Database Schema Requirements:**

#### **Users Table:**

```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE,
  role ENUM('student', 'admin') DEFAULT 'student',
  is_active BOOLEAN DEFAULT true,
  exam_preferences JSON,
  subscription JSON,
  preferences JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP
);
```

#### **OTP Storage Table:**

```sql
CREATE TABLE otp_verifications (
  id VARCHAR(36) PRIMARY KEY,
  phone VARCHAR(20) NOT NULL,
  otp VARCHAR(6) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  attempts INT DEFAULT 0,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **User Devices Table:**

```sql
CREATE TABLE user_devices (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  device_id VARCHAR(255) NOT NULL,
  device_type VARCHAR(50),
  platform VARCHAR(50),
  version VARCHAR(50),
  is_active BOOLEAN DEFAULT true,
  last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### **Security Requirements:**

- JWT tokens with proper expiry
- Rate limiting on OTP endpoints
- Device tracking and management
- Input validation and sanitization
- SQL injection prevention
- CORS configuration
- HTTPS enforcement

### **Integration Requirements:**

- SMS service for OTP (Twilio/AWS SNS)
- Redis for OTP caching
- JWT library for token management
- Bcrypt for password hashing (if needed)
- Input validation middleware

---

## 📱 **FRONTEND INTEGRATION POINTS**

The Flutter app expects these exact response formats and will handle:

- Automatic token refresh
- Persistent login state
- Exam preference selection flow
- Profile management
- Device registration
- Error handling with user-friendly messages

---

## 🚀 **NEXT PHASES**

After Phase 1 completion, we'll proceed with:

- **Phase 2**: Dashboard & Course APIs
- **Phase 3**: Content Management (Videos, PDFs)
- **Phase 4**: Test System APIs
- **Phase 5**: Live Classes & Notifications
- **Phase 6**: Payment & Subscription APIs

---

---

## 📊 **IMPLEMENTATION SUMMARY**

### **What I've Built in Flutter:**

#### **✅ Complete Authentication System:**

- **Login Screen**: Phone number input with OTP sending
- **OTP Verification Screen**: 6-digit OTP input with auto-verification
- **Registration Flow**: Name collection during OTP verification
- **Exam Preference Screen**: Multi-select exam categories for new users
- **Profile Management**: Update user info, preferences, exam selections

#### **✅ State Management:**

- **AuthProvider**: Complete authentication state management
- **DashboardProvider**: Dashboard data and course management
- **Real-time Updates**: Reactive UI with Provider pattern

#### **✅ API Integration Ready:**

- **HTTP Client**: Complete API service with error handling
- **Token Management**: JWT token storage and refresh
- **Request/Response Models**: All data models implemented
- **Error Handling**: User-friendly error messages

#### **✅ UI/UX Features:**

- **Responsive Design**: Works on all screen sizes
- **Animations**: Smooth transitions and loading states
- **Form Validation**: Input validation with error messages
- **Loading States**: Skeleton screens and progress indicators

### **Authentication Flow Implemented:**

```
1. User enters phone number → Send OTP API call
2. User enters OTP → Verify OTP API call
3. If new user → Navigate to Exam Preference Selection
4. User selects exams → Update Exam Preferences API call
5. Navigate to Dashboard → Load user data and courses
```

### **API Endpoints the App is Ready to Consume:**

1. `POST /api/auth/send-otp` - Send OTP to phone
2. `POST /api/auth/verify-otp` - Verify OTP and authenticate
3. `GET /api/exams/categories` - Get exam categories for selection
4. `PUT /api/user/exam-preferences` - Update user's exam preferences
5. `GET /api/user/profile` - Get user profile data
6. `PUT /api/user/profile` - Update user profile
7. `POST /api/auth/refresh-token` - Refresh expired tokens
8. `POST /api/auth/logout` - Logout and invalidate tokens

### **Ready for Testing:**

Once you implement these 8 APIs, the complete authentication flow will work end-to-end with:

- Phone OTP login/registration
- New user onboarding with exam selection
- Profile management
- Persistent login state
- Token refresh mechanism
- Secure logout

---

## 🏠 **PHASE 2: HOME SCREEN APIs (Step 1 - Requirement.md)**

> **Implementation Priority:** HIGH - Core home screen functionality
> **Dependencies:** Phase 1 (Authentication) must be completed first
> **Frontend Status:** ✅ READY - UI implemented and waiting for API integration

### **Overview:**

The Home Screen is the main dashboard where users interact with exam categories, view promotional banners, and access quick navigation to key features. This implements the exact requirements from Step 1 in Requirement.md.

---

### **2.1 Get Complete Home Screen Data**

**Endpoint:** `GET /api/dashboard/home`

**Purpose:** Get all home screen data in a single API call for optimal performance

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
examId: string (optional) - Filter content by specific exam category
refreshCache: boolean (default: false) - Force refresh cached data
```

**Response:**

```json
{
  "success": true,
  "message": "Home screen data retrieved successfully",
  "data": {
    "selectedExam": {
      "id": "bpsc",
      "name": "BPSC",
      "fullName": "Bihar Public Service Commission",
      "isUserPreferred": true
    },
    "banners": [
      {
        "id": "banner_1",
        "title": "New BPSC Course Available!",
        "subtitle": "Complete preparation for 70th BPSC examination",
        "imageUrl": "https://cdn.example.com/banners/bpsc-new-course.jpg",
        "type": "new_course",
        "priority": 1,
        "isActive": true,
        "examId": "bpsc",
        "examName": "BPSC",
        "actionType": "navigate_course",
        "actionUrl": "/courses/bpsc-70th-prelims",
        "actionData": {
          "courseId": "course_bpsc_70th",
          "examId": "bpsc"
        },
        "validFrom": "2024-01-01T00:00:00Z",
        "validUntil": "2024-12-31T23:59:59Z",
        "createdAt": "2024-01-15T10:00:00Z"
      },
      {
        "id": "banner_2",
        "title": "Important Announcement",
        "subtitle": "BPSC exam dates announced - Check details",
        "imageUrl": "https://cdn.example.com/banners/bpsc-announcement.jpg",
        "type": "announcement",
        "priority": 2,
        "isActive": true,
        "examId": "bpsc",
        "examName": "BPSC",
        "actionType": "show_details",
        "actionUrl": "/announcements/bpsc-exam-dates",
        "actionData": {
          "announcementId": "ann_bpsc_dates_2024",
          "examId": "bpsc"
        },
        "validFrom": "2024-01-10T00:00:00Z",
        "validUntil": "2024-03-31T23:59:59Z",
        "createdAt": "2024-01-10T09:00:00Z"
      },
      {
        "id": "banner_3",
        "title": "Special Offer - 50% Off",
        "subtitle": "Limited time offer on all BPSC courses",
        "imageUrl": "https://cdn.example.com/banners/special-offer.jpg",
        "type": "special_offer",
        "priority": 3,
        "isActive": true,
        "examId": "bpsc",
        "examName": "BPSC",
        "actionType": "show_courses",
        "actionUrl": "/courses?exam=bpsc&offer=special50",
        "actionData": {
          "examId": "bpsc",
          "offerCode": "SPECIAL50",
          "discountPercent": 50
        },
        "validFrom": "2024-01-15T00:00:00Z",
        "validUntil": "2024-01-31T23:59:59Z",
        "createdAt": "2024-01-15T08:00:00Z"
      }
    ],
    "quickAccessStats": {
      "myCourses": {
        "enrolledCount": 5,
        "inProgressCount": 3,
        "completedCount": 2,
        "newContentCount": 8,
        "lastAccessedCourse": {
          "id": "course_bpsc_70th",
          "title": "BPSC 70th Complete Course",
          "progress": 65.5,
          "lastWatchedAt": "2024-01-15T14:30:00Z"
        }
      },
      "liveClasses": {
        "upcomingCount": 2,
        "todayCount": 1,
        "isLiveNow": false,
        "nextClass": {
          "id": "live_class_123",
          "title": "BPSC Polity - Fundamental Rights",
          "scheduledAt": "2024-01-15T16:00:00Z",
          "duration": 90,
          "instructorName": "Dr. Rajesh Kumar"
        }
      },
      "dailyQuiz": {
        "isAvailable": true,
        "isCompleted": false,
        "streakCount": 7,
        "maxStreak": 15,
        "todaysTopic": "Indian History - Mughal Empire",
        "examId": "bpsc",
        "questionsCount": 10,
        "timeLimit": 600,
        "expiresAt": "2024-01-15T23:59:59Z"
      },
      "testSeries": {
        "availableCount": 12,
        "attemptedCount": 8,
        "completedCount": 6,
        "pendingCount": 4,
        "averageScore": 78.5,
        "lastAttempted": {
          "id": "test_bpsc_prelims_1",
          "title": "BPSC Prelims Mock Test 1",
          "score": 82,
          "attemptedAt": "2024-01-14T18:30:00Z"
        }
      }
    },
    "examCategories": [
      {
        "id": "bpsc",
        "name": "BPSC",
        "fullName": "Bihar Public Service Commission",
        "description": "State civil services examination for Bihar",
        "iconUrl": "https://cdn.example.com/icons/bpsc-icon.png",
        "logoUrl": "https://cdn.example.com/logos/bpsc-logo.png",
        "isPopular": true,
        "isUserPreferred": true,
        "courseCount": 15,
        "activeStudents": 12500,
        "order": 1,
        "category": "state_services",
        "difficulty": "high",
        "examPattern": {
          "hasPrelimsMainsInterview": true,
          "prelimsPattern": "objective",
          "mainsPattern": "descriptive"
        }
      },
      {
        "id": "ssc",
        "name": "SSC",
        "fullName": "Staff Selection Commission",
        "description": "Central government jobs examination",
        "iconUrl": "https://cdn.example.com/icons/ssc-icon.png",
        "logoUrl": "https://cdn.example.com/logos/ssc-logo.png",
        "isPopular": true,
        "isUserPreferred": false,
        "courseCount": 25,
        "activeStudents": 45000,
        "order": 2,
        "category": "central_services",
        "difficulty": "medium",
        "examPattern": {
          "hasPrelimsMainsInterview": false,
          "prelimsPattern": "objective",
          "mainsPattern": null
        }
      },
      {
        "id": "ctet",
        "name": "CTET",
        "fullName": "Central Teacher Eligibility Test",
        "description": "Teaching eligibility examination",
        "iconUrl": "https://cdn.example.com/icons/ctet-icon.png",
        "logoUrl": "https://cdn.example.com/logos/ctet-logo.png",
        "isPopular": true,
        "isUserPreferred": false,
        "courseCount": 8,
        "activeStudents": 18000,
        "order": 3,
        "category": "teaching",
        "difficulty": "medium",
        "examPattern": {
          "hasPrelimsMainsInterview": false,
          "prelimsPattern": "objective",
          "mainsPattern": null
        }
      },
      {
        "id": "railway",
        "name": "Railway",
        "fullName": "Railway Recruitment Board",
        "description": "Railway jobs examination",
        "iconUrl": "https://cdn.example.com/icons/railway-icon.png",
        "logoUrl": "https://cdn.example.com/logos/railway-logo.png",
        "isPopular": true,
        "isUserPreferred": false,
        "courseCount": 18,
        "activeStudents": 35000,
        "order": 4,
        "category": "railway",
        "difficulty": "medium",
        "examPattern": {
          "hasPrelimsMainsInterview": false,
          "prelimsPattern": "objective",
          "mainsPattern": null
        }
      },
      {
        "id": "banking",
        "name": "Banking",
        "fullName": "Banking Sector Examinations",
        "description": "Bank jobs examination (IBPS, SBI, etc.)",
        "iconUrl": "https://cdn.example.com/icons/banking-icon.png",
        "logoUrl": "https://cdn.example.com/logos/banking-logo.png",
        "isPopular": true,
        "isUserPreferred": false,
        "courseCount": 22,
        "activeStudents": 28000,
        "order": 5,
        "category": "banking",
        "difficulty": "high",
        "examPattern": {
          "hasPrelimsMainsInterview": true,
          "prelimsPattern": "objective",
          "mainsPattern": "objective_descriptive"
        }
      },
      {
        "id": "bihar_board_10th",
        "name": "Bihar Board 10th",
        "fullName": "Bihar School Examination Board - Class 10",
        "description": "Bihar state board 10th class examination",
        "iconUrl": "https://cdn.example.com/icons/bihar-board-icon.png",
        "logoUrl": "https://cdn.example.com/logos/bihar-board-logo.png",
        "isPopular": false,
        "isUserPreferred": false,
        "courseCount": 12,
        "activeStudents": 8500,
        "order": 6,
        "category": "school_board",
        "difficulty": "low",
        "examPattern": {
          "hasPrelimsMainsInterview": false,
          "prelimsPattern": "descriptive",
          "mainsPattern": null
        }
      },
      {
        "id": "bihar_board_12th",
        "name": "Bihar Board 12th",
        "fullName": "Bihar School Examination Board - Class 12",
        "description": "Bihar state board 12th class examination",
        "iconUrl": "https://cdn.example.com/icons/bihar-board-icon.png",
        "logoUrl": "https://cdn.example.com/logos/bihar-board-logo.png",
        "isPopular": false,
        "isUserPreferred": false,
        "courseCount": 15,
        "activeStudents": 9200,
        "order": 7,
        "category": "school_board",
        "difficulty": "medium",
        "examPattern": {
          "hasPrelimsMainsInterview": false,
          "prelimsPattern": "descriptive",
          "mainsPattern": null
        }
      }
    ],
    "featuredCourses": [
      {
        "id": "course_bpsc_70th_complete",
        "title": "BPSC 70th Complete Course - Prelims + Mains",
        "description": "Comprehensive course covering all subjects for BPSC 70th examination",
        "examId": "bpsc",
        "examName": "BPSC",
        "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-complete.jpg",
        "instructorId": "instructor_rajesh_kumar",
        "instructorName": "Dr. Rajesh Kumar",
        "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
        "type": "recorded",
        "pricing": {
          "originalPrice": 2999,
          "discountedPrice": 1499,
          "isFree": false,
          "currency": "INR",
          "discountPercent": 50,
          "discountValidUntil": "2024-01-31T23:59:59Z"
        },
        "content": {
          "totalVideos": 120,
          "totalDuration": 7200,
          "totalPDFs": 45,
          "totalTests": 25,
          "subjects": [
            "History",
            "Geography",
            "Polity",
            "Economics",
            "Current Affairs"
          ]
        },
        "stats": {
          "enrolledStudents": 1250,
          "averageRating": 4.5,
          "totalRatings": 320,
          "completionRate": 78
        },
        "isEnrolled": false,
        "isFeatured": true,
        "isPopular": true,
        "tags": ["complete", "prelims", "mains", "hindi"],
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      },
      {
        "id": "course_bpsc_current_affairs",
        "title": "BPSC Current Affairs 2024",
        "description": "Monthly updated current affairs for BPSC examination",
        "examId": "bpsc",
        "examName": "BPSC",
        "thumbnailUrl": "https://cdn.example.com/courses/bpsc-current-affairs.jpg",
        "instructorId": "instructor_priya_sharma",
        "instructorName": "Dr. Priya Sharma",
        "instructorImage": "https://cdn.example.com/instructors/priya-sharma.jpg",
        "type": "live",
        "pricing": {
          "originalPrice": 999,
          "discountedPrice": null,
          "isFree": false,
          "currency": "INR",
          "discountPercent": 0,
          "discountValidUntil": null
        },
        "content": {
          "totalVideos": 24,
          "totalDuration": 1440,
          "totalPDFs": 12,
          "totalTests": 6,
          "subjects": ["Current Affairs", "Monthly Updates"]
        },
        "stats": {
          "enrolledStudents": 850,
          "averageRating": 4.7,
          "totalRatings": 180,
          "completionRate": 85
        },
        "isEnrolled": true,
        "isFeatured": true,
        "isPopular": false,
        "tags": ["current-affairs", "monthly", "live", "hindi"],
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      }
    ],
    "recentActivities": [
      {
        "id": "activity_video_123",
        "type": "video_watched",
        "title": "Completed: Indian History - Mughal Empire",
        "description": "Watched 45 minutes of content in Chapter 3",
        "courseId": "course_bpsc_70th_complete",
        "courseName": "BPSC 70th Complete Course",
        "examId": "bpsc",
        "timestamp": "2024-01-15T14:30:00Z",
        "metadata": {
          "videoId": "video_history_mughal_3",
          "watchDuration": 2700,
          "totalDuration": 2700,
          "completionPercent": 100
        }
      },
      {
        "id": "activity_test_456",
        "type": "test_completed",
        "title": "Mock Test: BPSC Prelims Practice 1",
        "description": "Scored 82/100 in General Studies",
        "courseId": "course_bpsc_70th_complete",
        "courseName": "BPSC 70th Complete Course",
        "examId": "bpsc",
        "timestamp": "2024-01-14T18:45:00Z",
        "metadata": {
          "testId": "test_bpsc_prelims_1",
          "score": 82,
          "maxScore": 100,
          "timeTaken": 7200,
          "totalQuestions": 100,
          "correctAnswers": 82
        }
      }
    ],
    "userPreferences": {
      "selectedExamId": "bpsc",
      "language": "hindi",
      "lastActiveAt": "2024-01-15T14:30:00Z",
      "homeScreenLayout": "default"
    },
    "metadata": {
      "cacheExpiry": "2024-01-15T15:30:00Z",
      "lastUpdated": "2024-01-15T14:30:00Z",
      "apiVersion": "2.1"
    }
  }
}
```

**Business Logic Requirements:**

1. **Exam-based Content Filtering:**

   - If `examId` is provided, filter all content (banners, courses, stats) for that exam
   - If no `examId`, use user's preferred exam from profile
   - Fall back to most popular exam if no preference set

2. **Banner Priority & Validity:**

   - Sort banners by priority (1 = highest)
   - Only show active banners within valid date range
   - Limit to maximum 5 banners per response

3. **Quick Access Stats Calculation:**

   - Calculate real-time stats from user's actual data
   - Cache stats for 5 minutes to improve performance
   - Include next upcoming events (live classes, quiz expiry)

4. **Featured Courses Selection:**

   - Show max 3 featured courses per exam
   - Prioritize: enrolled courses → popular courses → new courses
   - Include user's enrollment status and progress

5. **Recent Activities:**
   - Show last 5 activities across all courses
   - Include detailed metadata for progress tracking
   - Sort by timestamp (newest first)

---

### **2.2 Update User's Selected Exam**

**Endpoint:** `PUT /api/user/selected-exam`

**Purpose:** Update user's currently selected exam for personalized content

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "examId": "ssc",
  "updatePreferences": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Selected exam updated successfully",
  "data": {
    "selectedExam": {
      "id": "ssc",
      "name": "SSC",
      "fullName": "Staff Selection Commission"
    },
    "contentUpdated": true,
    "updatedAt": "2024-01-15T15:00:00Z"
  }
}
```

**Business Logic:**

- Update user's current exam selection
- If `updatePreferences` is true, also update user's exam preferences
- Trigger content refresh for new exam
- Log exam change activity

---

### **2.3 Get Banners by Exam**

**Endpoint:** `GET /api/banners`

**Purpose:** Get promotional banners filtered by exam category (for banner slider)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
examId: string (optional) - Filter banners by specific exam
type: string (optional) - Filter by banner type (announcement, new_course, special_offer, free_test)
limit: number (default: 5) - Number of banners to return
activeOnly: boolean (default: true) - Only return active banners
```

**Response:**

```json
{
  "success": true,
  "message": "Banners retrieved successfully",
  "data": {
    "banners": [
      {
        "id": "banner_1",
        "title": "New BPSC Course Available!",
        "subtitle": "Complete preparation for 70th BPSC",
        "imageUrl": "https://cdn.example.com/banner1.jpg",
        "type": "new_course",
        "priority": 1,
        "isActive": true,
        "examId": "bpsc",
        "actionUrl": "/courses/bpsc-70th-prelims",
        "validUntil": "2024-12-31T23:59:59Z",
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### **2.4 Get Daily Quiz Status**

**Endpoint:** `GET /api/quiz/daily`

**Purpose:** Check if daily quiz is available and get user's progress (for Daily Quiz quick access)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
examId: string (optional) - Get quiz for specific exam
date: string (optional) - Get quiz for specific date (YYYY-MM-DD)
includeHistory: boolean (default: false) - Include user's quiz history
```

**Response:**

```json
{
  "success": true,
  "message": "Daily quiz status retrieved successfully",
  "data": {
    "isAvailable": true,
    "quizId": "daily_quiz_20240115",
    "examId": "bpsc",
    "examName": "BPSC",
    "date": "2024-01-15",
    "totalQuestions": 10,
    "timeLimit": 600,
    "isCompleted": false,
    "userScore": null,
    "maxScore": 100,
    "topic": "Indian History - Mughal Empire",
    "difficulty": "medium",
    "expiresAt": "2024-01-15T23:59:59Z",
    "streakInfo": {
      "currentStreak": 7,
      "maxStreak": 15,
      "streakBrokenDate": null
    },
    "history": {
      "totalAttempted": 45,
      "averageScore": 78.5,
      "bestScore": 95,
      "lastAttempted": "2024-01-14T10:30:00Z"
    }
  }
}
```

**Business Logic:**

- Generate daily quiz based on user's selected exam
- Questions should be from different topics to ensure variety
- Track user's streak and performance history
- Quiz expires at midnight (user's timezone)
- Only one attempt per day allowed

---

### **2.5 Get Quick Access Stats**

**Endpoint:** `GET /api/dashboard/quick-stats`

**Purpose:** Get real-time stats for quick access cards (My Courses, Live Class, Daily Quiz, Test Series)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
examId: string (optional) - Get stats for specific exam
refreshCache: boolean (default: false) - Force refresh cached stats
```

**Response:**

```json
{
  "success": true,
  "message": "Quick access stats retrieved successfully",
  "data": {
    "myCourses": {
      "enrolledCount": 5,
      "inProgressCount": 3,
      "completedCount": 2,
      "newContentCount": 8
    },
    "liveClasses": {
      "upcomingCount": 2,
      "todayCount": 1,
      "nextClassAt": "2024-01-15T16:00:00Z",
      "nextClassTitle": "BPSC Polity - Fundamental Rights"
    },
    "dailyQuiz": {
      "isAvailable": true,
      "isCompleted": false,
      "streakCount": 7,
      "todaysTopic": "Indian History"
    },
    "testSeries": {
      "availableCount": 12,
      "attemptedCount": 8,
      "pendingCount": 4,
      "averageScore": 78.5
    }
  }
}
```

---

## 📋 **HOME SCREEN API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 1 (Home Screen):**

1. **`GET /api/dashboard/home`** - Complete home screen data (PRIMARY)
2. **`PUT /api/user/selected-exam`** - Update user's selected exam
3. **`GET /api/banners`** - Get promotional banners
4. **`GET /api/quiz/daily`** - Daily quiz status and availability
5. **`GET /api/dashboard/quick-stats`** - Real-time quick access statistics

### **🏗️ Database Schema Requirements:**

#### **Banners Table:**

```sql
CREATE TABLE banners (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  subtitle TEXT,
  image_url VARCHAR(500),
  type ENUM('announcement', 'new_course', 'special_offer', 'free_test') NOT NULL,
  priority INT DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  exam_id VARCHAR(36),
  action_type VARCHAR(50),
  action_url VARCHAR(500),
  action_data JSON,
  valid_from TIMESTAMP,
  valid_until TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(id)
);
```

#### **Daily Quiz Table:**

```sql
CREATE TABLE daily_quizzes (
  id VARCHAR(36) PRIMARY KEY,
  exam_id VARCHAR(36) NOT NULL,
  quiz_date DATE NOT NULL,
  topic VARCHAR(255) NOT NULL,
  difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
  total_questions INT DEFAULT 10,
  time_limit INT DEFAULT 600,
  max_score INT DEFAULT 100,
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(id),
  UNIQUE KEY unique_exam_date (exam_id, quiz_date)
);
```

#### **User Quiz Attempts Table:**

```sql
CREATE TABLE user_quiz_attempts (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  quiz_id VARCHAR(36) NOT NULL,
  score INT,
  max_score INT,
  time_taken INT,
  total_questions INT,
  correct_answers INT,
  is_completed BOOLEAN DEFAULT false,
  attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (quiz_id) REFERENCES daily_quizzes(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Performance Optimization:**

- Cache home screen data for 5 minutes
- Use Redis for quick access stats caching
- Implement banner CDN for image delivery
- Database indexing on exam_id, user_id, and date fields

#### **Security Considerations:**

- Validate exam_id against user's preferences
- Rate limiting on quiz attempts (1 per day)
- Sanitize banner action URLs
- Implement proper CORS for banner images

#### **Business Logic Implementation:**

- Banner priority sorting and validity checks
- Daily quiz generation with topic rotation
- Real-time stats calculation with caching
- Exam-based content filtering
- User activity tracking and streak calculation

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Exam Dropdown**: Calls `PUT /api/user/selected-exam` on selection change
- **Banner Slider**: Displays banners from `GET /api/dashboard/home`
- **Quick Access Cards**: Shows stats from quick-stats API
- **Daily Quiz**: Checks availability and shows streak info
- **Dynamic Content**: Updates based on selected exam

### **🚀 Implementation Priority:**

1. **HIGH**: `GET /api/dashboard/home` - Core home screen functionality
2. **HIGH**: `PUT /api/user/selected-exam` - Exam switching
3. **MEDIUM**: `GET /api/quiz/daily` - Daily quiz feature
4. **MEDIUM**: `GET /api/banners` - Promotional content
5. **LOW**: `GET /api/dashboard/quick-stats` - Enhanced statistics

---

## 🧭 **PHASE 3: EXAM & COURSE HIERARCHY APIs (Step 2 - Requirement.md)**

> **Implementation Priority:** HIGH - Core navigation and content structure
> **Dependencies:** Phase 1 (Authentication) must be completed first
> **Frontend Status:** ✅ READY - Models and services implemented, waiting for API integration

### **Overview:**

The Exam & Course Hierarchy implements the 4-step navigation structure: Exam Selection → Course List → Subject List → Class Content. This creates the core content discovery and navigation experience for students.

**Hierarchy Structure:**

```
Exam (BPSC, SSC, etc.)
  └── Courses (BPSC 70th Prelims, SSC CGL Tier 1, etc.)
      └── Subjects (Polity, History, Economy, etc.)
          └── Content (Videos, PDFs, Tests)
```

---

### **3.1 Get Exam Categories with Hierarchy**

**Endpoint:** `GET /api/exams/hierarchy`

**Purpose:** Get complete exam hierarchy with courses and subjects for navigation

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeInactive: boolean (default: false) - Include inactive exams
includeCourseCount: boolean (default: true) - Include course counts
includeSubjects: boolean (default: false) - Include subject details
userEnrollmentStatus: boolean (default: true) - Include user's enrollment status
```

**Response:**

```json
{
  "success": true,
  "message": "Exam hierarchy retrieved successfully",
  "data": {
    "examCategories": [
      {
        "id": "bpsc",
        "name": "BPSC",
        "fullName": "Bihar Public Service Commission",
        "description": "State civil services examination for Bihar",
        "iconUrl": "https://cdn.example.com/icons/bpsc-icon.png",
        "logoUrl": "https://cdn.example.com/logos/bpsc-logo.png",
        "category": "state_services",
        "type": "competitive",
        "level": "advanced",
        "difficulty": "high",
        "isPopular": true,
        "isActive": true,
        "order": 1,
        "stats": {
          "totalCourses": 15,
          "totalStudents": 12500,
          "averageRating": 4.5
        },
        "courses": [
          {
            "id": "course_bpsc_70th_prelims",
            "title": "BPSC 70th Prelims Complete Course",
            "description": "Comprehensive preparation for BPSC 70th Preliminary examination",
            "type": "recorded",
            "level": "intermediate",
            "duration": 180,
            "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
            "instructorId": "instructor_rajesh_kumar",
            "instructorName": "Dr. Rajesh Kumar",
            "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
            "pricing": {
              "originalPrice": 2999,
              "discountedPrice": 1499,
              "isFree": false,
              "currency": "INR",
              "discountPercent": 50,
              "discountValidUntil": "2024-01-31T23:59:59Z"
            },
            "content": {
              "totalVideos": 120,
              "totalDuration": 7200,
              "totalPDFs": 45,
              "totalTests": 25,
              "subjectCount": 6
            },
            "stats": {
              "enrolledStudents": 1250,
              "averageRating": 4.5,
              "totalRatings": 320,
              "completionRate": 78
            },
            "isEnrolled": false,
            "userProgress": null,
            "tags": ["complete", "prelims", "hindi", "2024"],
            "status": "active",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-15T10:00:00Z"
          },
          {
            "id": "course_bpsc_70th_mains",
            "title": "BPSC 70th Mains Complete Course",
            "description": "Comprehensive preparation for BPSC 70th Mains examination",
            "type": "hybrid",
            "level": "advanced",
            "duration": 240,
            "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-mains.jpg",
            "instructorId": "instructor_priya_sharma",
            "instructorName": "Dr. Priya Sharma",
            "instructorImage": "https://cdn.example.com/instructors/priya-sharma.jpg",
            "pricing": {
              "originalPrice": 4999,
              "discountedPrice": 2999,
              "isFree": false,
              "currency": "INR",
              "discountPercent": 40,
              "discountValidUntil": "2024-02-15T23:59:59Z"
            },
            "content": {
              "totalVideos": 180,
              "totalDuration": 10800,
              "totalPDFs": 60,
              "totalTests": 35,
              "subjectCount": 8
            },
            "stats": {
              "enrolledStudents": 850,
              "averageRating": 4.7,
              "totalRatings": 180,
              "completionRate": 65
            },
            "isEnrolled": true,
            "userProgress": {
              "completionPercentage": 35.5,
              "videosWatched": 25,
              "testsAttempted": 8,
              "testsCompleted": 6,
              "lastAccessedAt": "2024-01-15T14:30:00Z",
              "totalTimeSpent": 1800
            },
            "tags": ["complete", "mains", "hindi", "2024"],
            "status": "active",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-15T10:00:00Z"
          }
        ]
      },
      {
        "id": "ssc",
        "name": "SSC",
        "fullName": "Staff Selection Commission",
        "description": "Central government jobs examination",
        "iconUrl": "https://cdn.example.com/icons/ssc-icon.png",
        "logoUrl": "https://cdn.example.com/logos/ssc-logo.png",
        "category": "central_services",
        "type": "competitive",
        "level": "intermediate",
        "difficulty": "medium",
        "isPopular": true,
        "isActive": true,
        "order": 2,
        "stats": {
          "totalCourses": 25,
          "totalStudents": 45000,
          "averageRating": 4.3
        },
        "courses": [
          {
            "id": "course_ssc_cgl_tier1",
            "title": "SSC CGL Tier 1 Complete Course",
            "description": "Complete preparation for SSC CGL Tier 1 examination",
            "type": "recorded",
            "level": "intermediate",
            "duration": 150,
            "thumbnailUrl": "https://cdn.example.com/courses/ssc-cgl-tier1.jpg",
            "instructorId": "instructor_amit_singh",
            "instructorName": "Amit Singh",
            "instructorImage": "https://cdn.example.com/instructors/amit-singh.jpg",
            "pricing": {
              "originalPrice": 1999,
              "discountedPrice": 999,
              "isFree": false,
              "currency": "INR",
              "discountPercent": 50,
              "discountValidUntil": "2024-01-31T23:59:59Z"
            },
            "content": {
              "totalVideos": 100,
              "totalDuration": 6000,
              "totalPDFs": 30,
              "totalTests": 20,
              "subjectCount": 4
            },
            "stats": {
              "enrolledStudents": 2500,
              "averageRating": 4.4,
              "totalRatings": 580,
              "completionRate": 82
            },
            "isEnrolled": false,
            "userProgress": null,
            "tags": ["complete", "tier1", "english", "hindi"],
            "status": "active",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-15T10:00:00Z"
          }
        ]
      }
    ],
    "metadata": {
      "totalExams": 7,
      "totalCourses": 125,
      "userEnrolledCourses": 5,
      "lastUpdated": "2024-01-15T15:00:00Z"
    }
  }
}
```

**Business Logic:**

- Sort exams by popularity and user preferences
- Include only active exams unless specified
- Show user's enrollment status for each course
- Calculate real-time statistics
- Filter based on user's exam preferences if available

### **3.2 Get Courses by Exam**

**Endpoint:** `GET /api/exams/{examId}/courses`

**Purpose:** Get all courses under a specific exam (Step 2 in hierarchy)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
page: number (default: 1) - Page number for pagination
limit: number (default: 10) - Number of courses per page
sortBy: string (default: "popularity") - Sort by: popularity, rating, price, newest
sortOrder: string (default: "desc") - Sort order: asc, desc
type: string (optional) - Filter by course type: recorded, live, hybrid
level: string (optional) - Filter by level: beginner, intermediate, advanced
isFree: boolean (optional) - Filter free courses
search: string (optional) - Search in course titles and descriptions
includeInactive: boolean (default: false) - Include inactive courses
```

**Response:**

```json
{
  "success": true,
  "message": "Courses retrieved successfully",
  "data": {
    "exam": {
      "id": "bpsc",
      "name": "BPSC",
      "fullName": "Bihar Public Service Commission"
    },
    "courses": [
      {
        "id": "course_bpsc_70th_prelims",
        "title": "BPSC 70th Prelims Complete Course",
        "description": "Comprehensive preparation for BPSC 70th Preliminary examination with detailed coverage of all subjects",
        "shortDescription": "Complete BPSC 70th Prelims preparation",
        "examId": "bpsc",
        "examName": "BPSC",
        "type": "recorded",
        "level": "intermediate",
        "duration": 180,
        "language": "hindi",
        "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
        "previewVideoUrl": "https://cdn.example.com/previews/bpsc-70th-prelims-preview.mp4",
        "instructorId": "instructor_rajesh_kumar",
        "instructorName": "Dr. Rajesh Kumar",
        "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
        "instructorBio": "15+ years experience in BPSC coaching",
        "pricing": {
          "originalPrice": 2999,
          "discountedPrice": 1499,
          "isFree": false,
          "currency": "INR",
          "discountPercent": 50,
          "discountValidUntil": "2024-01-31T23:59:59Z",
          "emiAvailable": true,
          "emiStartsFrom": 500
        },
        "content": {
          "totalVideos": 120,
          "totalDuration": 7200,
          "totalPDFs": 45,
          "totalTests": 25,
          "totalLiveClasses": 0,
          "subjectCount": 6,
          "subjects": [
            {
              "id": "subject_history",
              "name": "History",
              "videoCount": 25,
              "pdfCount": 8,
              "testCount": 5,
              "order": 1
            },
            {
              "id": "subject_polity",
              "name": "Polity",
              "videoCount": 20,
              "pdfCount": 7,
              "testCount": 4,
              "order": 2
            },
            {
              "id": "subject_geography",
              "name": "Geography",
              "videoCount": 18,
              "pdfCount": 6,
              "testCount": 4,
              "order": 3
            },
            {
              "id": "subject_economy",
              "name": "Economy",
              "videoCount": 22,
              "pdfCount": 8,
              "testCount": 5,
              "order": 4
            },
            {
              "id": "subject_current_affairs",
              "name": "Current Affairs",
              "videoCount": 20,
              "pdfCount": 10,
              "testCount": 4,
              "order": 5
            },
            {
              "id": "subject_general_science",
              "name": "General Science",
              "videoCount": 15,
              "pdfCount": 6,
              "testCount": 3,
              "order": 6
            }
          ]
        },
        "stats": {
          "enrolledStudents": 1250,
          "averageRating": 4.5,
          "totalRatings": 320,
          "completionRate": 78,
          "totalReviews": 180
        },
        "features": [
          "Lifetime Access",
          "Mobile App Access",
          "Certificate of Completion",
          "24/7 Doubt Support",
          "Mock Tests Included"
        ],
        "requirements": [
          "Basic understanding of BPSC exam pattern",
          "12th pass or equivalent"
        ],
        "learningOutcomes": [
          "Complete BPSC Prelims syllabus coverage",
          "Exam strategy and time management",
          "Current affairs updates",
          "Mock test practice"
        ],
        "isEnrolled": false,
        "userProgress": null,
        "tags": ["complete", "prelims", "hindi", "2024", "bestseller"],
        "status": "active",
        "isFeatured": true,
        "isPopular": true,
        "isBestseller": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z",
        "publishedAt": "2024-01-05T00:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "filters": {
      "availableTypes": ["recorded", "live", "hybrid"],
      "availableLevels": ["beginner", "intermediate", "advanced"],
      "priceRange": {
        "min": 0,
        "max": 9999
      },
      "languages": ["hindi", "english"]
    }
  }
}
```

**Business Logic:**

- Sort courses by popularity, rating, or price
- Include user's enrollment status and progress
- Calculate real-time statistics and ratings
- Filter based on course type, level, and price
- Include subject breakdown for each course

---

### **3.3 Get Course Details with Subjects**

**Endpoint:** `GET /api/courses/{courseId}`

**Purpose:** Get detailed course information with complete subject hierarchy (Step 3)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeContent: boolean (default: true) - Include detailed content structure
includeReviews: boolean (default: false) - Include user reviews
includeInstructor: boolean (default: true) - Include instructor details
includeProgress: boolean (default: true) - Include user progress if enrolled
```

**Response:**

```json
{
  "success": true,
  "message": "Course details retrieved successfully",
  "data": {
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "description": "Comprehensive preparation for BPSC 70th Preliminary examination with detailed coverage of all subjects including History, Polity, Geography, Economy, Current Affairs, and General Science.",
      "examId": "bpsc",
      "examName": "BPSC",
      "type": "recorded",
      "level": "intermediate",
      "duration": 180,
      "language": "hindi",
      "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
      "previewVideoUrl": "https://cdn.example.com/previews/bpsc-70th-prelims-preview.mp4",
      "instructor": {
        "id": "instructor_rajesh_kumar",
        "name": "Dr. Rajesh Kumar",
        "image": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
        "bio": "Dr. Rajesh Kumar has 15+ years of experience in BPSC coaching with 500+ successful candidates.",
        "qualifications": ["Ph.D. in Political Science", "M.A. History"],
        "experience": "15+ years",
        "rating": 4.8,
        "totalStudents": 5000,
        "totalCourses": 12
      },
      "pricing": {
        "originalPrice": 2999,
        "discountedPrice": 1499,
        "isFree": false,
        "currency": "INR",
        "discountPercent": 50,
        "discountValidUntil": "2024-01-31T23:59:59Z",
        "emiAvailable": true,
        "emiStartsFrom": 500,
        "refundPolicy": "30-day money back guarantee"
      },
      "content": {
        "totalVideos": 120,
        "totalDuration": 7200,
        "totalPDFs": 45,
        "totalTests": 25,
        "totalLiveClasses": 0,
        "subjectCount": 6,
        "subjects": [
          {
            "id": "subject_history",
            "name": "History",
            "description": "Ancient, Medieval and Modern Indian History",
            "videoCount": 25,
            "pdfCount": 8,
            "testCount": 5,
            "duration": 1500,
            "order": 1,
            "isCompleted": false,
            "progress": 0,
            "topics": [
              {
                "id": "topic_ancient_history",
                "name": "Ancient History",
                "description": "Indus Valley Civilization to Gupta Period",
                "videoCount": 8,
                "pdfCount": 3,
                "testCount": 2,
                "duration": 480,
                "order": 1,
                "isCompleted": false,
                "progress": 0
              },
              {
                "id": "topic_medieval_history",
                "name": "Medieval History",
                "description": "Delhi Sultanate to Mughal Empire",
                "videoCount": 10,
                "pdfCount": 3,
                "testCount": 2,
                "duration": 600,
                "order": 2,
                "isCompleted": false,
                "progress": 0
              },
              {
                "id": "topic_modern_history",
                "name": "Modern History",
                "description": "British Rule to Independence",
                "videoCount": 7,
                "pdfCount": 2,
                "testCount": 1,
                "duration": 420,
                "order": 3,
                "isCompleted": false,
                "progress": 0
              }
            ]
          },
          {
            "id": "subject_polity",
            "name": "Polity",
            "description": "Indian Constitution and Political System",
            "videoCount": 20,
            "pdfCount": 7,
            "testCount": 4,
            "duration": 1200,
            "order": 2,
            "isCompleted": false,
            "progress": 0,
            "topics": [
              {
                "id": "topic_constitution",
                "name": "Constitution",
                "description": "Making of Constitution and Basic Features",
                "videoCount": 8,
                "pdfCount": 3,
                "testCount": 2,
                "duration": 480,
                "order": 1,
                "isCompleted": false,
                "progress": 0
              },
              {
                "id": "topic_fundamental_rights",
                "name": "Fundamental Rights",
                "description": "Rights and Duties of Citizens",
                "videoCount": 6,
                "pdfCount": 2,
                "testCount": 1,
                "duration": 360,
                "order": 2,
                "isCompleted": false,
                "progress": 0
              },
              {
                "id": "topic_governance",
                "name": "Governance",
                "description": "Union and State Government Structure",
                "videoCount": 6,
                "pdfCount": 2,
                "testCount": 1,
                "duration": 360,
                "order": 3,
                "isCompleted": false,
                "progress": 0
              }
            ]
          }
        ]
      },
      "stats": {
        "enrolledStudents": 1250,
        "averageRating": 4.5,
        "totalRatings": 320,
        "completionRate": 78,
        "totalReviews": 180
      },
      "features": [
        "Lifetime Access",
        "Mobile App Access",
        "Certificate of Completion",
        "24/7 Doubt Support",
        "Mock Tests Included",
        "PDF Notes Download",
        "Progress Tracking"
      ],
      "requirements": [
        "Basic understanding of BPSC exam pattern",
        "12th pass or equivalent",
        "Dedication to study 2-3 hours daily"
      ],
      "learningOutcomes": [
        "Complete BPSC Prelims syllabus coverage",
        "Exam strategy and time management",
        "Current affairs updates",
        "Mock test practice",
        "Answer writing skills"
      ],
      "isEnrolled": false,
      "userProgress": null,
      "tags": ["complete", "prelims", "hindi", "2024", "bestseller"],
      "status": "active",
      "isFeatured": true,
      "isPopular": true,
      "isBestseller": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z",
      "publishedAt": "2024-01-05T00:00:00Z"
    }
  }
}
```

**Business Logic:**

- Include complete subject and topic hierarchy
- Show user's progress if enrolled
- Calculate real-time statistics and ratings
- Include instructor details and qualifications
- Show pricing with discount information

---

### **3.4 Get Subject Content (Videos, PDFs, Tests)**

**Endpoint:** `GET /api/courses/{courseId}/subjects/{subjectId}/content`

**Purpose:** Get all content (videos, PDFs, tests) for a specific subject (Step 4 in hierarchy)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
type: string (optional) - Filter by content type: video, pdf, test
topicId: string (optional) - Filter by specific topic
includeProgress: boolean (default: true) - Include user progress
sortBy: string (default: "order") - Sort by: order, duration, name
```

**Response:**

```json
{
  "success": true,
  "message": "Subject content retrieved successfully",
  "data": {
    "subject": {
      "id": "subject_history",
      "name": "History",
      "description": "Ancient, Medieval and Modern Indian History",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course"
    },
    "content": {
      "videos": [
        {
          "id": "video_ancient_history_1",
          "title": "Introduction to Ancient History",
          "description": "Overview of Ancient Indian History and Sources",
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "duration": 1800,
          "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-1.jpg",
          "videoUrl": "https://cdn.example.com/videos/ancient-history-1.mp4",
          "quality": ["720p", "480p", "360p"],
          "isWatched": false,
          "watchProgress": 0,
          "lastWatchedAt": null,
          "canDownload": true,
          "order": 1,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ],
      "pdfs": [
        {
          "id": "pdf_ancient_history_notes",
          "title": "Ancient History Complete Notes",
          "description": "Comprehensive notes covering all topics in Ancient History",
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "fileUrl": "https://cdn.example.com/pdfs/ancient-history-notes.pdf",
          "fileSize": 2048000,
          "pageCount": 45,
          "canDownload": true,
          "isDownloaded": false,
          "order": 1,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ],
      "tests": [
        {
          "id": "test_ancient_history_mcq",
          "title": "Ancient History MCQ Test",
          "description": "Multiple choice questions on Ancient Indian History",
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "type": "mcq",
          "totalQuestions": 25,
          "duration": 1800,
          "maxScore": 100,
          "passingScore": 40,
          "isAttempted": false,
          "bestScore": null,
          "totalAttempts": 0,
          "order": 1,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ]
    },
    "userProgress": {
      "videosWatched": 0,
      "totalVideos": 25,
      "videosProgress": 0,
      "pdfsViewed": 0,
      "totalPDFs": 8,
      "testsAttempted": 0,
      "totalTests": 5,
      "testsCompleted": 0,
      "averageTestScore": 0,
      "totalTimeSpent": 0,
      "completionPercentage": 0,
      "lastAccessedAt": null
    }
  }
}
```

**Business Logic:**

- Group content by type (videos, PDFs, tests)
- Include user's progress and watch history
- Show download permissions based on subscription
- Sort content by order or other criteria
- Track user engagement and completion

---

### **3.5 Course Enrollment**

**Endpoint:** `POST /api/courses/{courseId}/enroll`

**Purpose:** Enroll user in a course

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "paymentMethod": "free",
  "couponCode": "SPECIAL50",
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Successfully enrolled in course",
  "data": {
    "enrollment": {
      "id": "enrollment_123",
      "userId": "user_123",
      "courseId": "course_bpsc_70th_prelims",
      "enrolledAt": "2024-01-15T15:30:00Z",
      "expiresAt": null,
      "status": "active",
      "paymentStatus": "completed",
      "accessLevel": "full"
    },
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "isEnrolled": true,
      "userProgress": {
        "completionPercentage": 0,
        "videosWatched": 0,
        "testsAttempted": 0,
        "testsCompleted": 0,
        "lastAccessedAt": "2024-01-15T15:30:00Z",
        "totalTimeSpent": 0
      }
    }
  }
}
```

---

## 📹 **PHASE 4: VIDEO MODULE APIs (Step 3 - Requirement.md)**

> **Implementation Priority:** HIGH - Core content delivery and user engagement
> **Dependencies:** Phase 1 (Authentication) and Phase 3 (Course Hierarchy) must be completed first
> **Frontend Status:** ✅ READY - Complete video player with all features implemented

### **Overview:**

The Video Module provides comprehensive video streaming capabilities with advanced features like resume playback, progress tracking, bookmarks, and offline downloads. This implements the core content delivery system for the educational platform.

**Key Features:**

- Streaming video player with multiple quality options
- Resume playback from last watched position
- Progress bar with seek functionality
- Bookmark system for important moments
- Offline download for paid users
- Video categorization by class, subject, and topic
- Watch history and analytics

---

### **4.1 Get Video Details**

**Endpoint:** `GET /api/videos/{videoId}`

**Purpose:** Get detailed video information with streaming URLs and user progress

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeProgress: boolean (default: true) - Include user's watch progress
includeBookmarks: boolean (default: true) - Include user's bookmarks
includeRelated: boolean (default: false) - Include related videos
quality: string (optional) - Preferred video quality (720p, 480p, 360p)
```

**Response:**

```json
{
  "success": true,
  "message": "Video details retrieved successfully",
  "data": {
    "video": {
      "id": "video_ancient_history_1",
      "title": "Introduction to Ancient History",
      "description": "Comprehensive overview of Ancient Indian History covering Indus Valley Civilization, Vedic Period, and major dynasties with detailed analysis of sources and archaeological evidence.",
      "shortDescription": "Overview of Ancient Indian History and Sources",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course",
      "subjectId": "subject_history",
      "subjectName": "History",
      "topicId": "topic_ancient_history",
      "topicName": "Ancient History",
      "instructorId": "instructor_rajesh_kumar",
      "instructorName": "Dr. Rajesh Kumar",
      "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
      "duration": 1800,
      "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-1.jpg",
      "previewUrl": "https://cdn.example.com/previews/ancient-history-1-preview.mp4",
      "streamingUrls": {
        "720p": "https://cdn.example.com/videos/ancient-history-1-720p.m3u8",
        "480p": "https://cdn.example.com/videos/ancient-history-1-480p.m3u8",
        "360p": "https://cdn.example.com/videos/ancient-history-1-360p.m3u8"
      },
      "downloadUrls": {
        "720p": "https://cdn.example.com/downloads/ancient-history-1-720p.mp4",
        "480p": "https://cdn.example.com/downloads/ancient-history-1-480p.mp4",
        "360p": "https://cdn.example.com/downloads/ancient-history-1-360p.mp4"
      },
      "fileSize": {
        "720p": 524288000,
        "480p": 314572800,
        "360p": 209715200
      },
      "language": "hindi",
      "hasSubtitles": true,
      "subtitleUrls": {
        "hindi": "https://cdn.example.com/subtitles/ancient-history-1-hi.vtt",
        "english": "https://cdn.example.com/subtitles/ancient-history-1-en.vtt"
      },
      "tags": ["ancient", "history", "indus-valley", "vedic-period"],
      "difficulty": "intermediate",
      "order": 1,
      "isActive": true,
      "canDownload": true,
      "downloadExpiry": "2024-12-31T23:59:59Z",
      "views": 1250,
      "likes": 89,
      "dislikes": 3,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z",
      "publishedAt": "2024-01-05T00:00:00Z"
    },
    "userProgress": {
      "isWatched": false,
      "watchProgress": 0.35,
      "lastWatchedAt": "2024-01-14T18:30:00Z",
      "watchedDuration": 630,
      "totalWatchTime": 1200,
      "watchCount": 3,
      "isCompleted": false,
      "completedAt": null,
      "resumePosition": 630
    },
    "userBookmarks": [
      {
        "id": "bookmark_123",
        "videoId": "video_ancient_history_1",
        "position": 450,
        "title": "Indus Valley Civilization starts",
        "description": "Important section about IVC",
        "createdAt": "2024-01-14T18:35:00Z"
      },
      {
        "id": "bookmark_124",
        "videoId": "video_ancient_history_1",
        "position": 1200,
        "title": "Vedic Period explanation",
        "description": "Key concepts of Vedic society",
        "createdAt": "2024-01-14T18:45:00Z"
      }
    ],
    "relatedVideos": [
      {
        "id": "video_ancient_history_2",
        "title": "Indus Valley Civilization in Detail",
        "duration": 2100,
        "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-2.jpg",
        "isWatched": false,
        "order": 2
      }
    ]
  }
}
```

**Business Logic:**

- Check user's enrollment status for the course
- Return appropriate streaming URLs based on subscription
- Include download permissions based on user's plan
- Track video access for analytics
- Calculate watch progress and completion status

---

### **4.2 Update Video Progress**

**Endpoint:** `PUT /api/videos/{videoId}/progress`

**Purpose:** Update user's video watch progress and resume position

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "position": 630,
  "duration": 1800,
  "watchedDuration": 45,
  "quality": "720p",
  "isCompleted": false,
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Video progress updated successfully",
  "data": {
    "progress": {
      "videoId": "video_ancient_history_1",
      "userId": "user_123",
      "position": 630,
      "watchProgress": 0.35,
      "watchedDuration": 45,
      "totalWatchTime": 1245,
      "watchCount": 3,
      "isCompleted": false,
      "lastWatchedAt": "2024-01-15T16:00:00Z",
      "updatedAt": "2024-01-15T16:00:00Z"
    },
    "achievements": [
      {
        "type": "watch_streak",
        "title": "5 Day Watch Streak!",
        "description": "You've watched videos for 5 consecutive days",
        "points": 50
      }
    ]
  }
}
```

**Business Logic:**

- Update user's watch position for resume functionality
- Calculate completion percentage
- Track total watch time for analytics
- Award achievements for milestones
- Update course progress if video is completed

### **4.3 Video Bookmark Management**

**Endpoint:** `POST /api/videos/{videoId}/bookmarks`

**Purpose:** Create a new bookmark at specific video position

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "position": 450,
  "title": "Indus Valley Civilization starts",
  "description": "Important section about IVC features and characteristics"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Bookmark created successfully",
  "data": {
    "bookmark": {
      "id": "bookmark_125",
      "videoId": "video_ancient_history_1",
      "userId": "user_123",
      "position": 450,
      "title": "Indus Valley Civilization starts",
      "description": "Important section about IVC features and characteristics",
      "createdAt": "2024-01-15T16:15:00Z"
    }
  }
}
```

---

**Endpoint:** `GET /api/videos/{videoId}/bookmarks`

**Purpose:** Get all user bookmarks for a specific video

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "Bookmarks retrieved successfully",
  "data": {
    "bookmarks": [
      {
        "id": "bookmark_123",
        "position": 450,
        "title": "Indus Valley Civilization starts",
        "description": "Important section about IVC",
        "createdAt": "2024-01-14T18:35:00Z"
      },
      {
        "id": "bookmark_124",
        "position": 1200,
        "title": "Vedic Period explanation",
        "description": "Key concepts of Vedic society",
        "createdAt": "2024-01-14T18:45:00Z"
      }
    ],
    "totalCount": 2
  }
}
```

---

**Endpoint:** `DELETE /api/bookmarks/{bookmarkId}`

**Purpose:** Delete a specific bookmark

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "Bookmark deleted successfully"
}
```

---

### **4.4 Video Download Management**

**Endpoint:** `POST /api/videos/{videoId}/download`

**Purpose:** Initiate video download for offline viewing (paid users only)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "quality": "720p",
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android",
    "storageAvailable": 2048000000
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Download initiated successfully",
  "data": {
    "download": {
      "id": "download_456",
      "videoId": "video_ancient_history_1",
      "userId": "user_123",
      "quality": "720p",
      "fileSize": 524288000,
      "downloadUrl": "https://cdn.example.com/secure-downloads/ancient-history-1-720p.mp4?token=secure_token_here",
      "expiresAt": "2024-01-15T18:00:00Z",
      "status": "initiated",
      "deviceId": "device_unique_id",
      "createdAt": "2024-01-15T16:30:00Z"
    },
    "downloadLimits": {
      "dailyLimit": 5,
      "dailyUsed": 2,
      "monthlyLimit": 50,
      "monthlyUsed": 15,
      "deviceLimit": 2,
      "deviceUsed": 1
    }
  }
}
```

**Business Logic:**

- Check user's subscription status and download permissions
- Validate device limits and daily/monthly quotas
- Generate secure download URL with expiry
- Track download usage for analytics
- Support resume downloads for large files

---

**Endpoint:** `GET /api/user/downloads`

**Purpose:** Get user's download history and status

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
status: string (optional) - Filter by status: initiated, downloading, completed, failed, expired
page: number (default: 1) - Page number
limit: number (default: 10) - Items per page
```

**Response:**

```json
{
  "success": true,
  "message": "Downloads retrieved successfully",
  "data": {
    "downloads": [
      {
        "id": "download_456",
        "videoId": "video_ancient_history_1",
        "videoTitle": "Introduction to Ancient History",
        "courseName": "BPSC 70th Prelims Complete Course",
        "quality": "720p",
        "fileSize": 524288000,
        "status": "completed",
        "progress": 100,
        "downloadedAt": "2024-01-15T17:00:00Z",
        "expiresAt": "2024-02-15T17:00:00Z",
        "deviceId": "device_unique_id"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 15,
      "itemsPerPage": 10
    },
    "downloadLimits": {
      "dailyLimit": 5,
      "dailyUsed": 2,
      "monthlyLimit": 50,
      "monthlyUsed": 15
    }
  }
}
```

---

### **4.5 Video Analytics and Engagement**

**Endpoint:** `POST /api/videos/{videoId}/analytics`

**Purpose:** Track video engagement events (play, pause, seek, quality change)

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "event": "play",
  "position": 630,
  "quality": "720p",
  "timestamp": "2024-01-15T16:45:00Z",
  "sessionId": "session_789",
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android",
    "appVersion": "1.0.0"
  },
  "metadata": {
    "bufferTime": 2.5,
    "networkType": "wifi",
    "playbackSpeed": 1.0
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Analytics event recorded successfully",
  "data": {
    "eventId": "event_123",
    "processed": true
  }
}
```

**Supported Events:**

- `play` - Video started playing
- `pause` - Video paused
- `seek` - User seeked to different position
- `quality_change` - Video quality changed
- `speed_change` - Playback speed changed
- `fullscreen_enter` - Entered fullscreen mode
- `fullscreen_exit` - Exited fullscreen mode
- `buffer_start` - Video started buffering
- `buffer_end` - Video finished buffering
- `error` - Playback error occurred

---

**Endpoint:** `GET /api/videos/{videoId}/stats`

**Purpose:** Get video statistics and engagement metrics

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "Video statistics retrieved successfully",
  "data": {
    "video": {
      "id": "video_ancient_history_1",
      "title": "Introduction to Ancient History"
    },
    "stats": {
      "totalViews": 1250,
      "uniqueViewers": 890,
      "totalWatchTime": 1575000,
      "averageWatchTime": 1260,
      "completionRate": 0.78,
      "engagementRate": 0.85,
      "likes": 89,
      "dislikes": 3,
      "bookmarks": 45,
      "downloads": 156,
      "shares": 23
    },
    "qualityDistribution": {
      "720p": 0.65,
      "480p": 0.25,
      "360p": 0.1
    },
    "deviceDistribution": {
      "mobile": 0.75,
      "tablet": 0.15,
      "desktop": 0.1
    },
    "userEngagement": {
      "averageSessionDuration": 1800,
      "bounceRate": 0.12,
      "repeatViewers": 0.45
    }
  }
}
```

## 📋 **VIDEO MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 3 (Video Module):**

1. **`GET /api/videos/{videoId}`** - Get video details with streaming URLs (PRIMARY)
2. **`PUT /api/videos/{videoId}/progress`** - Update watch progress and resume position
3. **`POST /api/videos/{videoId}/bookmarks`** - Create video bookmarks
4. **`GET /api/videos/{videoId}/bookmarks`** - Get user bookmarks for video
5. **`DELETE /api/bookmarks/{bookmarkId}`** - Delete specific bookmark
6. **`POST /api/videos/{videoId}/download`** - Initiate video download (paid users)
7. **`GET /api/user/downloads`** - Get download history and status
8. **`POST /api/videos/{videoId}/analytics`** - Track video engagement events
9. **`GET /api/videos/{videoId}/stats`** - Get video statistics and metrics

### **🏗️ Database Schema Requirements:**

#### **Videos Table:**

```sql
CREATE TABLE videos (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  short_description VARCHAR(500),
  course_id VARCHAR(36) NOT NULL,
  subject_id VARCHAR(36) NOT NULL,
  topic_id VARCHAR(36),
  instructor_id VARCHAR(36) NOT NULL,
  duration INT NOT NULL, -- in seconds
  thumbnail_url VARCHAR(500),
  preview_url VARCHAR(500),
  streaming_urls JSON, -- {720p: url, 480p: url, 360p: url}
  download_urls JSON,
  file_sizes JSON, -- {720p: size, 480p: size, 360p: size}
  language VARCHAR(10) DEFAULT 'hindi',
  has_subtitles BOOLEAN DEFAULT false,
  subtitle_urls JSON,
  tags JSON,
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'intermediate',
  order_index INT DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  can_download BOOLEAN DEFAULT true,
  download_expiry TIMESTAMP,
  views INT DEFAULT 0,
  likes INT DEFAULT 0,
  dislikes INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id),
  FOREIGN KEY (topic_id) REFERENCES topics(id),
  FOREIGN KEY (instructor_id) REFERENCES instructors(id)
);
```

#### **Video Progress Table:**

```sql
CREATE TABLE video_progress (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  video_id VARCHAR(36) NOT NULL,
  position INT DEFAULT 0, -- current position in seconds
  watch_progress DECIMAL(5,4) DEFAULT 0, -- percentage (0.0 to 1.0)
  watched_duration INT DEFAULT 0, -- total watched time in seconds
  total_watch_time INT DEFAULT 0, -- cumulative watch time
  watch_count INT DEFAULT 0,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP NULL,
  last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (video_id) REFERENCES videos(id),
  UNIQUE KEY unique_user_video (user_id, video_id)
);
```

#### **Video Bookmarks Table:**

```sql
CREATE TABLE video_bookmarks (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  video_id VARCHAR(36) NOT NULL,
  position INT NOT NULL, -- position in seconds
  title VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (video_id) REFERENCES videos(id)
);
```

#### **Video Downloads Table:**

```sql
CREATE TABLE video_downloads (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  video_id VARCHAR(36) NOT NULL,
  quality VARCHAR(10) NOT NULL, -- 720p, 480p, 360p
  file_size BIGINT NOT NULL,
  download_url VARCHAR(1000),
  status ENUM('initiated', 'downloading', 'completed', 'failed', 'expired') DEFAULT 'initiated',
  progress INT DEFAULT 0, -- percentage
  device_id VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  downloaded_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (video_id) REFERENCES videos(id)
);
```

#### **Video Analytics Table:**

```sql
CREATE TABLE video_analytics (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  video_id VARCHAR(36) NOT NULL,
  event_type VARCHAR(50) NOT NULL, -- play, pause, seek, etc.
  position INT, -- position in seconds
  quality VARCHAR(10),
  session_id VARCHAR(100),
  device_id VARCHAR(255),
  platform VARCHAR(50),
  app_version VARCHAR(20),
  metadata JSON, -- additional event data
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (video_id) REFERENCES videos(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Video Streaming:**

- Use HLS (HTTP Live Streaming) for adaptive bitrate streaming
- Support multiple quality levels (720p, 480p, 360p)
- Implement CDN for global content delivery
- Use signed URLs for secure video access

#### **Progress Tracking:**

- Update progress every 10-15 seconds during playback
- Mark video as completed when 90% watched
- Store resume position for seamless continuation
- Track total watch time for analytics

#### **Download Management:**

- Implement download quotas based on subscription
- Use secure, time-limited download URLs
- Support download resume for large files
- Track device limits and usage

#### **Performance Optimization:**

- Index on user_id, video_id, and timestamp fields
- Use Redis for caching video metadata
- Implement video thumbnail generation
- Optimize database queries with proper indexing

#### **Security Considerations:**

- Validate user enrollment before providing video access
- Implement rate limiting on progress updates
- Secure download URLs with expiry tokens
- Prevent unauthorized video access

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Video Player**: Complete player with all controls implemented
- **Progress Tracking**: Automatic progress updates during playback
- **Bookmark System**: Create, view, and navigate to bookmarks
- **Download Manager**: Initiate and track video downloads
- **Analytics**: Track all user interactions and engagement
- **Resume Playback**: Continue from last watched position

### **🚀 Implementation Priority:**

1. **HIGH**: Video details and streaming URLs (core functionality)
2. **HIGH**: Progress tracking and resume playback (user experience)
3. **MEDIUM**: Bookmark management (engagement feature)
4. **MEDIUM**: Download management (premium feature)
5. **LOW**: Analytics and statistics (insights and optimization)

---

## 📄 **PHASE 5: PDF NOTES MODULE APIs (Step 4 - Requirement.md)**

> **Implementation Priority:** HIGH - Core study material delivery
> **Dependencies:** Phase 1 (Authentication) and Phase 3 (Course Hierarchy) must be completed first
> **Frontend Status:** ✅ READY - Complete PDF viewer with all features implemented

### **Overview:**

The PDF Notes Module provides comprehensive PDF document management with in-app viewing, download capabilities, and categorization by subject, class, and topic. This implements the core study material delivery system for educational content.

**Key Features:**

- In-app PDF viewer with zoom and navigation
- Admin-configurable download permissions
- PDF categorization by subject, class, and topic
- Bookmark system for important pages
- Progress tracking and reading analytics
- Offline access for downloaded PDFs
- Search functionality within PDFs

---

### **5.1 Get PDF Details**

**Endpoint:** `GET /api/pdfs/{pdfId}`

**Purpose:** Get detailed PDF information with viewing URLs and user progress

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeProgress: boolean (default: true) - Include user's reading progress
includeBookmarks: boolean (default: true) - Include user's bookmarks
includeContent: boolean (default: false) - Include PDF content metadata
```

**Response:**

```json
{
  "success": true,
  "message": "PDF details retrieved successfully",
  "data": {
    "pdf": {
      "id": "pdf_ancient_history_notes",
      "title": "Ancient History Complete Notes",
      "description": "Comprehensive study material covering Ancient Indian History from Indus Valley Civilization to Gupta Period with detailed analysis, maps, and important dates.",
      "shortDescription": "Complete notes on Ancient Indian History",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course",
      "subjectId": "subject_history",
      "subjectName": "History",
      "topicId": "topic_ancient_history",
      "topicName": "Ancient History",
      "instructorId": "instructor_rajesh_kumar",
      "instructorName": "Dr. Rajesh Kumar",
      "authorName": "Dr. Rajesh Kumar",
      "fileUrl": "https://cdn.example.com/pdfs/ancient-history-complete-notes.pdf",
      "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-notes.jpg",
      "previewUrl": "https://cdn.example.com/previews/ancient-history-notes-preview.pdf",
      "fileSize": 15728640,
      "fileSizeFormatted": "15.2 MB",
      "pageCount": 120,
      "language": "hindi",
      "format": "pdf",
      "version": "1.2",
      "tags": ["ancient", "history", "complete", "notes", "prelims"],
      "difficulty": "intermediate",
      "order": 1,
      "isActive": true,
      "canDownload": true,
      "canView": true,
      "downloadPermission": "enrolled_users",
      "downloadExpiry": "2024-12-31T23:59:59Z",
      "views": 2500,
      "downloads": 1250,
      "likes": 189,
      "dislikes": 8,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z",
      "publishedAt": "2024-01-05T00:00:00Z",
      "lastModified": "2024-01-10T15:30:00Z"
    },
    "userProgress": {
      "isViewed": true,
      "readingProgress": 0.65,
      "lastViewedAt": "2024-01-14T18:30:00Z",
      "lastViewedPage": 78,
      "totalReadingTime": 3600,
      "sessionCount": 8,
      "isCompleted": false,
      "completedAt": null,
      "bookmarkCount": 5
    },
    "userBookmarks": [
      {
        "id": "bookmark_pdf_123",
        "pdfId": "pdf_ancient_history_notes",
        "pageNumber": 15,
        "title": "Indus Valley Civilization Features",
        "description": "Key characteristics of IVC",
        "createdAt": "2024-01-14T18:35:00Z"
      },
      {
        "id": "bookmark_pdf_124",
        "pdfId": "pdf_ancient_history_notes",
        "pageNumber": 45,
        "title": "Mauryan Administration",
        "description": "Administrative system under Mauryas",
        "createdAt": "2024-01-14T19:15:00Z"
      }
    ],
    "downloadInfo": {
      "isDownloaded": false,
      "canDownload": true,
      "downloadUrl": null,
      "downloadExpiresAt": null,
      "downloadSize": 15728640,
      "downloadCount": 1250
    }
  }
}
```

**Business Logic:**

- Check user's enrollment status for the course
- Return appropriate viewing URLs based on subscription
- Include download permissions based on admin configuration
- Track PDF access for analytics
- Calculate reading progress and completion status

---

### **5.2 Get PDFs by Subject/Topic**

**Endpoint:** `GET /api/courses/{courseId}/subjects/{subjectId}/pdfs`

**Purpose:** Get all PDFs for a specific subject or topic

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
topicId: string (optional) - Filter by specific topic
page: number (default: 1) - Page number for pagination
limit: number (default: 10) - Number of PDFs per page
sortBy: string (default: "order") - Sort by: order, title, date, popularity
sortOrder: string (default: "asc") - Sort order: asc, desc
includeProgress: boolean (default: true) - Include user progress
```

**Response:**

```json
{
  "success": true,
  "message": "PDFs retrieved successfully",
  "data": {
    "subject": {
      "id": "subject_history",
      "name": "History",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course"
    },
    "pdfs": [
      {
        "id": "pdf_ancient_history_notes",
        "title": "Ancient History Complete Notes",
        "description": "Comprehensive study material covering all topics in Ancient History",
        "topicId": "topic_ancient_history",
        "topicName": "Ancient History",
        "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-notes.jpg",
        "fileSize": 15728640,
        "fileSizeFormatted": "15.2 MB",
        "pageCount": 120,
        "authorName": "Dr. Rajesh Kumar",
        "language": "hindi",
        "tags": ["ancient", "history", "complete", "notes"],
        "difficulty": "intermediate",
        "order": 1,
        "canDownload": true,
        "canView": true,
        "views": 2500,
        "downloads": 1250,
        "likes": 189,
        "userProgress": {
          "isViewed": true,
          "readingProgress": 0.65,
          "lastViewedAt": "2024-01-14T18:30:00Z",
          "lastViewedPage": 78,
          "isCompleted": false
        },
        "downloadInfo": {
          "isDownloaded": false,
          "canDownload": true
        },
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      },
      {
        "id": "pdf_medieval_history_notes",
        "title": "Medieval History Quick Revision",
        "description": "Concise notes for quick revision of Medieval Indian History",
        "topicId": "topic_medieval_history",
        "topicName": "Medieval History",
        "thumbnailUrl": "https://cdn.example.com/thumbnails/medieval-history-notes.jpg",
        "fileSize": 6291456,
        "fileSizeFormatted": "6.0 MB",
        "pageCount": 45,
        "authorName": "Dr. Priya Sharma",
        "language": "hindi",
        "tags": ["medieval", "history", "revision", "notes"],
        "difficulty": "intermediate",
        "order": 2,
        "canDownload": true,
        "canView": true,
        "views": 1800,
        "downloads": 950,
        "likes": 142,
        "userProgress": {
          "isViewed": false,
          "readingProgress": 0,
          "lastViewedAt": null,
          "lastViewedPage": 0,
          "isCompleted": false
        },
        "downloadInfo": {
          "isDownloaded": false,
          "canDownload": true
        },
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 15,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "stats": {
      "totalPDFs": 15,
      "totalPages": 850,
      "totalSize": 125829120,
      "userViewedCount": 8,
      "userDownloadedCount": 5
    }
  }
}
```

**Business Logic:**

- Filter PDFs by subject and optionally by topic
- Include user's reading progress for each PDF
- Show download permissions based on admin settings
- Calculate aggregate statistics
- Sort by various criteria (order, popularity, date)

### **5.3 Update PDF Reading Progress**

**Endpoint:** `PUT /api/pdfs/{pdfId}/progress`

**Purpose:** Update user's PDF reading progress and current page

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "currentPage": 78,
  "totalPages": 120,
  "readingTime": 300,
  "isCompleted": false,
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "PDF progress updated successfully",
  "data": {
    "progress": {
      "pdfId": "pdf_ancient_history_notes",
      "userId": "user_123",
      "currentPage": 78,
      "readingProgress": 0.65,
      "totalReadingTime": 3900,
      "sessionCount": 9,
      "isCompleted": false,
      "lastViewedAt": "2024-01-15T16:00:00Z",
      "updatedAt": "2024-01-15T16:00:00Z"
    },
    "achievements": [
      {
        "type": "reading_streak",
        "title": "7 Day Reading Streak!",
        "description": "You've read PDFs for 7 consecutive days",
        "points": 70
      }
    ]
  }
}
```

**Business Logic:**

- Update user's current page position
- Calculate reading progress percentage
- Track total reading time for analytics
- Award achievements for milestones
- Update course progress if PDF is completed

---

### **5.4 PDF Bookmark Management**

**Endpoint:** `POST /api/pdfs/{pdfId}/bookmarks`

**Purpose:** Create a new bookmark at specific PDF page

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "pageNumber": 45,
  "title": "Mauryan Administration",
  "description": "Important section about administrative system under Mauryas"
}
```

**Response:**

```json
{
  "success": true,
  "message": "PDF bookmark created successfully",
  "data": {
    "bookmark": {
      "id": "bookmark_pdf_125",
      "pdfId": "pdf_ancient_history_notes",
      "userId": "user_123",
      "pageNumber": 45,
      "title": "Mauryan Administration",
      "description": "Important section about administrative system under Mauryas",
      "createdAt": "2024-01-15T16:15:00Z"
    }
  }
}
```

---

**Endpoint:** `GET /api/pdfs/{pdfId}/bookmarks`

**Purpose:** Get all user bookmarks for a specific PDF

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "PDF bookmarks retrieved successfully",
  "data": {
    "bookmarks": [
      {
        "id": "bookmark_pdf_123",
        "pageNumber": 15,
        "title": "Indus Valley Civilization Features",
        "description": "Key characteristics of IVC",
        "createdAt": "2024-01-14T18:35:00Z"
      },
      {
        "id": "bookmark_pdf_124",
        "pageNumber": 45,
        "title": "Mauryan Administration",
        "description": "Administrative system under Mauryas",
        "createdAt": "2024-01-14T19:15:00Z"
      }
    ],
    "totalCount": 2
  }
}
```

---

**Endpoint:** `DELETE /api/pdf-bookmarks/{bookmarkId}`

**Purpose:** Delete a specific PDF bookmark

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "PDF bookmark deleted successfully"
}
```

---

### **5.5 PDF Download Management**

**Endpoint:** `POST /api/pdfs/{pdfId}/download`

**Purpose:** Initiate PDF download for offline viewing

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android",
    "storageAvailable": 2048000000
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "PDF download initiated successfully",
  "data": {
    "download": {
      "id": "download_pdf_456",
      "pdfId": "pdf_ancient_history_notes",
      "userId": "user_123",
      "fileSize": 15728640,
      "downloadUrl": "https://cdn.example.com/secure-downloads/ancient-history-notes.pdf?token=secure_token_here",
      "expiresAt": "2024-01-15T18:00:00Z",
      "status": "initiated",
      "deviceId": "device_unique_id",
      "createdAt": "2024-01-15T16:30:00Z"
    },
    "downloadLimits": {
      "dailyLimit": 10,
      "dailyUsed": 3,
      "monthlyLimit": 100,
      "monthlyUsed": 25,
      "deviceLimit": 5,
      "deviceUsed": 2
    }
  }
}
```

**Business Logic:**

- Check user's enrollment and download permissions
- Validate admin-configured download settings
- Generate secure download URL with expiry
- Track download usage for analytics
- Support offline access for downloaded PDFs

---

**Endpoint:** `GET /api/user/pdf-downloads`

**Purpose:** Get user's PDF download history and status

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
status: string (optional) - Filter by status: initiated, downloading, completed, failed, expired
page: number (default: 1) - Page number
limit: number (default: 10) - Items per page
```

**Response:**

```json
{
  "success": true,
  "message": "PDF downloads retrieved successfully",
  "data": {
    "downloads": [
      {
        "id": "download_pdf_456",
        "pdfId": "pdf_ancient_history_notes",
        "pdfTitle": "Ancient History Complete Notes",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectName": "History",
        "fileSize": 15728640,
        "fileSizeFormatted": "15.2 MB",
        "pageCount": 120,
        "status": "completed",
        "progress": 100,
        "downloadedAt": "2024-01-15T17:00:00Z",
        "expiresAt": "2024-02-15T17:00:00Z",
        "deviceId": "device_unique_id"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 18,
      "itemsPerPage": 10
    },
    "downloadLimits": {
      "dailyLimit": 10,
      "dailyUsed": 3,
      "monthlyLimit": 100,
      "monthlyUsed": 25
    }
  }
}
```

## 📋 **PDF NOTES MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 4 (PDF Notes Module):**

1. **`GET /api/pdfs/{pdfId}`** - Get PDF details with viewing URLs (PRIMARY)
2. **`GET /api/courses/{courseId}/subjects/{subjectId}/pdfs`** - Get PDFs by subject/topic
3. **`PUT /api/pdfs/{pdfId}/progress`** - Update reading progress and current page
4. **`POST /api/pdfs/{pdfId}/bookmarks`** - Create PDF bookmarks
5. **`GET /api/pdfs/{pdfId}/bookmarks`** - Get user bookmarks for PDF
6. **`DELETE /api/pdf-bookmarks/{bookmarkId}`** - Delete specific bookmark
7. **`POST /api/pdfs/{pdfId}/download`** - Initiate PDF download
8. **`GET /api/user/pdf-downloads`** - Get download history and status

### **🏗️ Database Schema Requirements:**

#### **PDFs Table:**

```sql
CREATE TABLE pdfs (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  short_description VARCHAR(500),
  course_id VARCHAR(36) NOT NULL,
  subject_id VARCHAR(36) NOT NULL,
  topic_id VARCHAR(36),
  instructor_id VARCHAR(36) NOT NULL,
  author_name VARCHAR(255),
  file_url VARCHAR(1000) NOT NULL,
  thumbnail_url VARCHAR(500),
  preview_url VARCHAR(500),
  file_size BIGINT NOT NULL,
  file_size_formatted VARCHAR(20),
  page_count INT NOT NULL,
  language VARCHAR(10) DEFAULT 'hindi',
  format VARCHAR(10) DEFAULT 'pdf',
  version VARCHAR(10) DEFAULT '1.0',
  tags JSON,
  difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'intermediate',
  order_index INT DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  can_download BOOLEAN DEFAULT true,
  can_view BOOLEAN DEFAULT true,
  download_permission ENUM('all', 'enrolled_users', 'paid_users') DEFAULT 'enrolled_users',
  download_expiry TIMESTAMP,
  views INT DEFAULT 0,
  downloads INT DEFAULT 0,
  likes INT DEFAULT 0,
  dislikes INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  last_modified TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id),
  FOREIGN KEY (topic_id) REFERENCES topics(id),
  FOREIGN KEY (instructor_id) REFERENCES instructors(id)
);
```

#### **PDF Progress Table:**

```sql
CREATE TABLE pdf_progress (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  pdf_id VARCHAR(36) NOT NULL,
  current_page INT DEFAULT 1,
  reading_progress DECIMAL(5,4) DEFAULT 0, -- percentage (0.0 to 1.0)
  total_reading_time INT DEFAULT 0, -- in seconds
  session_count INT DEFAULT 0,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP NULL,
  last_viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (pdf_id) REFERENCES pdfs(id),
  UNIQUE KEY unique_user_pdf (user_id, pdf_id)
);
```

#### **PDF Bookmarks Table:**

```sql
CREATE TABLE pdf_bookmarks (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  pdf_id VARCHAR(36) NOT NULL,
  page_number INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (pdf_id) REFERENCES pdfs(id)
);
```

#### **PDF Downloads Table:**

```sql
CREATE TABLE pdf_downloads (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  pdf_id VARCHAR(36) NOT NULL,
  file_size BIGINT NOT NULL,
  download_url VARCHAR(1000),
  status ENUM('initiated', 'downloading', 'completed', 'failed', 'expired') DEFAULT 'initiated',
  progress INT DEFAULT 0, -- percentage
  device_id VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  downloaded_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (pdf_id) REFERENCES pdfs(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **PDF Viewing:**

- Use secure, signed URLs for PDF access
- Support in-app PDF viewer with zoom and navigation
- Implement page-by-page progress tracking
- Cache PDF metadata for performance

#### **Progress Tracking:**

- Update progress when user changes pages
- Mark PDF as completed when 90% read
- Store current page for resume functionality
- Track total reading time for analytics

#### **Download Management:**

- Implement admin-configurable download permissions
- Use secure, time-limited download URLs
- Support offline access for downloaded PDFs
- Track download quotas and usage

#### **Performance Optimization:**

- Index on user_id, pdf_id, and course_id fields
- Use CDN for PDF file delivery
- Implement PDF thumbnail generation
- Optimize database queries with proper indexing

#### **Security Considerations:**

- Validate user enrollment before providing PDF access
- Implement rate limiting on progress updates
- Secure download URLs with expiry tokens
- Prevent unauthorized PDF access

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **PDF Viewer**: Complete viewer with zoom, navigation, and bookmarks
- **Progress Tracking**: Automatic progress updates during reading
- **Bookmark System**: Create, view, and navigate to bookmarks
- **Download Manager**: Initiate and track PDF downloads
- **Reading Analytics**: Track all user interactions and engagement

### **🚀 Implementation Priority:**

1. **HIGH**: PDF details and viewing URLs (core functionality)
2. **HIGH**: Progress tracking and bookmark system (user experience)
3. **MEDIUM**: Download management (offline access)
4. **MEDIUM**: Subject/topic filtering (content organization)
5. **LOW**: Advanced analytics and statistics (insights)

---

## 🧪 **PHASE 6: TEST MODULE APIs (Step 5 - Requirement.md)**

> **Implementation Priority:** HIGH - Core assessment and evaluation system
> **Dependencies:** Phase 1 (Authentication) and Phase 3 (Course Hierarchy) must be completed first
> **Frontend Status:** ✅ READY - Complete test engine with all question types and features implemented

### **Overview:**

The Test Module provides comprehensive assessment capabilities with multiple question types, timer functionality, score analysis, and performance tracking. This implements the core evaluation system for measuring student progress and understanding.

**Key Features:**

- Multiple question types: MCQ, True/False, Fill in the Blanks
- Test sections: Subject-wise, Topic-wise, Full Mock Tests
- Timer functionality with auto-submit
- Detailed score analysis and explanations
- Performance tracking and history
- Test retake functionality
- Comprehensive analytics and insights

---

### **6.1 Get Test Details**

**Endpoint:** `GET /api/tests/{testId}`

**Purpose:** Get detailed test information with questions and configuration

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeQuestions: boolean (default: true) - Include test questions
includeAttempts: boolean (default: true) - Include user's previous attempts
includeAnalytics: boolean (default: false) - Include test analytics
shuffleQuestions: boolean (default: true) - Randomize question order
```

**Response:**

```json
{
  "success": true,
  "message": "Test details retrieved successfully",
  "data": {
    "test": {
      "id": "test_ancient_history_mcq",
      "title": "Ancient History MCQ Test",
      "description": "Comprehensive test on Ancient Indian History covering Indus Valley Civilization, Vedic Period, and major dynasties with detailed explanations.",
      "shortDescription": "Test your knowledge of Ancient Indian History",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course",
      "subjectId": "subject_history",
      "subjectName": "History",
      "topicId": "topic_ancient_history",
      "topicName": "Ancient History",
      "instructorId": "instructor_rajesh_kumar",
      "instructorName": "Dr. Rajesh Kumar",
      "testType": "topic_test",
      "difficulty": "intermediate",
      "totalQuestions": 25,
      "totalMarks": 50,
      "passingMarks": 30,
      "duration": 1800,
      "negativeMarking": false,
      "negativeMarkingRatio": 0,
      "allowRetake": true,
      "maxAttempts": 3,
      "showResultsImmediately": true,
      "showCorrectAnswers": true,
      "showExplanations": true,
      "randomizeQuestions": true,
      "randomizeOptions": true,
      "isActive": true,
      "isPublished": true,
      "language": "hindi",
      "tags": ["ancient", "history", "mcq", "prelims"],
      "instructions": [
        "Read all questions carefully before answering",
        "Each question carries 2 marks",
        "No negative marking in this test",
        "You can review and change answers before submitting",
        "Timer will auto-submit the test when time expires"
      ],
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z",
      "publishedAt": "2024-01-05T00:00:00Z"
    },
    "questions": [
      {
        "id": "question_ancient_1",
        "testId": "test_ancient_history_mcq",
        "questionNumber": 1,
        "questionType": "mcq",
        "question": "Which of the following is considered the earliest civilization in the Indian subcontinent?",
        "options": [
          {
            "id": "option_1",
            "text": "Vedic Civilization",
            "order": 1
          },
          {
            "id": "option_2",
            "text": "Indus Valley Civilization",
            "order": 2
          },
          {
            "id": "option_3",
            "text": "Mauryan Civilization",
            "order": 3
          },
          {
            "id": "option_4",
            "text": "Gupta Civilization",
            "order": 4
          }
        ],
        "correctAnswer": "option_2",
        "explanation": "The Indus Valley Civilization (3300-1300 BCE) is considered the earliest known civilization in the Indian subcontinent, predating the Vedic period by several centuries.",
        "marks": 2,
        "difficulty": "easy",
        "tags": ["indus-valley", "civilization", "ancient"],
        "order": 1
      },
      {
        "id": "question_ancient_2",
        "testId": "test_ancient_history_mcq",
        "questionNumber": 2,
        "questionType": "true_false",
        "question": "The Indus Valley Civilization had a well-developed drainage system.",
        "options": [
          {
            "id": "option_true",
            "text": "True",
            "order": 1
          },
          {
            "id": "option_false",
            "text": "False",
            "order": 2
          }
        ],
        "correctAnswer": "option_true",
        "explanation": "True. The Indus Valley Civilization is famous for its advanced urban planning, including sophisticated drainage and sewerage systems that were far ahead of their time.",
        "marks": 2,
        "difficulty": "easy",
        "tags": ["indus-valley", "urban-planning"],
        "order": 2
      },
      {
        "id": "question_ancient_3",
        "testId": "test_ancient_history_mcq",
        "questionNumber": 3,
        "questionType": "fill_blank",
        "question": "The Indus Valley Civilization is also known as the _______ Civilization.",
        "correctAnswer": "Harappan",
        "acceptableAnswers": ["Harappan", "harappan", "HARAPPAN"],
        "explanation": "The Indus Valley Civilization is also called the Harappan Civilization, named after Harappa, one of the first sites to be excavated.",
        "marks": 2,
        "difficulty": "medium",
        "tags": ["indus-valley", "harappa"],
        "order": 3
      }
    ],
    "userAttempts": [
      {
        "id": "attempt_123",
        "testId": "test_ancient_history_mcq",
        "userId": "user_123",
        "attemptNumber": 1,
        "score": 42,
        "maxScore": 50,
        "percentage": 84,
        "timeTaken": 1200,
        "totalQuestions": 25,
        "correctAnswers": 21,
        "incorrectAnswers": 4,
        "unanswered": 0,
        "isPassed": true,
        "submittedAt": "2024-01-14T18:30:00Z",
        "status": "completed"
      }
    ],
    "testStats": {
      "totalAttempts": 1250,
      "averageScore": 38.5,
      "averageTime": 1350,
      "passRate": 0.78,
      "difficultyRating": 3.2
    }
  }
}
```

**Business Logic:**

- Check user's enrollment status for the course
- Randomize questions and options if configured
- Include user's previous attempts and best score
- Track test access for analytics
- Validate attempt limits and retake permissions

---

### **6.2 Submit Test Attempt**

**Endpoint:** `POST /api/tests/{testId}/submit`

**Purpose:** Submit user's test attempt with answers and calculate results

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "answers": [
    {
      "questionId": "question_ancient_1",
      "selectedAnswer": "option_2",
      "timeTaken": 45
    },
    {
      "questionId": "question_ancient_2",
      "selectedAnswer": "option_true",
      "timeTaken": 30
    },
    {
      "questionId": "question_ancient_3",
      "answer": "Harappan",
      "timeTaken": 60
    }
  ],
  "totalTimeTaken": 1200,
  "isAutoSubmit": false,
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Test submitted successfully",
  "data": {
    "attempt": {
      "id": "attempt_124",
      "testId": "test_ancient_history_mcq",
      "userId": "user_123",
      "attemptNumber": 2,
      "score": 46,
      "maxScore": 50,
      "percentage": 92,
      "timeTaken": 1200,
      "totalQuestions": 25,
      "correctAnswers": 23,
      "incorrectAnswers": 2,
      "unanswered": 0,
      "isPassed": true,
      "submittedAt": "2024-01-15T16:00:00Z",
      "status": "completed"
    },
    "results": {
      "questionResults": [
        {
          "questionId": "question_ancient_1",
          "isCorrect": true,
          "selectedAnswer": "option_2",
          "correctAnswer": "option_2",
          "marksObtained": 2,
          "timeTaken": 45
        },
        {
          "questionId": "question_ancient_2",
          "isCorrect": true,
          "selectedAnswer": "option_true",
          "correctAnswer": "option_true",
          "marksObtained": 2,
          "timeTaken": 30
        },
        {
          "questionId": "question_ancient_3",
          "isCorrect": true,
          "userAnswer": "Harappan",
          "correctAnswer": "Harappan",
          "marksObtained": 2,
          "timeTaken": 60
        }
      ],
      "subjectWiseAnalysis": [
        {
          "subjectId": "subject_history",
          "subjectName": "History",
          "totalQuestions": 25,
          "correctAnswers": 23,
          "score": 46,
          "maxScore": 50,
          "percentage": 92
        }
      ],
      "topicWiseAnalysis": [
        {
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "totalQuestions": 25,
          "correctAnswers": 23,
          "score": 46,
          "maxScore": 50,
          "percentage": 92
        }
      ]
    },
    "achievements": [
      {
        "type": "score_improvement",
        "title": "Score Improvement!",
        "description": "You improved your score by 8% from last attempt",
        "points": 20
      }
    ],
    "recommendations": [
      {
        "type": "study_material",
        "title": "Review Vedic Period",
        "description": "Focus on Vedic Period topics to improve your score",
        "resourceId": "pdf_vedic_period_notes",
        "resourceType": "pdf"
      }
    ]
  }
}
```

**Business Logic:**

- Validate all answers and calculate scores
- Apply negative marking if configured
- Generate detailed analysis by subject and topic
- Award achievements for improvements
- Provide personalized recommendations
- Update user's overall progress

### **6.3 Get Tests by Subject/Topic**

**Endpoint:** `GET /api/courses/{courseId}/subjects/{subjectId}/tests`

**Purpose:** Get all tests for a specific subject or topic

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
topicId: string (optional) - Filter by specific topic
testType: string (optional) - Filter by type: topic_test, subject_test, mock_test
difficulty: string (optional) - Filter by difficulty: easy, medium, hard
page: number (default: 1) - Page number for pagination
limit: number (default: 10) - Number of tests per page
sortBy: string (default: "order") - Sort by: order, title, difficulty, attempts
sortOrder: string (default: "asc") - Sort order: asc, desc
includeAttempts: boolean (default: true) - Include user's attempt history
```

**Response:**

```json
{
  "success": true,
  "message": "Tests retrieved successfully",
  "data": {
    "subject": {
      "id": "subject_history",
      "name": "History",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course"
    },
    "tests": [
      {
        "id": "test_ancient_history_mcq",
        "title": "Ancient History MCQ Test",
        "description": "Comprehensive test on Ancient Indian History",
        "testType": "topic_test",
        "topicId": "topic_ancient_history",
        "topicName": "Ancient History",
        "difficulty": "intermediate",
        "totalQuestions": 25,
        "totalMarks": 50,
        "passingMarks": 30,
        "duration": 1800,
        "negativeMarking": false,
        "allowRetake": true,
        "maxAttempts": 3,
        "language": "hindi",
        "tags": ["ancient", "history", "mcq"],
        "isActive": true,
        "order": 1,
        "userAttempts": [
          {
            "id": "attempt_123",
            "attemptNumber": 1,
            "score": 42,
            "maxScore": 50,
            "percentage": 84,
            "isPassed": true,
            "submittedAt": "2024-01-14T18:30:00Z"
          }
        ],
        "userStats": {
          "totalAttempts": 1,
          "bestScore": 42,
          "bestPercentage": 84,
          "averageScore": 42,
          "lastAttemptAt": "2024-01-14T18:30:00Z"
        },
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      },
      {
        "id": "test_medieval_history_mcq",
        "title": "Medieval History MCQ Test",
        "description": "Test on Medieval Indian History",
        "testType": "topic_test",
        "topicId": "topic_medieval_history",
        "topicName": "Medieval History",
        "difficulty": "intermediate",
        "totalQuestions": 20,
        "totalMarks": 40,
        "passingMarks": 24,
        "duration": 1500,
        "negativeMarking": false,
        "allowRetake": true,
        "maxAttempts": 3,
        "language": "hindi",
        "tags": ["medieval", "history", "mcq"],
        "isActive": true,
        "order": 2,
        "userAttempts": [],
        "userStats": {
          "totalAttempts": 0,
          "bestScore": 0,
          "bestPercentage": 0,
          "averageScore": 0,
          "lastAttemptAt": null
        },
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 15,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "stats": {
      "totalTests": 15,
      "attemptedTests": 8,
      "passedTests": 6,
      "averageScore": 78.5,
      "totalAttempts": 12
    }
  }
}
```

**Business Logic:**

- Filter tests by subject, topic, type, and difficulty
- Include user's attempt history and statistics
- Show test availability based on enrollment
- Calculate aggregate performance statistics
- Sort by various criteria

---

### **6.4 Get Test Performance Analytics**

**Endpoint:** `GET /api/tests/{testId}/analytics`

**Purpose:** Get detailed performance analytics for a specific test

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeQuestionAnalysis: boolean (default: true) - Include question-wise analysis
includeComparison: boolean (default: true) - Include peer comparison
timeRange: string (default: "all") - Time range: week, month, quarter, all
```

**Response:**

```json
{
  "success": true,
  "message": "Test analytics retrieved successfully",
  "data": {
    "test": {
      "id": "test_ancient_history_mcq",
      "title": "Ancient History MCQ Test",
      "totalQuestions": 25,
      "totalMarks": 50
    },
    "userPerformance": {
      "totalAttempts": 2,
      "bestScore": 46,
      "bestPercentage": 92,
      "averageScore": 44,
      "averagePercentage": 88,
      "improvement": 8,
      "timeSpentTotal": 2400,
      "averageTimePerAttempt": 1200,
      "rank": 15,
      "percentile": 85
    },
    "attemptHistory": [
      {
        "attemptNumber": 1,
        "score": 42,
        "percentage": 84,
        "timeTaken": 1200,
        "submittedAt": "2024-01-14T18:30:00Z",
        "rank": 25,
        "percentile": 75
      },
      {
        "attemptNumber": 2,
        "score": 46,
        "percentage": 92,
        "timeTaken": 1200,
        "submittedAt": "2024-01-15T16:00:00Z",
        "rank": 15,
        "percentile": 85
      }
    ],
    "questionAnalysis": [
      {
        "questionId": "question_ancient_1",
        "questionNumber": 1,
        "difficulty": "easy",
        "topic": "Indus Valley Civilization",
        "userCorrect": 2,
        "userIncorrect": 0,
        "successRate": 100,
        "averageTime": 42.5,
        "globalSuccessRate": 85,
        "globalAverageTime": 45
      },
      {
        "questionId": "question_ancient_2",
        "questionNumber": 2,
        "difficulty": "easy",
        "topic": "Indus Valley Civilization",
        "userCorrect": 2,
        "userIncorrect": 0,
        "successRate": 100,
        "averageTime": 30,
        "globalSuccessRate": 78,
        "globalAverageTime": 35
      }
    ],
    "topicWisePerformance": [
      {
        "topicId": "topic_indus_valley",
        "topicName": "Indus Valley Civilization",
        "totalQuestions": 8,
        "correctAnswers": 7,
        "successRate": 87.5,
        "averageTime": 35,
        "strength": "strong"
      },
      {
        "topicId": "topic_vedic_period",
        "topicName": "Vedic Period",
        "totalQuestions": 6,
        "correctAnswers": 5,
        "successRate": 83.3,
        "averageTime": 40,
        "strength": "good"
      }
    ],
    "peerComparison": {
      "totalParticipants": 1250,
      "userRank": 15,
      "userPercentile": 85,
      "averageScore": 38.5,
      "medianScore": 40,
      "topScore": 50,
      "userVsAverage": 7.5,
      "userVsMedian": 6
    },
    "recommendations": [
      {
        "type": "weak_topic",
        "title": "Focus on Mauryan Period",
        "description": "You scored 60% in Mauryan Period questions. Review the study material.",
        "priority": "high",
        "resourceId": "pdf_mauryan_period_notes",
        "resourceType": "pdf"
      },
      {
        "type": "time_management",
        "title": "Improve Time Management",
        "description": "You're spending too much time on easy questions. Practice more.",
        "priority": "medium"
      }
    ]
  }
}
```

**Business Logic:**

- Calculate comprehensive performance metrics
- Compare user performance with peers
- Identify strengths and weaknesses by topic
- Provide personalized recommendations
- Track improvement over time

---

### **6.5 Get User Test History**

**Endpoint:** `GET /api/user/test-history`

**Purpose:** Get user's complete test attempt history across all subjects

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
courseId: string (optional) - Filter by specific course
subjectId: string (optional) - Filter by specific subject
testType: string (optional) - Filter by test type
status: string (optional) - Filter by status: completed, in_progress, abandoned
page: number (default: 1) - Page number
limit: number (default: 20) - Items per page
sortBy: string (default: "submittedAt") - Sort by: submittedAt, score, percentage
sortOrder: string (default: "desc") - Sort order: asc, desc
```

**Response:**

```json
{
  "success": true,
  "message": "Test history retrieved successfully",
  "data": {
    "attempts": [
      {
        "id": "attempt_124",
        "testId": "test_ancient_history_mcq",
        "testTitle": "Ancient History MCQ Test",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_history",
        "subjectName": "History",
        "topicId": "topic_ancient_history",
        "topicName": "Ancient History",
        "testType": "topic_test",
        "attemptNumber": 2,
        "score": 46,
        "maxScore": 50,
        "percentage": 92,
        "timeTaken": 1200,
        "totalQuestions": 25,
        "correctAnswers": 23,
        "incorrectAnswers": 2,
        "unanswered": 0,
        "isPassed": true,
        "rank": 15,
        "percentile": 85,
        "submittedAt": "2024-01-15T16:00:00Z",
        "status": "completed"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 45,
      "itemsPerPage": 20
    },
    "summary": {
      "totalAttempts": 45,
      "totalTests": 28,
      "passedTests": 25,
      "failedTests": 3,
      "averageScore": 82.5,
      "averagePercentage": 82.5,
      "totalTimeSpent": 54000,
      "bestScore": 98,
      "worstScore": 45,
      "improvementTrend": "positive"
    },
    "subjectWiseStats": [
      {
        "subjectId": "subject_history",
        "subjectName": "History",
        "totalAttempts": 15,
        "averageScore": 85,
        "passRate": 93.3,
        "bestScore": 96
      },
      {
        "subjectId": "subject_polity",
        "subjectName": "Polity",
        "totalAttempts": 12,
        "averageScore": 78,
        "passRate": 83.3,
        "bestScore": 92
      }
    ]
  }
}
```

## 📋 **TEST MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 5 (Test Module):**

1. **`GET /api/tests/{testId}`** - Get test details with questions (PRIMARY)
2. **`POST /api/tests/{testId}/submit`** - Submit test attempt and get results
3. **`GET /api/courses/{courseId}/subjects/{subjectId}/tests`** - Get tests by subject/topic
4. **`GET /api/tests/{testId}/analytics`** - Get test performance analytics
5. **`GET /api/user/test-history`** - Get user's complete test history

### **🏗️ Database Schema Requirements:**

#### **Tests Table:**

```sql
CREATE TABLE tests (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  short_description VARCHAR(500),
  course_id VARCHAR(36) NOT NULL,
  subject_id VARCHAR(36) NOT NULL,
  topic_id VARCHAR(36),
  instructor_id VARCHAR(36) NOT NULL,
  test_type ENUM('topic_test', 'subject_test', 'mock_test') DEFAULT 'topic_test',
  difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
  total_questions INT NOT NULL,
  total_marks INT NOT NULL,
  passing_marks INT NOT NULL,
  duration INT NOT NULL, -- in seconds
  negative_marking BOOLEAN DEFAULT false,
  negative_marking_ratio DECIMAL(3,2) DEFAULT 0,
  allow_retake BOOLEAN DEFAULT true,
  max_attempts INT DEFAULT 3,
  show_results_immediately BOOLEAN DEFAULT true,
  show_correct_answers BOOLEAN DEFAULT true,
  show_explanations BOOLEAN DEFAULT true,
  randomize_questions BOOLEAN DEFAULT true,
  randomize_options BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  is_published BOOLEAN DEFAULT false,
  language VARCHAR(10) DEFAULT 'hindi',
  tags JSON,
  instructions JSON,
  order_index INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id),
  FOREIGN KEY (topic_id) REFERENCES topics(id),
  FOREIGN KEY (instructor_id) REFERENCES instructors(id)
);
```

#### **Questions Table:**

```sql
CREATE TABLE questions (
  id VARCHAR(36) PRIMARY KEY,
  test_id VARCHAR(36) NOT NULL,
  question_number INT NOT NULL,
  question_type ENUM('mcq', 'true_false', 'fill_blank') DEFAULT 'mcq',
  question TEXT NOT NULL,
  options JSON, -- For MCQ and True/False
  correct_answer VARCHAR(500) NOT NULL,
  acceptable_answers JSON, -- For fill_blank questions
  explanation TEXT,
  marks INT DEFAULT 1,
  difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
  tags JSON,
  order_index INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE
);
```

#### **Test Attempts Table:**

```sql
CREATE TABLE test_attempts (
  id VARCHAR(36) PRIMARY KEY,
  test_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  attempt_number INT NOT NULL,
  score INT DEFAULT 0,
  max_score INT NOT NULL,
  percentage DECIMAL(5,2) DEFAULT 0,
  time_taken INT DEFAULT 0, -- in seconds
  total_questions INT NOT NULL,
  correct_answers INT DEFAULT 0,
  incorrect_answers INT DEFAULT 0,
  unanswered INT DEFAULT 0,
  is_passed BOOLEAN DEFAULT false,
  is_auto_submit BOOLEAN DEFAULT false,
  submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('in_progress', 'completed', 'abandoned') DEFAULT 'completed',
  device_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (test_id) REFERENCES tests(id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  UNIQUE KEY unique_user_test_attempt (user_id, test_id, attempt_number)
);
```

#### **Test Answers Table:**

```sql
CREATE TABLE test_answers (
  id VARCHAR(36) PRIMARY KEY,
  attempt_id VARCHAR(36) NOT NULL,
  question_id VARCHAR(36) NOT NULL,
  selected_answer VARCHAR(500),
  user_answer TEXT, -- For fill_blank questions
  is_correct BOOLEAN DEFAULT false,
  marks_obtained INT DEFAULT 0,
  time_taken INT DEFAULT 0, -- in seconds
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (attempt_id) REFERENCES test_attempts(id) ON DELETE CASCADE,
  FOREIGN KEY (question_id) REFERENCES questions(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Test Engine:**

- Support multiple question types (MCQ, True/False, Fill in the Blanks)
- Implement timer functionality with auto-submit
- Randomize questions and options if configured
- Calculate scores with negative marking support

#### **Performance Analytics:**

- Track detailed question-wise performance
- Generate topic-wise and subject-wise analysis
- Compare user performance with peers
- Provide personalized recommendations

#### **Test Management:**

- Support test retakes with attempt limits
- Store complete answer history for analysis
- Generate detailed result reports
- Track improvement over time

#### **Performance Optimization:**

- Index on user_id, test_id, and attempt_number fields
- Use Redis for caching test questions during attempts
- Implement efficient scoring algorithms
- Optimize database queries with proper indexing

#### **Security Considerations:**

- Validate user enrollment before test access
- Implement anti-cheating measures
- Secure test questions and answers
- Track suspicious activity patterns

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Test Engine**: Complete test interface with timer and question navigation
- **Question Types**: Support for MCQ, True/False, and Fill in the Blanks
- **Result Analysis**: Detailed score analysis and explanations
- **Performance Tracking**: Historical performance and improvement tracking
- **Retake Functionality**: Support for multiple attempts with limits

### **🚀 Implementation Priority:**

1. **HIGH**: Test details and question delivery (core functionality)
2. **HIGH**: Test submission and result calculation (assessment)
3. **MEDIUM**: Test listing and filtering (content discovery)
4. **MEDIUM**: Performance analytics (insights and improvement)
5. **LOW**: Advanced analytics and peer comparison (optimization)

---

## 📅 **PHASE 7: LIVE CLASSES MODULE APIs (Step 6 - Requirement.md)**

> **Implementation Priority:** MEDIUM - Optional feature for enhanced learning experience
> **Dependencies:** Phase 1 (Authentication) and Phase 3 (Course Hierarchy) must be completed first
> **Frontend Status:** ✅ READY - Live classes UI components and calendar integration implemented

### **Overview:**

The Live Classes Module provides real-time interactive learning sessions with scheduled calendar, video conferencing integration, and push notification reminders. This implements the live learning experience for students to interact with instructors in real-time.

**Key Features:**

- Scheduled class calendar with upcoming sessions
- Zoom/Jitsi integration for video conferencing
- Push notification reminders before classes
- Class registration and attendance tracking
- Recording availability for missed sessions
- Interactive features like chat and Q&A
- Integration with course topics and subjects

---

### **7.1 Get Live Classes Schedule**

**Endpoint:** `GET /api/live-classes`

**Purpose:** Get scheduled live classes with filtering and calendar view

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
courseId: string (optional) - Filter by specific course
subjectId: string (optional) - Filter by specific subject
status: string (optional) - Filter by status: upcoming, live, completed, cancelled
startDate: string (optional) - Filter from date (ISO format)
endDate: string (optional) - Filter to date (ISO format)
page: number (default: 1) - Page number for pagination
limit: number (default: 10) - Number of classes per page
includeRegistration: boolean (default: true) - Include user's registration status
```

**Response:**

```json
{
  "success": true,
  "message": "Live classes retrieved successfully",
  "data": {
    "liveClasses": [
      {
        "id": "live_class_123",
        "title": "Indian Polity - Constitutional Framework",
        "description": "Comprehensive discussion on the constitutional framework of India, covering fundamental rights, directive principles, and the structure of government institutions.",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_polity",
        "subjectName": "Indian Polity",
        "topicId": "topic_constitutional_framework",
        "topicName": "Constitutional Framework",
        "instructorId": "instructor_rajesh_kumar",
        "instructorName": "Dr. Rajesh Kumar",
        "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
        "instructorBio": "15+ years experience in BPSC coaching",
        "scheduledAt": "2024-01-20T10:00:00Z",
        "duration": 90,
        "timeZone": "Asia/Kolkata",
        "status": "upcoming",
        "meetingDetails": {
          "platform": "zoom",
          "meetingUrl": "https://zoom.us/j/123456789",
          "meetingId": "123-456-789",
          "password": "polity123",
          "dialInNumbers": ["+91-80-71279440", "+91-80-47102000"],
          "webinarId": "web_123456"
        },
        "registration": {
          "isRequired": true,
          "isRegistered": true,
          "registeredAt": "2024-01-15T14:30:00Z",
          "registrationCount": 450,
          "maxParticipants": 500,
          "waitlistCount": 25
        },
        "features": {
          "hasRecording": true,
          "hasChat": true,
          "hasQnA": true,
          "hasPolls": true,
          "hasBreakoutRooms": false,
          "hasScreenShare": true
        },
        "reminders": {
          "email": true,
          "push": true,
          "sms": false,
          "reminderTimes": [1440, 60, 15]
        },
        "tags": ["polity", "constitution", "fundamental-rights"],
        "language": "hindi",
        "isRecorded": true,
        "recordingUrl": null,
        "recordingAvailableAt": null,
        "createdAt": "2024-01-10T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      },
      {
        "id": "live_class_124",
        "title": "Ancient History - Indus Valley Civilization",
        "description": "Detailed study of Indus Valley Civilization covering urban planning, trade, culture, and decline theories.",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_history",
        "subjectName": "History",
        "topicId": "topic_ancient_history",
        "topicName": "Ancient History",
        "instructorId": "instructor_priya_sharma",
        "instructorName": "Dr. Priya Sharma",
        "instructorImage": "https://cdn.example.com/instructors/priya-sharma.jpg",
        "instructorBio": "Ph.D. in Ancient History, 12+ years teaching experience",
        "scheduledAt": "2024-01-18T15:30:00Z",
        "duration": 75,
        "timeZone": "Asia/Kolkata",
        "status": "completed",
        "meetingDetails": {
          "platform": "jitsi",
          "meetingUrl": "https://meet.jit.si/UtkrishataAncientHistory124",
          "meetingId": "UtkrishataAncientHistory124",
          "password": null
        },
        "registration": {
          "isRequired": true,
          "isRegistered": true,
          "registeredAt": "2024-01-15T14:30:00Z",
          "registrationCount": 380,
          "maxParticipants": 400,
          "waitlistCount": 0
        },
        "features": {
          "hasRecording": true,
          "hasChat": true,
          "hasQnA": true,
          "hasPolls": false,
          "hasBreakoutRooms": false,
          "hasScreenShare": true
        },
        "reminders": {
          "email": true,
          "push": true,
          "sms": false,
          "reminderTimes": [1440, 60, 15]
        },
        "tags": ["history", "ancient", "indus-valley"],
        "language": "hindi",
        "isRecorded": true,
        "recordingUrl": "https://cdn.example.com/recordings/live-class-124.mp4",
        "recordingAvailableAt": "2024-01-18T17:00:00Z",
        "attendanceCount": 342,
        "averageAttendanceTime": 68,
        "createdAt": "2024-01-10T00:00:00Z",
        "updatedAt": "2024-01-18T17:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "summary": {
      "totalClasses": 25,
      "upcomingClasses": 8,
      "liveClasses": 1,
      "completedClasses": 15,
      "registeredClasses": 20,
      "attendedClasses": 12
    }
  }
}
```

**Business Logic:**

- Filter classes by course, subject, and status
- Include user's registration and attendance status
- Show meeting details only for registered users
- Calculate time until class starts
- Track registration counts and waitlists

---

### **7.2 Register for Live Class**

**Endpoint:** `POST /api/live-classes/{classId}/register`

**Purpose:** Register user for a specific live class

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "reminderPreferences": {
    "email": true,
    "push": true,
    "sms": false,
    "customReminderTimes": [1440, 60, 15]
  },
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android",
    "timezone": "Asia/Kolkata"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Successfully registered for live class",
  "data": {
    "registration": {
      "id": "registration_456",
      "classId": "live_class_123",
      "userId": "user_123",
      "registeredAt": "2024-01-15T16:00:00Z",
      "status": "confirmed",
      "reminderPreferences": {
        "email": true,
        "push": true,
        "sms": false,
        "customReminderTimes": [1440, 60, 15]
      },
      "meetingDetails": {
        "platform": "zoom",
        "meetingUrl": "https://zoom.us/j/123456789",
        "meetingId": "123-456-789",
        "password": "polity123",
        "dialInNumbers": ["+91-80-71279440", "+91-80-47102000"]
      }
    },
    "class": {
      "id": "live_class_123",
      "title": "Indian Polity - Constitutional Framework",
      "scheduledAt": "2024-01-20T10:00:00Z",
      "duration": 90,
      "instructorName": "Dr. Rajesh Kumar",
      "registrationCount": 451,
      "maxParticipants": 500
    },
    "reminders": [
      {
        "type": "email",
        "scheduledAt": "2024-01-19T10:00:00Z",
        "message": "Your live class starts in 24 hours"
      },
      {
        "type": "push",
        "scheduledAt": "2024-01-20T09:00:00Z",
        "message": "Your live class starts in 1 hour"
      },
      {
        "type": "push",
        "scheduledAt": "2024-01-20T09:45:00Z",
        "message": "Your live class starts in 15 minutes"
      }
    ]
  }
}
```

**Business Logic:**

- Check if user is enrolled in the course
- Validate class capacity and waitlist management
- Generate meeting access details
- Schedule reminder notifications
- Track registration analytics

### **7.3 Track Live Class Attendance**

**Endpoint:** `POST /api/live-classes/{classId}/attendance`

**Purpose:** Track user attendance and engagement during live class

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "action": "join",
  "timestamp": "2024-01-20T10:05:00Z",
  "deviceInfo": {
    "deviceId": "device_unique_id",
    "platform": "android",
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "*************"
  },
  "meetingInfo": {
    "platform": "zoom",
    "participantId": "zoom_participant_123",
    "connectionQuality": "good"
  }
}
```

**Supported Actions:**

- `join` - User joined the class
- `leave` - User left the class
- `reconnect` - User reconnected after disconnection
- `interaction` - User participated (chat, Q&A, poll)

**Response:**

```json
{
  "success": true,
  "message": "Attendance recorded successfully",
  "data": {
    "attendance": {
      "id": "attendance_789",
      "classId": "live_class_123",
      "userId": "user_123",
      "joinedAt": "2024-01-20T10:05:00Z",
      "leftAt": null,
      "duration": 0,
      "isPresent": true,
      "interactionCount": 0,
      "connectionQuality": "good",
      "deviceInfo": {
        "platform": "android",
        "deviceId": "device_unique_id"
      }
    },
    "classStats": {
      "totalParticipants": 342,
      "currentParticipants": 298,
      "peakParticipants": 356,
      "averageAttendanceTime": 68
    }
  }
}
```

**Business Logic:**

- Track join/leave times for attendance calculation
- Monitor user engagement and interactions
- Calculate attendance percentage and duration
- Generate real-time class statistics
- Support multiple device connections per user

---

### **7.4 Get Live Class Recordings**

**Endpoint:** `GET /api/live-classes/{classId}/recording`

**Purpose:** Get recording details and access for completed live classes

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeTranscript: boolean (default: false) - Include auto-generated transcript
includeChapters: boolean (default: true) - Include video chapters/timestamps
quality: string (optional) - Preferred video quality (720p, 480p, 360p)
```

**Response:**

```json
{
  "success": true,
  "message": "Recording details retrieved successfully",
  "data": {
    "recording": {
      "id": "recording_456",
      "classId": "live_class_124",
      "title": "Ancient History - Indus Valley Civilization",
      "description": "Recorded session covering IVC urban planning, trade, and culture",
      "instructorName": "Dr. Priya Sharma",
      "recordedAt": "2024-01-18T15:30:00Z",
      "duration": 4500,
      "fileSize": 1073741824,
      "fileSizeFormatted": "1.0 GB",
      "isAvailable": true,
      "availableUntil": "2024-07-18T15:30:00Z",
      "streamingUrls": {
        "720p": "https://cdn.example.com/recordings/live-class-124-720p.m3u8",
        "480p": "https://cdn.example.com/recordings/live-class-124-480p.m3u8",
        "360p": "https://cdn.example.com/recordings/live-class-124-360p.m3u8"
      },
      "downloadUrls": {
        "720p": "https://cdn.example.com/downloads/live-class-124-720p.mp4",
        "480p": "https://cdn.example.com/downloads/live-class-124-480p.mp4",
        "360p": "https://cdn.example.com/downloads/live-class-124-360p.mp4"
      },
      "thumbnailUrl": "https://cdn.example.com/thumbnails/live-class-124.jpg",
      "chapters": [
        {
          "id": "chapter_1",
          "title": "Introduction to Indus Valley Civilization",
          "startTime": 0,
          "endTime": 900,
          "description": "Overview and historical context"
        },
        {
          "id": "chapter_2",
          "title": "Urban Planning and Architecture",
          "startTime": 900,
          "endTime": 1800,
          "description": "City layout, drainage systems, and buildings"
        },
        {
          "id": "chapter_3",
          "title": "Trade and Economy",
          "startTime": 1800,
          "endTime": 2700,
          "description": "Trade routes, crafts, and economic activities"
        },
        {
          "id": "chapter_4",
          "title": "Culture and Society",
          "startTime": 2700,
          "endTime": 3600,
          "description": "Religion, art, and social structure"
        },
        {
          "id": "chapter_5",
          "title": "Decline and Legacy",
          "startTime": 3600,
          "endTime": 4500,
          "description": "Theories of decline and historical significance"
        }
      ],
      "transcript": {
        "isAvailable": true,
        "language": "hindi",
        "transcriptUrl": "https://cdn.example.com/transcripts/live-class-124.vtt",
        "searchableText": true
      },
      "stats": {
        "views": 1250,
        "downloads": 340,
        "averageWatchTime": 3200,
        "completionRate": 0.71
      },
      "canDownload": true,
      "downloadExpiry": "2024-07-18T15:30:00Z"
    },
    "userProgress": {
      "isWatched": false,
      "watchProgress": 0,
      "lastWatchedAt": null,
      "watchedDuration": 0,
      "isCompleted": false
    }
  }
}
```

**Business Logic:**

- Check user's registration for the original class
- Validate recording availability and expiry
- Provide appropriate quality options
- Track recording views and engagement
- Support chapter navigation and transcripts

---

### **7.5 Get Live Class Notifications**

**Endpoint:** `GET /api/live-classes/notifications`

**Purpose:** Get upcoming class reminders and notifications

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
type: string (optional) - Filter by type: reminder, update, cancellation
status: string (optional) - Filter by status: pending, sent, failed
timeRange: string (default: "week") - Time range: day, week, month
```

**Response:**

```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": "notification_123",
        "type": "reminder",
        "classId": "live_class_123",
        "className": "Indian Polity - Constitutional Framework",
        "scheduledAt": "2024-01-20T10:00:00Z",
        "reminderTime": "2024-01-20T09:00:00Z",
        "title": "Live Class Starting Soon",
        "message": "Your live class 'Indian Polity - Constitutional Framework' starts in 1 hour. Join now!",
        "actionUrl": "https://zoom.us/j/123456789",
        "actionText": "Join Class",
        "status": "pending",
        "channels": ["push", "email"],
        "priority": "high",
        "createdAt": "2024-01-15T16:00:00Z"
      },
      {
        "id": "notification_124",
        "type": "update",
        "classId": "live_class_125",
        "className": "Geography - Climate and Weather",
        "scheduledAt": "2024-01-22T14:00:00Z",
        "title": "Class Time Updated",
        "message": "The class 'Geography - Climate and Weather' has been rescheduled from 2:00 PM to 3:00 PM on Jan 22.",
        "actionUrl": null,
        "actionText": null,
        "status": "sent",
        "channels": ["push", "email"],
        "priority": "medium",
        "sentAt": "2024-01-19T10:00:00Z",
        "createdAt": "2024-01-19T09:45:00Z"
      },
      {
        "id": "notification_125",
        "type": "recording_available",
        "classId": "live_class_124",
        "className": "Ancient History - Indus Valley Civilization",
        "title": "Recording Available",
        "message": "The recording for 'Ancient History - Indus Valley Civilization' is now available to watch.",
        "actionUrl": "/recordings/live_class_124",
        "actionText": "Watch Recording",
        "status": "sent",
        "channels": ["push"],
        "priority": "low",
        "sentAt": "2024-01-18T17:00:00Z",
        "createdAt": "2024-01-18T17:00:00Z"
      }
    ],
    "summary": {
      "totalNotifications": 15,
      "pendingReminders": 3,
      "sentNotifications": 12,
      "failedNotifications": 0
    }
  }
}
```

**Business Logic:**

- Generate automatic reminders based on user preferences
- Send class updates and schedule changes
- Notify about recording availability
- Track notification delivery status
- Support multiple notification channels

## 📋 **LIVE CLASSES MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 6 (Live Classes Module):**

1. **`GET /api/live-classes`** - Get scheduled live classes with filtering (PRIMARY)
2. **`POST /api/live-classes/{classId}/register`** - Register for live class
3. **`POST /api/live-classes/{classId}/attendance`** - Track attendance and engagement
4. **`GET /api/live-classes/{classId}/recording`** - Get recording access and details
5. **`GET /api/live-classes/notifications`** - Get class reminders and notifications

### **🏗️ Database Schema Requirements:**

#### **Live Classes Table:**

```sql
CREATE TABLE live_classes (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  course_id VARCHAR(36) NOT NULL,
  subject_id VARCHAR(36) NOT NULL,
  topic_id VARCHAR(36),
  instructor_id VARCHAR(36) NOT NULL,
  scheduled_at TIMESTAMP NOT NULL,
  duration INT NOT NULL, -- in minutes
  time_zone VARCHAR(50) DEFAULT 'Asia/Kolkata',
  status ENUM('upcoming', 'live', 'completed', 'cancelled') DEFAULT 'upcoming',
  meeting_platform ENUM('zoom', 'jitsi', 'teams', 'meet') DEFAULT 'zoom',
  meeting_url VARCHAR(1000),
  meeting_id VARCHAR(255),
  meeting_password VARCHAR(255),
  webinar_id VARCHAR(255),
  dial_in_numbers JSON,
  max_participants INT DEFAULT 500,
  registration_required BOOLEAN DEFAULT true,
  has_recording BOOLEAN DEFAULT true,
  has_chat BOOLEAN DEFAULT true,
  has_qna BOOLEAN DEFAULT true,
  has_polls BOOLEAN DEFAULT false,
  has_breakout_rooms BOOLEAN DEFAULT false,
  has_screen_share BOOLEAN DEFAULT true,
  language VARCHAR(10) DEFAULT 'hindi',
  tags JSON,
  is_recorded BOOLEAN DEFAULT false,
  recording_url VARCHAR(1000),
  recording_available_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id),
  FOREIGN KEY (topic_id) REFERENCES topics(id),
  FOREIGN KEY (instructor_id) REFERENCES instructors(id)
);
```

#### **Live Class Registrations Table:**

```sql
CREATE TABLE live_class_registrations (
  id VARCHAR(36) PRIMARY KEY,
  class_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('confirmed', 'waitlisted', 'cancelled') DEFAULT 'confirmed',
  reminder_email BOOLEAN DEFAULT true,
  reminder_push BOOLEAN DEFAULT true,
  reminder_sms BOOLEAN DEFAULT false,
  custom_reminder_times JSON, -- [1440, 60, 15] minutes before
  device_id VARCHAR(255),
  timezone VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES live_classes(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id),
  UNIQUE KEY unique_user_class (user_id, class_id)
);
```

#### **Live Class Attendance Table:**

```sql
CREATE TABLE live_class_attendance (
  id VARCHAR(36) PRIMARY KEY,
  class_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  joined_at TIMESTAMP,
  left_at TIMESTAMP,
  duration INT DEFAULT 0, -- in seconds
  is_present BOOLEAN DEFAULT false,
  interaction_count INT DEFAULT 0,
  connection_quality ENUM('excellent', 'good', 'fair', 'poor') DEFAULT 'good',
  platform_participant_id VARCHAR(255),
  device_info JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES live_classes(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### **Live Class Notifications Table:**

```sql
CREATE TABLE live_class_notifications (
  id VARCHAR(36) PRIMARY KEY,
  class_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36),
  type ENUM('reminder', 'update', 'cancellation', 'recording_available') NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  action_url VARCHAR(1000),
  action_text VARCHAR(100),
  channels JSON, -- ["push", "email", "sms"]
  priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
  scheduled_at TIMESTAMP,
  sent_at TIMESTAMP,
  status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
  failure_reason TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (class_id) REFERENCES live_classes(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Video Conferencing Integration:**

- Support multiple platforms (Zoom, Jitsi, Teams, Google Meet)
- Generate secure meeting URLs with passwords
- Handle platform-specific features and limitations
- Implement webhook integrations for real-time updates

#### **Notification System:**

- Schedule automatic reminders based on user preferences
- Support multiple channels (push, email, SMS)
- Handle timezone conversions for global users
- Track delivery status and retry failed notifications

#### **Attendance Tracking:**

- Real-time tracking of join/leave events
- Calculate attendance percentage and engagement
- Support multiple device connections per user
- Generate attendance reports for instructors

#### **Recording Management:**

- Automatic recording initiation and processing
- Multiple quality options for streaming and download
- Chapter-based navigation with timestamps
- Auto-generated transcripts and searchable content

#### **Performance Optimization:**

- Index on class_id, user_id, and scheduled_at fields
- Use Redis for real-time attendance tracking
- Implement CDN for recording delivery
- Cache meeting details for quick access

#### **Security Considerations:**

- Secure meeting URLs with time-limited access
- Validate user enrollment before registration
- Implement rate limiting on attendance updates
- Monitor for suspicious activity patterns

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Calendar View**: Display scheduled classes with registration status
- **Registration System**: One-click registration with reminder preferences
- **Meeting Integration**: Direct links to Zoom/Jitsi meetings
- **Attendance Tracking**: Automatic tracking during live sessions
- **Recording Access**: Seamless access to recorded sessions
- **Notification Management**: Push reminders and class updates

### **🚀 Implementation Priority:**

1. **HIGH**: Live class scheduling and registration (core functionality)
2. **HIGH**: Meeting integration and attendance tracking (live experience)
3. **MEDIUM**: Recording management and access (missed class support)
4. **MEDIUM**: Notification system (engagement and reminders)
5. **LOW**: Advanced analytics and reporting (insights and optimization)

---

## 🧑‍🎓 **PHASE 8: STUDENT DASHBOARD MODULE APIs (Step 7 - Requirement.md)**

> **Implementation Priority:** HIGH - Core user experience and progress tracking
> **Dependencies:** All previous phases (Authentication, Courses, Videos, PDFs, Tests, Live Classes)
> **Frontend Status:** ✅ READY - Complete dashboard with all sections and analytics implemented

### **Overview:**

The Student Dashboard Module provides a comprehensive overview of the student's learning journey with purchased/free courses, class completion tracking, test score history, bookmarks, notifications, and doubt corner. This implements the central hub for student progress monitoring and engagement.

**Key Features:**

- View purchased and free courses with progress tracking
- Track class completion across all subjects and topics
- Attempted tests with detailed score history and analytics
- Bookmark section for saved videos and notes
- Notifications section for updates and reminders
- Doubt corner with chat-based or comment system
- Learning streaks and achievement tracking
- Performance analytics and insights

---

### **8.1 Get Student Dashboard Overview**

**Endpoint:** `GET /api/student/dashboard`

**Purpose:** Get comprehensive dashboard data with user stats, courses, and activities

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeStats: boolean (default: true) - Include user statistics
includeCourses: boolean (default: true) - Include enrolled courses
includeActivities: boolean (default: true) - Include recent activities
includeNotifications: boolean (default: true) - Include notifications
timeRange: string (default: "week") - Time range for activities: day, week, month
```

**Response:**

```json
{
  "success": true,
  "message": "Dashboard data retrieved successfully",
  "data": {
    "userProfile": {
      "id": "user_123",
      "name": "Rajesh Kumar",
      "email": "<EMAIL>",
      "profileImage": "https://cdn.example.com/profiles/user-123.jpg",
      "memberSince": "2024-01-01T00:00:00Z",
      "subscriptionType": "premium",
      "subscriptionExpiry": "2024-12-31T23:59:59Z"
    },
    "userStats": {
      "totalCoursesEnrolled": 8,
      "totalCoursesCompleted": 3,
      "totalVideosWatched": 145,
      "totalTestsAttempted": 32,
      "totalTestsCompleted": 28,
      "averageTestScore": 82.5,
      "totalStudyHours": 156,
      "currentStreak": 7,
      "longestStreak": 15,
      "overallProgress": 0.68,
      "totalBookmarks": 23,
      "totalDownloads": 12,
      "rank": 15,
      "percentile": 85
    },
    "enrolledCourses": [
      {
        "id": "course_bpsc_70th_prelims",
        "title": "BPSC 70th Prelims Complete Course",
        "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
        "instructorName": "Dr. Rajesh Kumar",
        "enrolledAt": "2024-01-01T00:00:00Z",
        "lastAccessedAt": "2024-01-15T14:30:00Z",
        "progress": {
          "completionPercentage": 75.5,
          "videosWatched": 45,
          "totalVideos": 60,
          "testsCompleted": 8,
          "totalTests": 12,
          "timeSpent": 4800
        },
        "nextClass": {
          "id": "video_modern_history_1",
          "title": "Modern History - Freedom Struggle",
          "type": "video",
          "duration": 1800
        },
        "isCompleted": false,
        "certificateEarned": false
      },
      {
        "id": "course_ssc_cgl_tier1",
        "title": "SSC CGL Tier 1 Complete Course",
        "thumbnailUrl": "https://cdn.example.com/courses/ssc-cgl-tier1.jpg",
        "instructorName": "Amit Singh",
        "enrolledAt": "2024-01-05T00:00:00Z",
        "lastAccessedAt": "2024-01-14T16:00:00Z",
        "progress": {
          "completionPercentage": 100,
          "videosWatched": 80,
          "totalVideos": 80,
          "testsCompleted": 15,
          "totalTests": 15,
          "timeSpent": 7200
        },
        "nextClass": null,
        "isCompleted": true,
        "certificateEarned": true,
        "certificateUrl": "https://cdn.example.com/certificates/user-123-ssc-cgl.pdf"
      }
    ],
    "recentActivities": [
      {
        "id": "activity_456",
        "type": "video_completed",
        "title": "Completed: Ancient History - Mauryan Empire",
        "description": "Watched complete video on Mauryan administration",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "timestamp": "2024-01-15T14:30:00Z",
        "metadata": {
          "videoId": "video_mauryan_empire",
          "duration": 1800,
          "watchTime": 1750
        }
      },
      {
        "id": "activity_457",
        "type": "test_completed",
        "title": "Test Completed: Ancient History Quiz",
        "description": "Scored 88/100 in Ancient History test",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "timestamp": "2024-01-15T12:00:00Z",
        "metadata": {
          "testId": "test_ancient_history_mcq",
          "score": 88,
          "maxScore": 100,
          "timeTaken": 1200
        }
      },
      {
        "id": "activity_458",
        "type": "bookmark_added",
        "title": "Bookmarked: Constitutional Framework",
        "description": "Added bookmark in Indian Polity video",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "timestamp": "2024-01-15T10:15:00Z",
        "metadata": {
          "contentId": "video_constitutional_framework",
          "contentType": "video",
          "position": 450
        }
      }
    ],
    "learningStreak": {
      "currentStreak": 7,
      "longestStreak": 15,
      "streakStartDate": "2024-01-09T00:00:00Z",
      "lastActivityDate": "2024-01-15T14:30:00Z",
      "streakGoal": 30,
      "streakProgress": 0.23,
      "isActiveToday": true,
      "consecutiveDays": [
        "2024-01-09",
        "2024-01-10",
        "2024-01-11",
        "2024-01-12",
        "2024-01-13",
        "2024-01-14",
        "2024-01-15"
      ]
    },
    "upcomingClasses": [
      {
        "id": "live_class_123",
        "title": "Indian Polity - Constitutional Framework",
        "type": "live_class",
        "scheduledAt": "2024-01-20T10:00:00Z",
        "duration": 90,
        "instructorName": "Dr. Rajesh Kumar",
        "isRegistered": true
      }
    ],
    "notifications": {
      "unreadCount": 3,
      "recentNotifications": [
        {
          "id": "notification_789",
          "title": "New Video Added",
          "message": "Modern History - Freedom Struggle video is now available",
          "type": "content_update",
          "isRead": false,
          "createdAt": "2024-01-15T16:00:00Z"
        }
      ]
    }
  }
}
```

**Business Logic:**

- Calculate real-time user statistics and progress
- Include only enrolled courses with progress data
- Show recent activities across all learning modules
- Track learning streaks and daily activity
- Include upcoming live classes and notifications

---

### **8.2 Get Course Progress Details**

**Endpoint:** `GET /api/student/courses/{courseId}/progress`

**Purpose:** Get detailed progress tracking for a specific course

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
includeSubjects: boolean (default: true) - Include subject-wise progress
includeTopics: boolean (default: false) - Include topic-wise breakdown
includeContent: boolean (default: false) - Include content-level details
```

**Response:**

```json
{
  "success": true,
  "message": "Course progress retrieved successfully",
  "data": {
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "enrolledAt": "2024-01-01T00:00:00Z",
      "lastAccessedAt": "2024-01-15T14:30:00Z"
    },
    "overallProgress": {
      "completionPercentage": 75.5,
      "videosWatched": 45,
      "totalVideos": 60,
      "videosProgress": 0.75,
      "pdfsViewed": 18,
      "totalPDFs": 25,
      "pdfsProgress": 0.72,
      "testsCompleted": 8,
      "totalTests": 12,
      "testsProgress": 0.67,
      "liveClassesAttended": 3,
      "totalLiveClasses": 5,
      "liveClassesProgress": 0.6,
      "totalTimeSpent": 14400,
      "averageTestScore": 85.2,
      "certificateEligible": false,
      "certificateRequirement": 0.8
    },
    "subjectProgress": [
      {
        "subjectId": "subject_history",
        "subjectName": "History",
        "completionPercentage": 85.0,
        "videosWatched": 20,
        "totalVideos": 25,
        "pdfsViewed": 8,
        "totalPDFs": 10,
        "testsCompleted": 4,
        "totalTests": 5,
        "averageTestScore": 88.5,
        "timeSpent": 7200,
        "lastAccessedAt": "2024-01-15T14:30:00Z",
        "isCompleted": false,
        "topicProgress": [
          {
            "topicId": "topic_ancient_history",
            "topicName": "Ancient History",
            "completionPercentage": 100,
            "videosWatched": 8,
            "totalVideos": 8,
            "testsCompleted": 2,
            "totalTests": 2,
            "averageTestScore": 92.0,
            "isCompleted": true
          },
          {
            "topicId": "topic_medieval_history",
            "topicName": "Medieval History",
            "completionPercentage": 80,
            "videosWatched": 6,
            "totalVideos": 8,
            "testsCompleted": 1,
            "totalTests": 2,
            "averageTestScore": 85.0,
            "isCompleted": false
          }
        ]
      },
      {
        "subjectId": "subject_polity",
        "subjectName": "Indian Polity",
        "completionPercentage": 70.0,
        "videosWatched": 14,
        "totalVideos": 20,
        "pdfsViewed": 5,
        "totalPDFs": 8,
        "testsCompleted": 2,
        "totalTests": 4,
        "averageTestScore": 82.0,
        "timeSpent": 4800,
        "lastAccessedAt": "2024-01-14T16:00:00Z",
        "isCompleted": false
      }
    ],
    "achievements": [
      {
        "id": "achievement_first_test",
        "title": "First Test Completed",
        "description": "Completed your first test with 85% score",
        "iconUrl": "https://cdn.example.com/achievements/first-test.png",
        "earnedAt": "2024-01-02T15:00:00Z",
        "points": 50
      },
      {
        "id": "achievement_week_streak",
        "title": "Week Warrior",
        "description": "Maintained 7-day learning streak",
        "iconUrl": "https://cdn.example.com/achievements/week-streak.png",
        "earnedAt": "2024-01-15T00:00:00Z",
        "points": 100
      }
    ],
    "nextRecommendations": [
      {
        "contentId": "video_modern_history_1",
        "contentType": "video",
        "title": "Modern History - Freedom Struggle",
        "subjectName": "History",
        "estimatedTime": 30,
        "priority": "high"
      },
      {
        "contentId": "test_polity_fundamentals",
        "contentType": "test",
        "title": "Indian Polity Fundamentals Test",
        "subjectName": "Indian Polity",
        "estimatedTime": 45,
        "priority": "medium"
      }
    ]
  }
}
```

**Business Logic:**

- Calculate progress across all content types
- Track subject and topic-wise completion
- Award achievements for milestones
- Provide personalized recommendations
- Determine certificate eligibility

### **8.3 Get Student Bookmarks**

**Endpoint:** `GET /api/student/bookmarks`

**Purpose:** Get all user bookmarks across videos and PDFs

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
type: string (optional) - Filter by type: video, pdf
courseId: string (optional) - Filter by specific course
subjectId: string (optional) - Filter by specific subject
page: number (default: 1) - Page number
limit: number (default: 20) - Items per page
sortBy: string (default: "createdAt") - Sort by: createdAt, title, course
sortOrder: string (default: "desc") - Sort order: asc, desc
```

**Response:**

```json
{
  "success": true,
  "message": "Bookmarks retrieved successfully",
  "data": {
    "bookmarks": [
      {
        "id": "bookmark_video_123",
        "type": "video",
        "contentId": "video_constitutional_framework",
        "contentTitle": "Indian Polity - Constitutional Framework",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_polity",
        "subjectName": "Indian Polity",
        "position": 450,
        "title": "Fundamental Rights Discussion",
        "description": "Important section on fundamental rights and their limitations",
        "thumbnailUrl": "https://cdn.example.com/thumbnails/constitutional-framework.jpg",
        "duration": 1800,
        "createdAt": "2024-01-15T10:15:00Z"
      },
      {
        "id": "bookmark_pdf_124",
        "type": "pdf",
        "contentId": "pdf_ancient_history_notes",
        "contentTitle": "Ancient History Complete Notes",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_history",
        "subjectName": "History",
        "pageNumber": 45,
        "title": "Mauryan Administration",
        "description": "Detailed notes on Mauryan administrative system",
        "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-notes.jpg",
        "pageCount": 120,
        "createdAt": "2024-01-14T18:45:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 23,
      "itemsPerPage": 20
    },
    "summary": {
      "totalBookmarks": 23,
      "videoBookmarks": 15,
      "pdfBookmarks": 8,
      "recentBookmarks": 5
    }
  }
}
```

**Business Logic:**

- Group bookmarks by content type (video/PDF)
- Include course and subject information
- Support filtering and sorting options
- Show bookmark position/page for navigation
- Track bookmark creation and usage

---

### **8.4 Get Test Score History**

**Endpoint:** `GET /api/student/test-history`

**Purpose:** Get comprehensive test attempt history with score analysis

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
courseId: string (optional) - Filter by specific course
subjectId: string (optional) - Filter by specific subject
testType: string (optional) - Filter by test type
timeRange: string (default: "all") - Time range: week, month, quarter, all
includeAnalytics: boolean (default: true) - Include performance analytics
page: number (default: 1) - Page number
limit: number (default: 20) - Items per page
```

**Response:**

```json
{
  "success": true,
  "message": "Test history retrieved successfully",
  "data": {
    "testHistory": [
      {
        "id": "attempt_124",
        "testId": "test_ancient_history_mcq",
        "testTitle": "Ancient History MCQ Test",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_history",
        "subjectName": "History",
        "topicId": "topic_ancient_history",
        "topicName": "Ancient History",
        "testType": "topic_test",
        "attemptNumber": 2,
        "score": 46,
        "maxScore": 50,
        "percentage": 92,
        "grade": "A+",
        "timeTaken": 1200,
        "totalQuestions": 25,
        "correctAnswers": 23,
        "incorrectAnswers": 2,
        "unanswered": 0,
        "isPassed": true,
        "rank": 15,
        "percentile": 85,
        "improvement": 8,
        "submittedAt": "2024-01-15T16:00:00Z",
        "weakTopics": [
          {
            "topicName": "Vedic Period",
            "questionsAttempted": 3,
            "correctAnswers": 1,
            "successRate": 33.3
          }
        ],
        "strongTopics": [
          {
            "topicName": "Indus Valley Civilization",
            "questionsAttempted": 8,
            "correctAnswers": 8,
            "successRate": 100
          }
        ]
      }
    ],
    "analytics": {
      "overallStats": {
        "totalAttempts": 45,
        "totalTests": 28,
        "averageScore": 82.5,
        "averagePercentage": 82.5,
        "bestScore": 98,
        "worstScore": 45,
        "improvementTrend": "positive",
        "totalTimeSpent": 54000
      },
      "subjectWisePerformance": [
        {
          "subjectId": "subject_history",
          "subjectName": "History",
          "totalAttempts": 15,
          "averageScore": 85.2,
          "bestScore": 96,
          "passRate": 93.3,
          "improvementTrend": "positive"
        },
        {
          "subjectId": "subject_polity",
          "subjectName": "Indian Polity",
          "totalAttempts": 12,
          "averageScore": 78.5,
          "bestScore": 92,
          "passRate": 83.3,
          "improvementTrend": "stable"
        }
      ],
      "monthlyProgress": [
        {
          "month": "2024-01",
          "testsAttempted": 12,
          "averageScore": 84.2,
          "improvement": 5.8
        }
      ],
      "recommendations": [
        {
          "type": "weak_subject",
          "title": "Focus on Geography",
          "description": "Your Geography scores are below average. Practice more questions.",
          "priority": "high",
          "resourceId": "subject_geography",
          "resourceType": "subject"
        }
      ]
    },
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 45,
      "itemsPerPage": 20
    }
  }
}
```

**Business Logic:**

- Track all test attempts with detailed analysis
- Calculate improvement trends and patterns
- Identify weak and strong topics
- Provide subject-wise performance breakdown
- Generate personalized recommendations

---

### **8.5 Get Student Notifications**

**Endpoint:** `GET /api/student/notifications`

**Purpose:** Get all notifications for the student with filtering options

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
type: string (optional) - Filter by type: content_update, reminder, achievement, announcement
status: string (optional) - Filter by status: read, unread
page: number (default: 1) - Page number
limit: number (default: 20) - Items per page
markAsRead: boolean (default: false) - Mark notifications as read when fetched
```

**Response:**

```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": "notification_789",
        "type": "content_update",
        "title": "New Video Added",
        "message": "Modern History - Freedom Struggle video is now available in your BPSC course",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "actionUrl": "/videos/video_modern_history_1",
        "actionText": "Watch Now",
        "iconUrl": "https://cdn.example.com/icons/video-added.png",
        "priority": "medium",
        "isRead": false,
        "createdAt": "2024-01-15T16:00:00Z"
      },
      {
        "id": "notification_790",
        "type": "achievement",
        "title": "Achievement Unlocked!",
        "message": "Congratulations! You've maintained a 7-day learning streak",
        "actionUrl": "/profile/achievements",
        "actionText": "View Achievements",
        "iconUrl": "https://cdn.example.com/icons/achievement.png",
        "priority": "high",
        "isRead": false,
        "metadata": {
          "achievementId": "achievement_week_streak",
          "points": 100
        },
        "createdAt": "2024-01-15T00:00:00Z"
      },
      {
        "id": "notification_791",
        "type": "reminder",
        "title": "Live Class Reminder",
        "message": "Your live class 'Indian Polity - Constitutional Framework' starts in 1 hour",
        "courseId": "course_bpsc_70th_prelims",
        "actionUrl": "/live-classes/live_class_123",
        "actionText": "Join Class",
        "iconUrl": "https://cdn.example.com/icons/live-class.png",
        "priority": "high",
        "isRead": true,
        "readAt": "2024-01-15T09:30:00Z",
        "createdAt": "2024-01-15T09:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 25,
      "itemsPerPage": 20
    },
    "summary": {
      "totalNotifications": 25,
      "unreadCount": 8,
      "todayCount": 3,
      "weekCount": 12
    }
  }
}
```

**Business Logic:**

- Support multiple notification types and priorities
- Track read/unread status with timestamps
- Include actionable links for relevant content
- Filter by type, status, and time range
- Provide notification summary statistics

### **8.6 Doubt Corner (Chat/Comments System)**

**Endpoint:** `GET /api/student/doubts`

**Purpose:** Get student's doubts and questions with responses

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
courseId: string (optional) - Filter by specific course
subjectId: string (optional) - Filter by specific subject
status: string (optional) - Filter by status: open, answered, closed
page: number (default: 1) - Page number
limit: number (default: 10) - Items per page
```

**Response:**

```json
{
  "success": true,
  "message": "Doubts retrieved successfully",
  "data": {
    "doubts": [
      {
        "id": "doubt_456",
        "title": "Confusion about Fundamental Rights vs Directive Principles",
        "question": "Can you explain the key differences between Fundamental Rights and Directive Principles of State Policy?",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "subjectId": "subject_polity",
        "subjectName": "Indian Polity",
        "contentId": "video_constitutional_framework",
        "contentType": "video",
        "contentTitle": "Constitutional Framework",
        "status": "answered",
        "priority": "medium",
        "tags": ["fundamental-rights", "directive-principles", "constitution"],
        "createdAt": "2024-01-14T15:30:00Z",
        "updatedAt": "2024-01-15T10:00:00Z",
        "responses": [
          {
            "id": "response_789",
            "responderId": "instructor_rajesh_kumar",
            "responderName": "Dr. Rajesh Kumar",
            "responderType": "instructor",
            "responderImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
            "response": "Great question! Fundamental Rights are justiciable (enforceable by courts) while Directive Principles are non-justiciable guidelines for governance. Fundamental Rights protect individual liberties, while Directive Principles guide state policy for social welfare.",
            "isHelpful": true,
            "helpfulCount": 15,
            "createdAt": "2024-01-15T10:00:00Z"
          }
        ],
        "isResolved": true,
        "resolvedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10
    },
    "summary": {
      "totalDoubts": 25,
      "openDoubts": 8,
      "answeredDoubts": 15,
      "resolvedDoubts": 12
    }
  }
}
```

---

**Endpoint:** `POST /api/student/doubts`

**Purpose:** Submit a new doubt or question

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "title": "Question about Mauryan Administration",
  "question": "What were the key features of Mauryan administrative system?",
  "courseId": "course_bpsc_70th_prelims",
  "subjectId": "subject_history",
  "contentId": "video_mauryan_empire",
  "contentType": "video",
  "tags": ["mauryan", "administration", "ancient-history"],
  "priority": "medium"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Doubt submitted successfully",
  "data": {
    "doubt": {
      "id": "doubt_457",
      "title": "Question about Mauryan Administration",
      "question": "What were the key features of Mauryan administrative system?",
      "courseId": "course_bpsc_70th_prelims",
      "subjectId": "subject_history",
      "contentId": "video_mauryan_empire",
      "status": "open",
      "priority": "medium",
      "createdAt": "2024-01-15T16:30:00Z"
    }
  }
}
```

---

## 📋 **STUDENT DASHBOARD MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 7 (Student Dashboard Module):**

1. **`GET /api/student/dashboard`** - Get comprehensive dashboard overview (PRIMARY)
2. **`GET /api/student/courses/{courseId}/progress`** - Get detailed course progress
3. **`GET /api/student/bookmarks`** - Get all user bookmarks across content
4. **`GET /api/student/test-history`** - Get test attempt history with analytics
5. **`GET /api/student/notifications`** - Get student notifications
6. **`GET /api/student/doubts`** - Get doubt corner questions and responses
7. **`POST /api/student/doubts`** - Submit new doubt or question

### **🏗️ Database Schema Requirements:**

#### **User Activities Table:**

```sql
CREATE TABLE user_activities (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  activity_type ENUM('video_completed', 'test_completed', 'bookmark_added', 'course_enrolled', 'achievement_earned') NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  course_id VARCHAR(36),
  content_id VARCHAR(36),
  content_type ENUM('video', 'pdf', 'test', 'live_class'),
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (course_id) REFERENCES courses(id)
);
```

#### **User Achievements Table:**

```sql
CREATE TABLE user_achievements (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  achievement_id VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  icon_url VARCHAR(500),
  points INT DEFAULT 0,
  earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  UNIQUE KEY unique_user_achievement (user_id, achievement_id)
);
```

#### **Student Doubts Table:**

```sql
CREATE TABLE student_doubts (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  title VARCHAR(255) NOT NULL,
  question TEXT NOT NULL,
  course_id VARCHAR(36) NOT NULL,
  subject_id VARCHAR(36),
  content_id VARCHAR(36),
  content_type ENUM('video', 'pdf', 'test', 'live_class'),
  status ENUM('open', 'answered', 'closed') DEFAULT 'open',
  priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
  tags JSON,
  is_resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id)
);
```

#### **Doubt Responses Table:**

```sql
CREATE TABLE doubt_responses (
  id VARCHAR(36) PRIMARY KEY,
  doubt_id VARCHAR(36) NOT NULL,
  responder_id VARCHAR(36) NOT NULL,
  responder_type ENUM('instructor', 'admin', 'student') DEFAULT 'instructor',
  response TEXT NOT NULL,
  is_helpful BOOLEAN DEFAULT false,
  helpful_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (doubt_id) REFERENCES student_doubts(id) ON DELETE CASCADE,
  FOREIGN KEY (responder_id) REFERENCES users(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Dashboard Analytics:**

- Real-time calculation of user statistics and progress
- Aggregation of data from multiple modules (videos, PDFs, tests, live classes)
- Learning streak tracking with daily activity monitoring
- Achievement system with milestone-based rewards

#### **Progress Tracking:**

- Course-level progress with subject and topic breakdown
- Content completion tracking across all types
- Certificate eligibility calculation based on completion criteria
- Personalized recommendations based on learning patterns

#### **Bookmark Management:**

- Unified bookmark system for videos and PDFs
- Position/page-based navigation for quick access
- Course and subject-based organization
- Search and filter capabilities

#### **Performance Optimization:**

- Index on user_id, course_id, and timestamp fields
- Use Redis for caching dashboard data
- Implement efficient aggregation queries
- Optimize database queries with proper indexing

#### **Security Considerations:**

- Validate user access to courses and content
- Implement rate limiting on doubt submissions
- Secure notification delivery
- Track user activity for analytics

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Dashboard Overview**: Complete dashboard with stats, courses, and activities
- **Progress Tracking**: Detailed course progress with subject breakdown
- **Bookmark Management**: Unified bookmark system across content types
- **Test Analytics**: Comprehensive test history with performance insights
- **Notification Center**: Real-time notifications with action buttons
- **Doubt Corner**: Chat-based doubt resolution system

### **🚀 Implementation Priority:**

1. **HIGH**: Dashboard overview and course progress (core user experience)
2. **HIGH**: Bookmarks and test history (essential features)
3. **MEDIUM**: Notifications and doubt corner (engagement features)
4. **MEDIUM**: Achievement system (gamification)
5. **LOW**: Advanced analytics and insights (optimization)

---

## 🔔 **PHASE 9: NOTIFICATIONS MODULE APIs (Step 9 - Requirement.md)**

> **Implementation Priority:** HIGH - Critical for user engagement and retention
> **Dependencies:** All previous phases for content-based notifications
> **Frontend Status:** ✅ READY - Firebase integration and notification UI components implemented

### **Overview:**

The Notifications Module provides comprehensive Firebase push notification system for user engagement with new class uploads, live class reminders, test reminders, and motivational content. This implements the core communication channel between the platform and students.

**Key Features:**

- Firebase Push Notifications for real-time delivery
- Multiple notification types (new class, live class, test reminders, motivational quotes)
- Exam/course-based grouping and targeting
- User preference management for notification types
- Delivery tracking and analytics
- Multi-channel support (push, email, SMS)
- Scheduled and triggered notifications

---

### **9.1 Send Push Notification**

**Endpoint:** `POST /api/notifications/send`

**Purpose:** Send push notification to specific users or groups

**Headers:**

```
Authorization: Bearer <admin_access_token>
```

**Request Body:**

```json
{
  "type": "new_class_upload",
  "title": "New Video Added: Modern History",
  "message": "Modern History - Freedom Struggle video is now available in your BPSC course",
  "recipients": {
    "type": "course_enrolled",
    "courseId": "course_bpsc_70th_prelims",
    "examId": "bpsc",
    "userIds": []
  },
  "data": {
    "contentId": "video_modern_history_1",
    "contentType": "video",
    "courseId": "course_bpsc_70th_prelims",
    "subjectId": "subject_history",
    "actionUrl": "/videos/video_modern_history_1",
    "actionText": "Watch Now"
  },
  "scheduling": {
    "sendImmediately": true,
    "scheduledAt": null,
    "timezone": "Asia/Kolkata"
  },
  "channels": ["push", "email"],
  "priority": "high",
  "imageUrl": "https://cdn.example.com/notifications/modern-history.jpg",
  "sound": "default",
  "badge": 1
}
```

**Response:**

```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "notification": {
      "id": "notification_456",
      "type": "new_class_upload",
      "title": "New Video Added: Modern History",
      "message": "Modern History - Freedom Struggle video is now available in your BPSC course",
      "totalRecipients": 1250,
      "channels": ["push", "email"],
      "priority": "high",
      "status": "sent",
      "createdAt": "2024-01-15T16:00:00Z",
      "sentAt": "2024-01-15T16:00:00Z"
    },
    "deliveryStats": {
      "totalSent": 1250,
      "pushSent": 1250,
      "emailSent": 1250,
      "failed": 0,
      "pending": 0
    }
  }
}
```

**Business Logic:**

- Validate admin permissions for sending notifications
- Filter recipients based on course enrollment and preferences
- Support multiple delivery channels
- Track delivery status and failures
- Handle timezone conversions for scheduling

---

### **9.2 Get User Notifications**

**Endpoint:** `GET /api/notifications`

**Purpose:** Get paginated notifications for the authenticated user

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
page: number (default: 1) - Page number
limit: number (default: 20) - Items per page
type: string (optional) - Filter by type: new_class_upload, live_class_reminder, test_reminder, motivational, announcement
status: string (optional) - Filter by status: read, unread
courseId: string (optional) - Filter by specific course
examId: string (optional) - Filter by specific exam
markAsRead: boolean (default: false) - Mark notifications as read when fetched
```

**Response:**

```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": "notification_456",
        "type": "new_class_upload",
        "title": "New Video Added: Modern History",
        "message": "Modern History - Freedom Struggle video is now available in your BPSC course",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "examId": "bpsc",
        "examName": "BPSC",
        "data": {
          "contentId": "video_modern_history_1",
          "contentType": "video",
          "actionUrl": "/videos/video_modern_history_1",
          "actionText": "Watch Now"
        },
        "imageUrl": "https://cdn.example.com/notifications/modern-history.jpg",
        "iconUrl": "https://cdn.example.com/icons/video-added.png",
        "priority": "high",
        "isRead": false,
        "readAt": null,
        "createdAt": "2024-01-15T16:00:00Z",
        "expiresAt": "2024-02-15T16:00:00Z"
      },
      {
        "id": "notification_457",
        "type": "live_class_reminder",
        "title": "Live Class Starting Soon",
        "message": "Your live class 'Indian Polity - Constitutional Framework' starts in 1 hour",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "examId": "bpsc",
        "examName": "BPSC",
        "data": {
          "contentId": "live_class_123",
          "contentType": "live_class",
          "actionUrl": "/live-classes/live_class_123",
          "actionText": "Join Class",
          "scheduledAt": "2024-01-20T10:00:00Z"
        },
        "imageUrl": "https://cdn.example.com/notifications/live-class.jpg",
        "iconUrl": "https://cdn.example.com/icons/live-class.png",
        "priority": "high",
        "isRead": true,
        "readAt": "2024-01-20T09:30:00Z",
        "createdAt": "2024-01-20T09:00:00Z",
        "expiresAt": "2024-01-20T11:30:00Z"
      },
      {
        "id": "notification_458",
        "type": "test_reminder",
        "title": "Test Reminder",
        "message": "Don't forget to attempt the Ancient History test. Only 2 days left!",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "examId": "bpsc",
        "examName": "BPSC",
        "data": {
          "contentId": "test_ancient_history_mcq",
          "contentType": "test",
          "actionUrl": "/tests/test_ancient_history_mcq",
          "actionText": "Take Test",
          "dueDate": "2024-01-22T23:59:59Z"
        },
        "imageUrl": "https://cdn.example.com/notifications/test-reminder.jpg",
        "iconUrl": "https://cdn.example.com/icons/test-reminder.png",
        "priority": "medium",
        "isRead": false,
        "readAt": null,
        "createdAt": "2024-01-20T10:00:00Z",
        "expiresAt": "2024-01-22T23:59:59Z"
      },
      {
        "id": "notification_459",
        "type": "motivational",
        "title": "Daily Motivation",
        "message": "Success is not final, failure is not fatal: it is the courage to continue that counts. Keep studying!",
        "courseId": null,
        "courseName": null,
        "examId": "bpsc",
        "examName": "BPSC",
        "data": {
          "quote": "Success is not final, failure is not fatal: it is the courage to continue that counts.",
          "author": "Winston Churchill",
          "category": "motivation"
        },
        "imageUrl": "https://cdn.example.com/notifications/motivation.jpg",
        "iconUrl": "https://cdn.example.com/icons/motivation.png",
        "priority": "low",
        "isRead": false,
        "readAt": null,
        "createdAt": "2024-01-20T08:00:00Z",
        "expiresAt": "2024-01-21T08:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 45,
      "itemsPerPage": 20,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "summary": {
      "totalNotifications": 45,
      "unreadCount": 12,
      "todayCount": 5,
      "weekCount": 18,
      "typeBreakdown": {
        "new_class_upload": 15,
        "live_class_reminder": 8,
        "test_reminder": 12,
        "motivational": 7,
        "announcement": 3
      }
    }
  }
}
```

**Business Logic:**

- Filter notifications based on user's enrolled courses and exams
- Respect user's notification preferences
- Mark notifications as read if requested
- Include action URLs for direct navigation
- Track notification engagement

### **9.3 Mark Notification as Read**

**Endpoint:** `PUT /api/notifications/{notificationId}/read`

**Purpose:** Mark a specific notification as read

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "Notification marked as read",
  "data": {
    "notification": {
      "id": "notification_456",
      "isRead": true,
      "readAt": "2024-01-20T15:30:00Z"
    }
  }
}
```

---

**Endpoint:** `PUT /api/notifications/mark-all-read`

**Purpose:** Mark all notifications as read for the user

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
type: string (optional) - Mark only specific type as read
courseId: string (optional) - Mark only specific course notifications as read
```

**Response:**

```json
{
  "success": true,
  "message": "All notifications marked as read",
  "data": {
    "markedCount": 12,
    "totalNotifications": 45
  }
}
```

---

### **9.4 Manage Notification Preferences**

**Endpoint:** `GET /api/notifications/preferences`

**Purpose:** Get user's notification preferences

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "success": true,
  "message": "Notification preferences retrieved successfully",
  "data": {
    "preferences": {
      "newClassUpload": {
        "enabled": true,
        "channels": ["push", "email"],
        "frequency": "immediate"
      },
      "liveClassReminder": {
        "enabled": true,
        "channels": ["push"],
        "reminderTimes": [1440, 60, 15]
      },
      "testReminder": {
        "enabled": true,
        "channels": ["push", "email"],
        "reminderTimes": [2880, 1440, 720]
      },
      "motivationalQuotes": {
        "enabled": true,
        "channels": ["push"],
        "frequency": "daily",
        "preferredTime": "08:00"
      },
      "announcements": {
        "enabled": true,
        "channels": ["push", "email"],
        "frequency": "immediate"
      },
      "achievementNotifications": {
        "enabled": true,
        "channels": ["push"],
        "frequency": "immediate"
      }
    },
    "globalSettings": {
      "doNotDisturbStart": "22:00",
      "doNotDisturbEnd": "07:00",
      "timezone": "Asia/Kolkata",
      "language": "hindi"
    },
    "examSpecificSettings": [
      {
        "examId": "bpsc",
        "examName": "BPSC",
        "enabled": true,
        "customPreferences": {
          "testReminder": {
            "enabled": true,
            "reminderTimes": [2880, 1440]
          }
        }
      }
    ]
  }
}
```

---

**Endpoint:** `PUT /api/notifications/preferences`

**Purpose:** Update user's notification preferences

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "preferences": {
    "newClassUpload": {
      "enabled": true,
      "channels": ["push", "email"],
      "frequency": "immediate"
    },
    "liveClassReminder": {
      "enabled": true,
      "channels": ["push"],
      "reminderTimes": [1440, 60, 15]
    },
    "testReminder": {
      "enabled": false,
      "channels": [],
      "reminderTimes": []
    },
    "motivationalQuotes": {
      "enabled": true,
      "channels": ["push"],
      "frequency": "daily",
      "preferredTime": "08:00"
    }
  },
  "globalSettings": {
    "doNotDisturbStart": "22:00",
    "doNotDisturbEnd": "07:00",
    "timezone": "Asia/Kolkata",
    "language": "hindi"
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Notification preferences updated successfully",
  "data": {
    "preferences": {
      "newClassUpload": {
        "enabled": true,
        "channels": ["push", "email"],
        "frequency": "immediate"
      },
      "testReminder": {
        "enabled": false,
        "channels": [],
        "reminderTimes": []
      }
    },
    "updatedAt": "2024-01-20T16:00:00Z"
  }
}
```

**Business Logic:**

- Validate notification types and channels
- Respect do-not-disturb hours
- Handle timezone conversions
- Update Firebase FCM topic subscriptions
- Track preference changes for analytics

---

### **9.5 Get Notification Analytics**

**Endpoint:** `GET /api/notifications/analytics`

**Purpose:** Get notification delivery and engagement analytics (Admin only)

**Headers:**

```
Authorization: Bearer <admin_access_token>
```

**Query Parameters:**

```
startDate: string (required) - Start date (ISO format)
endDate: string (required) - End date (ISO format)
type: string (optional) - Filter by notification type
examId: string (optional) - Filter by exam
courseId: string (optional) - Filter by course
```

**Response:**

```json
{
  "success": true,
  "message": "Notification analytics retrieved successfully",
  "data": {
    "summary": {
      "totalNotificationsSent": 15000,
      "totalRecipients": 5000,
      "deliveryRate": 0.98,
      "openRate": 0.65,
      "clickRate": 0.35,
      "unsubscribeRate": 0.02
    },
    "typeBreakdown": [
      {
        "type": "new_class_upload",
        "sent": 6000,
        "delivered": 5880,
        "opened": 4200,
        "clicked": 2520,
        "deliveryRate": 0.98,
        "openRate": 0.71,
        "clickRate": 0.42
      },
      {
        "type": "live_class_reminder",
        "sent": 3000,
        "delivered": 2940,
        "opened": 2205,
        "clicked": 1470,
        "deliveryRate": 0.98,
        "openRate": 0.75,
        "clickRate": 0.49
      },
      {
        "type": "test_reminder",
        "sent": 4000,
        "delivered": 3920,
        "opened": 2352,
        "clicked": 1176,
        "deliveryRate": 0.98,
        "openRate": 0.6,
        "clickRate": 0.29
      },
      {
        "type": "motivational",
        "sent": 2000,
        "delivered": 1960,
        "opened": 1176,
        "clicked": 392,
        "deliveryRate": 0.98,
        "openRate": 0.6,
        "clickRate": 0.2
      }
    ],
    "channelPerformance": [
      {
        "channel": "push",
        "sent": 12000,
        "delivered": 11760,
        "opened": 7056,
        "clicked": 3528,
        "deliveryRate": 0.98,
        "openRate": 0.6,
        "clickRate": 0.3
      },
      {
        "channel": "email",
        "sent": 3000,
        "delivered": 2940,
        "opened": 2205,
        "clicked": 1323,
        "deliveryRate": 0.98,
        "openRate": 0.75,
        "clickRate": 0.45
      }
    ],
    "timeAnalysis": {
      "bestSendTime": "09:00",
      "bestSendDay": "Tuesday",
      "hourlyBreakdown": [
        {
          "hour": 9,
          "sent": 2000,
          "openRate": 0.75,
          "clickRate": 0.45
        }
      ]
    },
    "examPerformance": [
      {
        "examId": "bpsc",
        "examName": "BPSC",
        "sent": 8000,
        "openRate": 0.68,
        "clickRate": 0.38
      },
      {
        "examId": "ssc",
        "examName": "SSC",
        "sent": 7000,
        "openRate": 0.62,
        "clickRate": 0.32
      }
    ]
  }
}
```

**Business Logic:**

- Calculate delivery and engagement metrics
- Analyze performance by type, channel, and time
- Provide insights for optimization
- Track user engagement patterns
- Generate recommendations for improvement

## 📋 **NOTIFICATIONS MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 9 (Notifications Module):**

1. **`POST /api/notifications/send`** - Send push notification to users or groups (ADMIN)
2. **`GET /api/notifications`** - Get paginated user notifications with filtering (PRIMARY)
3. **`PUT /api/notifications/{notificationId}/read`** - Mark specific notification as read
4. **`PUT /api/notifications/mark-all-read`** - Mark all notifications as read
5. **`GET /api/notifications/preferences`** - Get user notification preferences
6. **`PUT /api/notifications/preferences`** - Update user notification preferences
7. **`GET /api/notifications/analytics`** - Get notification analytics (ADMIN)

### **🏗️ Database Schema Requirements:**

#### **Notifications Table:**

```sql
CREATE TABLE notifications (
  id VARCHAR(36) PRIMARY KEY,
  type ENUM('new_class_upload', 'live_class_reminder', 'test_reminder', 'motivational', 'announcement', 'achievement') NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  course_id VARCHAR(36),
  exam_id VARCHAR(36),
  content_id VARCHAR(36),
  content_type ENUM('video', 'pdf', 'test', 'live_class'),
  action_url VARCHAR(1000),
  action_text VARCHAR(100),
  image_url VARCHAR(1000),
  icon_url VARCHAR(500),
  priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
  channels JSON, -- ["push", "email", "sms"]
  data JSON, -- Additional notification data
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (exam_id) REFERENCES exams(id)
);
```

#### **User Notifications Table:**

```sql
CREATE TABLE user_notifications (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  notification_id VARCHAR(36) NOT NULL,
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMP NULL,
  delivered_at TIMESTAMP,
  opened_at TIMESTAMP,
  clicked_at TIMESTAMP,
  delivery_status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'pending',
  failure_reason TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_notification (user_id, notification_id)
);
```

#### **Notification Preferences Table:**

```sql
CREATE TABLE notification_preferences (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  new_class_upload_enabled BOOLEAN DEFAULT true,
  new_class_upload_channels JSON DEFAULT '["push", "email"]',
  live_class_reminder_enabled BOOLEAN DEFAULT true,
  live_class_reminder_channels JSON DEFAULT '["push"]',
  live_class_reminder_times JSON DEFAULT '[1440, 60, 15]', -- minutes before
  test_reminder_enabled BOOLEAN DEFAULT true,
  test_reminder_channels JSON DEFAULT '["push", "email"]',
  test_reminder_times JSON DEFAULT '[2880, 1440, 720]', -- minutes before
  motivational_enabled BOOLEAN DEFAULT true,
  motivational_channels JSON DEFAULT '["push"]',
  motivational_frequency ENUM('daily', 'weekly', 'never') DEFAULT 'daily',
  motivational_preferred_time TIME DEFAULT '08:00:00',
  announcements_enabled BOOLEAN DEFAULT true,
  announcements_channels JSON DEFAULT '["push", "email"]',
  achievement_enabled BOOLEAN DEFAULT true,
  achievement_channels JSON DEFAULT '["push"]',
  do_not_disturb_start TIME DEFAULT '22:00:00',
  do_not_disturb_end TIME DEFAULT '07:00:00',
  timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
  language VARCHAR(10) DEFAULT 'hindi',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  UNIQUE KEY unique_user_preferences (user_id)
);
```

#### **FCM Tokens Table:**

```sql
CREATE TABLE fcm_tokens (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  token VARCHAR(500) NOT NULL,
  device_id VARCHAR(255),
  device_type ENUM('android', 'ios', 'web') NOT NULL,
  app_version VARCHAR(50),
  is_active BOOLEAN DEFAULT true,
  last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  UNIQUE KEY unique_token (token),
  INDEX idx_user_active (user_id, is_active)
);
```

### **🔧 Technical Implementation Notes:**

#### **Firebase Integration:**

- Use Firebase Cloud Messaging (FCM) for push notifications
- Implement topic-based messaging for exam/course groups
- Handle token management and device registration
- Support rich notifications with images and actions

#### **Multi-Channel Delivery:**

- Push notifications via Firebase FCM
- Email notifications via SMTP/SendGrid
- SMS notifications via Twilio/AWS SNS
- In-app notifications for real-time updates

#### **Scheduling System:**

- Use job queues (Redis/Celery) for scheduled notifications
- Handle timezone conversions for global users
- Implement retry logic for failed deliveries
- Support recurring notifications (daily motivational quotes)

#### **Performance Optimization:**

- Index on user_id, notification_id, and created_at fields
- Use Redis for caching notification preferences
- Implement batch processing for bulk notifications
- Optimize database queries with proper indexing

#### **Security Considerations:**

- Validate admin permissions for sending notifications
- Implement rate limiting on notification sending
- Secure FCM token storage and management
- Track and prevent notification spam

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Firebase Integration**: FCM token registration and message handling
- **Notification Center**: Display notifications with read/unread status
- **Preference Management**: Settings screen for notification preferences
- **Action Handling**: Deep linking to content from notifications
- **Badge Management**: Unread count display on app icon

### **🚀 Implementation Priority:**

1. **HIGH**: User notifications and preferences (core engagement)
2. **HIGH**: Firebase push notification delivery (real-time communication)
3. **MEDIUM**: Admin notification sending (content management)
4. **MEDIUM**: Multi-channel delivery (email, SMS)
5. **LOW**: Analytics and optimization (insights and improvement)

### **📊 Notification Types Implementation:**

#### **New Class Upload Notifications:**

- Triggered when new video/PDF is added to enrolled courses
- Include course name, content title, and direct action link
- Respect user preferences for immediate or batched delivery

#### **Live Class Reminders:**

- Scheduled notifications before live classes (24h, 1h, 15min)
- Include meeting links and class details
- Support timezone-aware scheduling

#### **Test Reminders:**

- Reminders for upcoming test deadlines
- Include test details and time remaining
- Support multiple reminder intervals

#### **Motivational Quotes:**

- Daily inspirational messages for student motivation
- Customizable timing based on user preferences
- Exam-specific motivational content

#### **Achievement Notifications:**

- Instant notifications for earned achievements
- Include achievement details and points earned
- Support gamification and engagement

---

## 💳 **PHASE 10: PAYMENT GATEWAY MODULE APIs (Step 11 - Requirement.md)**

> **Implementation Priority:** HIGH - Critical for monetization and course access
> **Dependencies:** Course management and user authentication
> **Frontend Status:** ✅ READY - Course pricing models and payment flow implemented

### **Overview:**

The Payment Gateway Module provides comprehensive payment processing for course purchases with Razorpay/Stripe integration, invoice generation, referral system, and support for free/paid/EMI course structures. This implements the core monetization system for the educational platform.

**Key Features:**

- Razorpay and Stripe payment gateway integration
- Complete payment flow: Course → Price → Buy → Payment → Unlock content
- Automatic invoice generation with GST calculation
- Referral code system with discount management
- Support for Free, Paid, and EMI course structures
- Payment verification and security
- Transaction history and analytics

---

### **10.1 Create Payment Order**

**Endpoint:** `POST /api/payments/create-order`

**Purpose:** Create payment order for course purchase with pricing calculation

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "courseId": "course_bpsc_70th_prelims",
  "paymentMethod": "full_payment",
  "couponCode": "SAVE20",
  "referralCode": "REF123456",
  "emiPlan": null
}
```

**Response:**

```json
{
  "success": true,
  "message": "Payment order created successfully",
  "data": {
    "order": {
      "id": "order_456789",
      "razorpayOrderId": "order_MnKLmNOPqr123456",
      "stripePaymentIntentId": "pi_3MnKLmNOPqr123456_1234567890",
      "amount": 94400,
      "currency": "INR",
      "status": "created"
    },
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
      "instructorName": "Dr. Rajesh Kumar"
    },
    "pricing": {
      "originalPrice": 12000,
      "courseDiscount": 2000,
      "courseDiscountPercentage": 16.67,
      "couponDiscount": 1000,
      "couponCode": "SAVE20",
      "referralDiscount": 500,
      "referralCode": "REF123456",
      "subtotal": 8500,
      "gstRate": 18,
      "gstAmount": 1530,
      "totalAmount": 10030,
      "finalAmount": 10030,
      "savings": 3500
    },
    "paymentGateways": {
      "razorpay": {
        "keyId": "rzp_live_1234567890",
        "orderId": "order_MnKLmNOPqr123456",
        "amount": 1003000,
        "currency": "INR",
        "name": "Utkrishta Education",
        "description": "BPSC 70th Prelims Complete Course",
        "image": "https://cdn.example.com/logo.png",
        "prefill": {
          "name": "Rajesh Kumar",
          "email": "<EMAIL>",
          "contact": "+************"
        },
        "theme": {
          "color": "#3399cc"
        }
      },
      "stripe": {
        "publishableKey": "pk_live_1234567890",
        "clientSecret": "pi_3MnKLmNOPqr123456_secret_1234567890",
        "paymentIntentId": "pi_3MnKLmNOPqr123456_1234567890"
      }
    },
    "emiOptions": [
      {
        "duration": 3,
        "monthlyAmount": 3677,
        "totalAmount": 11030,
        "interestRate": 10,
        "processingFee": 500
      },
      {
        "duration": 6,
        "monthlyAmount": 1922,
        "totalAmount": 11530,
        "interestRate": 15,
        "processingFee": 500
      }
    ],
    "validUntil": "2024-01-20T16:30:00Z"
  }
}
```

**Business Logic:**

- Calculate pricing with discounts and taxes
- Validate coupon and referral codes
- Create orders in both Razorpay and Stripe
- Generate EMI options if applicable
- Set order expiration time

---

### **10.2 Verify Payment**

**Endpoint:** `POST /api/payments/verify`

**Purpose:** Verify payment signature and complete course enrollment

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "orderId": "order_456789",
  "gateway": "razorpay",
  "paymentData": {
    "razorpay_order_id": "order_MnKLmNOPqr123456",
    "razorpay_payment_id": "pay_MnKLmNOPqr789012",
    "razorpay_signature": "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
  },
  "paymentMethod": {
    "type": "card",
    "details": {
      "last4": "1234",
      "brand": "visa",
      "funding": "credit"
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Payment verified and course unlocked successfully",
  "data": {
    "payment": {
      "id": "payment_789012",
      "orderId": "order_456789",
      "gatewayPaymentId": "pay_MnKLmNOPqr789012",
      "gateway": "razorpay",
      "amount": 10030,
      "currency": "INR",
      "status": "completed",
      "paidAt": "2024-01-20T15:45:00Z"
    },
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "accessGranted": true,
      "enrolledAt": "2024-01-20T15:45:00Z",
      "validUntil": "2025-01-20T15:45:00Z"
    },
    "invoice": {
      "id": "invoice_345678",
      "invoiceNumber": "UTK-2024-000123",
      "downloadUrl": "https://cdn.example.com/invoices/UTK-2024-000123.pdf",
      "generatedAt": "2024-01-20T15:45:00Z"
    },
    "referralReward": {
      "referrerUserId": "user_referrer_123",
      "rewardAmount": 250,
      "rewardType": "wallet_credit",
      "message": "You earned ₹250 for successful referral!"
    }
  }
}
```

**Business Logic:**

- Verify payment signature with gateway
- Validate payment amount and order details
- Grant course access to user
- Generate and send invoice
- Process referral rewards
- Send confirmation notifications

### **10.3 Get Payment History**

**Endpoint:** `GET /api/payments/history`

**Purpose:** Get user's payment transaction history

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
page: number (default: 1) - Page number
limit: number (default: 10) - Items per page
status: string (optional) - Filter by status: created, pending, completed, failed, refunded
gateway: string (optional) - Filter by gateway: razorpay, stripe
startDate: string (optional) - Start date filter (ISO format)
endDate: string (optional) - End date filter (ISO format)
```

**Response:**

```json
{
  "success": true,
  "message": "Payment history retrieved successfully",
  "data": {
    "payments": [
      {
        "id": "payment_789012",
        "orderId": "order_456789",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "amount": 10030,
        "currency": "INR",
        "gateway": "razorpay",
        "gatewayPaymentId": "pay_MnKLmNOPqr789012",
        "status": "completed",
        "paymentMethod": {
          "type": "card",
          "details": {
            "last4": "1234",
            "brand": "visa"
          }
        },
        "createdAt": "2024-01-20T15:30:00Z",
        "paidAt": "2024-01-20T15:45:00Z",
        "invoice": {
          "invoiceNumber": "UTK-2024-000123",
          "downloadUrl": "https://cdn.example.com/invoices/UTK-2024-000123.pdf"
        }
      },
      {
        "id": "payment_789013",
        "orderId": "order_456790",
        "courseId": "course_ssc_cgl_tier1",
        "courseName": "SSC CGL Tier 1 Complete Course",
        "amount": 8500,
        "currency": "INR",
        "gateway": "stripe",
        "gatewayPaymentId": "pi_3MnKLmNOPqr123457_1234567891",
        "status": "failed",
        "paymentMethod": {
          "type": "upi",
          "details": {
            "vpa": "user@paytm"
          }
        },
        "createdAt": "2024-01-18T14:20:00Z",
        "failedAt": "2024-01-18T14:25:00Z",
        "failureReason": "Insufficient funds"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10
    },
    "summary": {
      "totalPayments": 25,
      "totalAmount": 125000,
      "successfulPayments": 20,
      "failedPayments": 3,
      "refundedPayments": 2,
      "successRate": 80
    }
  }
}
```

**Business Logic:**

- Filter payments by status, gateway, and date range
- Include course and invoice information
- Calculate payment statistics
- Support pagination for large datasets

---

### **10.4 Manage Coupons and Referrals**

**Endpoint:** `GET /api/payments/coupons/validate`

**Purpose:** Validate coupon code for course purchase

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
couponCode: string (required) - Coupon code to validate
courseId: string (required) - Course ID for which coupon is being applied
```

**Response:**

```json
{
  "success": true,
  "message": "Coupon validated successfully",
  "data": {
    "coupon": {
      "id": "coupon_123",
      "code": "SAVE20",
      "title": "20% Off on All Courses",
      "description": "Get 20% discount on any course purchase",
      "discountType": "percentage",
      "discountValue": 20,
      "maxDiscountAmount": 2000,
      "minOrderAmount": 5000,
      "isValid": true,
      "validFrom": "2024-01-01T00:00:00Z",
      "validUntil": "2024-12-31T23:59:59Z",
      "usageLimit": 1000,
      "usedCount": 245,
      "userUsageLimit": 1,
      "userUsedCount": 0
    },
    "applicableDiscount": {
      "originalPrice": 12000,
      "discountAmount": 2000,
      "finalPrice": 10000,
      "savings": 2000
    }
  }
}
```

---

**Endpoint:** `GET /api/payments/referrals/validate`

**Purpose:** Validate referral code and get referral information

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
referralCode: string (required) - Referral code to validate
```

**Response:**

```json
{
  "success": true,
  "message": "Referral code validated successfully",
  "data": {
    "referral": {
      "id": "referral_456",
      "code": "REF123456",
      "referrerUserId": "user_referrer_123",
      "referrerName": "Amit Sharma",
      "isValid": true,
      "discountType": "fixed",
      "discountValue": 500,
      "referrerReward": 250,
      "refereeReward": 500,
      "maxUsage": 50,
      "usedCount": 12,
      "validUntil": "2024-12-31T23:59:59Z"
    },
    "applicableDiscount": {
      "discountAmount": 500,
      "message": "You'll save ₹500 with this referral code!"
    }
  }
}
```

---

### **10.5 EMI Management**

**Endpoint:** `POST /api/payments/emi/create`

**Purpose:** Create EMI plan for course purchase

**Headers:**

```
Authorization: Bearer <access_token>
```

**Request Body:**

```json
{
  "courseId": "course_bpsc_70th_prelims",
  "emiDuration": 6,
  "downPayment": 2000
}
```

**Response:**

```json
{
  "success": true,
  "message": "EMI plan created successfully",
  "data": {
    "emiPlan": {
      "id": "emi_plan_789",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course",
      "totalAmount": 10030,
      "downPayment": 2000,
      "remainingAmount": 8030,
      "duration": 6,
      "monthlyAmount": 1422,
      "interestRate": 12,
      "processingFee": 500,
      "totalPayable": 10532,
      "startDate": "2024-02-01T00:00:00Z",
      "endDate": "2024-07-01T00:00:00Z",
      "status": "active"
    },
    "installments": [
      {
        "installmentNumber": 1,
        "amount": 1422,
        "dueDate": "2024-02-01T00:00:00Z",
        "status": "pending"
      },
      {
        "installmentNumber": 2,
        "amount": 1422,
        "dueDate": "2024-03-01T00:00:00Z",
        "status": "pending"
      }
    ]
  }
}
```

---

**Endpoint:** `GET /api/payments/emi/installments`

**Purpose:** Get user's EMI installment schedule

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
status: string (optional) - Filter by status: pending, paid, overdue
```

**Response:**

```json
{
  "success": true,
  "message": "EMI installments retrieved successfully",
  "data": {
    "installments": [
      {
        "id": "installment_123",
        "emiPlanId": "emi_plan_789",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "installmentNumber": 1,
        "amount": 1422,
        "dueDate": "2024-02-01T00:00:00Z",
        "status": "paid",
        "paidAt": "2024-01-30T14:30:00Z",
        "paymentId": "payment_installment_456"
      },
      {
        "id": "installment_124",
        "emiPlanId": "emi_plan_789",
        "courseId": "course_bpsc_70th_prelims",
        "courseName": "BPSC 70th Prelims Complete Course",
        "installmentNumber": 2,
        "amount": 1422,
        "dueDate": "2024-03-01T00:00:00Z",
        "status": "pending",
        "daysUntilDue": 5
      }
    ],
    "summary": {
      "totalInstallments": 6,
      "paidInstallments": 1,
      "pendingInstallments": 5,
      "overdueInstallments": 0,
      "nextDueDate": "2024-03-01T00:00:00Z",
      "nextDueAmount": 1422
    }
  }
}
```

**Business Logic:**

- Calculate EMI amounts with interest and fees
- Track installment payment status
- Send reminders for due installments
- Handle overdue payments and penalties

## 📋 **PAYMENT GATEWAY MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 11 (Payment Gateway Module):**

1. **`POST /api/payments/create-order`** - Create payment order with pricing calculation (PRIMARY)
2. **`POST /api/payments/verify`** - Verify payment and complete course enrollment
3. **`GET /api/payments/history`** - Get user's payment transaction history
4. **`GET /api/payments/coupons/validate`** - Validate coupon codes for discounts
5. **`GET /api/payments/referrals/validate`** - Validate referral codes and rewards
6. **`POST /api/payments/emi/create`** - Create EMI plans for course purchases
7. **`GET /api/payments/emi/installments`** - Get EMI installment schedules

### **🏗️ Database Schema Requirements:**

#### **Payment Orders Table:**

```sql
CREATE TABLE payment_orders (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  course_id VARCHAR(36) NOT NULL,
  razorpay_order_id VARCHAR(255),
  stripe_payment_intent_id VARCHAR(255),
  amount INT NOT NULL, -- in paise/cents
  currency VARCHAR(3) DEFAULT 'INR',
  original_price INT NOT NULL,
  discount_amount INT DEFAULT 0,
  coupon_id VARCHAR(36),
  coupon_discount INT DEFAULT 0,
  referral_id VARCHAR(36),
  referral_discount INT DEFAULT 0,
  gst_amount INT DEFAULT 0,
  final_amount INT NOT NULL,
  status ENUM('created', 'pending', 'completed', 'failed', 'expired') DEFAULT 'created',
  payment_method ENUM('full_payment', 'emi') DEFAULT 'full_payment',
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (coupon_id) REFERENCES coupons(id),
  FOREIGN KEY (referral_id) REFERENCES referrals(id)
);
```

#### **Payments Table:**

```sql
CREATE TABLE payments (
  id VARCHAR(36) PRIMARY KEY,
  order_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  course_id VARCHAR(36) NOT NULL,
  gateway ENUM('razorpay', 'stripe') NOT NULL,
  gateway_payment_id VARCHAR(255),
  gateway_order_id VARCHAR(255),
  amount INT NOT NULL,
  currency VARCHAR(3) DEFAULT 'INR',
  status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
  payment_method_type ENUM('card', 'upi', 'netbanking', 'wallet', 'emi') NOT NULL,
  payment_method_details JSON,
  gateway_response JSON,
  failure_reason TEXT,
  refund_amount INT DEFAULT 0,
  refund_reason TEXT,
  invoice_id VARCHAR(36),
  paid_at TIMESTAMP,
  refunded_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES payment_orders(id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);
```

#### **Coupons Table:**

```sql
CREATE TABLE coupons (
  id VARCHAR(36) PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  discount_type ENUM('percentage', 'fixed') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  max_discount_amount INT,
  min_order_amount INT DEFAULT 0,
  usage_limit INT,
  used_count INT DEFAULT 0,
  user_usage_limit INT DEFAULT 1,
  applicable_courses JSON, -- course IDs or "all"
  applicable_exams JSON, -- exam IDs or "all"
  is_active BOOLEAN DEFAULT true,
  valid_from TIMESTAMP,
  valid_until TIMESTAMP,
  created_by VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### **Referrals Table:**

```sql
CREATE TABLE referrals (
  id VARCHAR(36) PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  referrer_user_id VARCHAR(36) NOT NULL,
  discount_type ENUM('percentage', 'fixed') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  referrer_reward_type ENUM('fixed', 'percentage') DEFAULT 'fixed',
  referrer_reward_value DECIMAL(10,2) NOT NULL,
  referee_reward_type ENUM('fixed', 'percentage') DEFAULT 'fixed',
  referee_reward_value DECIMAL(10,2) NOT NULL,
  max_usage INT DEFAULT 100,
  used_count INT DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  valid_until TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (referrer_user_id) REFERENCES users(id)
);
```

#### **EMI Plans Table:**

```sql
CREATE TABLE emi_plans (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  course_id VARCHAR(36) NOT NULL,
  order_id VARCHAR(36) NOT NULL,
  total_amount INT NOT NULL,
  down_payment INT DEFAULT 0,
  remaining_amount INT NOT NULL,
  duration INT NOT NULL, -- in months
  monthly_amount INT NOT NULL,
  interest_rate DECIMAL(5,2) NOT NULL,
  processing_fee INT DEFAULT 0,
  total_payable INT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  status ENUM('active', 'completed', 'defaulted', 'cancelled') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  FOREIGN KEY (order_id) REFERENCES payment_orders(id)
);
```

#### **EMI Installments Table:**

```sql
CREATE TABLE emi_installments (
  id VARCHAR(36) PRIMARY KEY,
  emi_plan_id VARCHAR(36) NOT NULL,
  installment_number INT NOT NULL,
  amount INT NOT NULL,
  due_date DATE NOT NULL,
  status ENUM('pending', 'paid', 'overdue', 'waived') DEFAULT 'pending',
  payment_id VARCHAR(36),
  paid_at TIMESTAMP,
  late_fee INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (emi_plan_id) REFERENCES emi_plans(id) ON DELETE CASCADE,
  FOREIGN KEY (payment_id) REFERENCES payments(id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Payment Gateway Integration:**

- Dual gateway support (Razorpay + Stripe) for redundancy
- Webhook handling for payment status updates
- Signature verification for security
- Automatic retry for failed payments

#### **Pricing Engine:**

- Dynamic pricing calculation with multiple discount layers
- GST calculation based on Indian tax regulations
- Coupon validation with usage limits and expiry
- Referral system with dual rewards (referrer + referee)

#### **EMI System:**

- Flexible EMI plans with configurable interest rates
- Automatic installment generation and tracking
- Late fee calculation for overdue payments
- EMI payment reminders and notifications

#### **Invoice Generation:**

- Automatic PDF invoice generation
- GST-compliant invoice format
- Digital signature and watermarking
- Email delivery and download links

#### **Security Measures:**

- Payment signature verification
- PCI DSS compliance for card data
- Encrypted storage of sensitive information
- Fraud detection and prevention

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Course Purchase Flow**: Complete payment integration with Razorpay/Stripe
- **Pricing Display**: Dynamic price calculation with discounts
- **Payment History**: Transaction history with invoice downloads
- **EMI Management**: EMI plan creation and installment tracking
- **Coupon System**: Coupon validation and application
- **Referral System**: Referral code sharing and validation

### **🚀 Implementation Priority:**

1. **HIGH**: Payment order creation and verification (core monetization)
2. **HIGH**: Course access unlocking after payment (user experience)
3. **MEDIUM**: Payment history and invoice generation (user records)
4. **MEDIUM**: Coupon and referral systems (marketing features)
5. **LOW**: EMI system and advanced analytics (additional features)

---

## 📊 **PHASE 11: PERFORMANCE & ANALYTICS MODULE APIs (Step 12 - Requirement.md)**

> **Implementation Priority:** MEDIUM - Important for user engagement and insights
> **Dependencies:** All content modules (videos, PDFs, tests, live classes)
> **Frontend Status:** ✅ READY - Basic progress tracking implemented, ready for advanced analytics

### **Overview:**

The Performance & Analytics Module provides comprehensive tracking and visualization of student learning progress with detailed charts, subject-wise performance analysis, monthly leaderboards, and completion badges. This implements the core analytics system for student engagement and performance insights.

**Key Features:**

- Student-wise progress charts with detailed breakdowns
- Subject-wise performance tracking and analysis
- Monthly leaderboards based on quiz and test performance
- Completion badges and achievement system
- Learning analytics and insights
- Comparative performance analysis
- Time-based progress tracking

---

### **11.1 Get Student Progress Analytics**

**Endpoint:** `GET /api/analytics/student/progress`

**Purpose:** Get comprehensive student progress analytics with charts and insights

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
timeRange: string (default: "all") - Time range: week, month, quarter, year, all
courseId: string (optional) - Filter by specific course
subjectId: string (optional) - Filter by specific subject
includeComparison: boolean (default: true) - Include peer comparison data
```

**Response:**

```json
{
  "success": true,
  "message": "Student progress analytics retrieved successfully",
  "data": {
    "overallProgress": {
      "completionPercentage": 68.5,
      "totalTimeSpent": 15600,
      "totalTimeSpentFormatted": "4h 20m",
      "averageDailyTime": 45,
      "studyStreak": {
        "current": 12,
        "longest": 28,
        "target": 30
      },
      "lastActivityAt": "2024-01-20T16:30:00Z"
    },
    "contentProgress": {
      "videos": {
        "totalVideos": 120,
        "watchedVideos": 85,
        "completionPercentage": 70.8,
        "totalWatchTime": 8400,
        "averageWatchTime": 98.8,
        "recentlyWatched": [
          {
            "videoId": "video_modern_history_1",
            "title": "Modern History - Freedom Struggle",
            "watchedAt": "2024-01-20T15:30:00Z",
            "watchPercentage": 100
          }
        ]
      },
      "pdfs": {
        "totalPDFs": 45,
        "openedPDFs": 32,
        "completionPercentage": 71.1,
        "totalReadTime": 3600,
        "averageReadTime": 112.5,
        "recentlyOpened": [
          {
            "pdfId": "pdf_ancient_history_notes",
            "title": "Ancient History Complete Notes",
            "openedAt": "2024-01-20T14:15:00Z",
            "readPercentage": 85
          }
        ]
      },
      "tests": {
        "totalTests": 25,
        "attemptedTests": 18,
        "completionPercentage": 72.0,
        "averageScore": 82.5,
        "bestScore": 96,
        "improvementTrend": "positive",
        "recentAttempts": [
          {
            "testId": "test_ancient_history_mcq",
            "title": "Ancient History MCQ Test",
            "attemptedAt": "2024-01-20T12:00:00Z",
            "score": 88,
            "maxScore": 100
          }
        ]
      },
      "liveClasses": {
        "totalClasses": 12,
        "attendedClasses": 9,
        "attendancePercentage": 75.0,
        "totalAttendanceTime": 2700,
        "averageAttendanceTime": 300,
        "recentAttendance": [
          {
            "classId": "live_class_123",
            "title": "Indian Polity - Constitutional Framework",
            "attendedAt": "2024-01-20T10:00:00Z",
            "attendanceDuration": 85
          }
        ]
      }
    },
    "subjectWiseProgress": [
      {
        "subjectId": "subject_history",
        "subjectName": "History",
        "completionPercentage": 85.2,
        "timeSpent": 4800,
        "videosWatched": 28,
        "totalVideos": 32,
        "testsAttempted": 8,
        "totalTests": 10,
        "averageTestScore": 88.5,
        "rank": 15,
        "percentile": 85,
        "strongTopics": ["Ancient History", "Medieval History"],
        "weakTopics": ["Modern History"],
        "trend": "improving"
      },
      {
        "subjectId": "subject_polity",
        "subjectName": "Indian Polity",
        "completionPercentage": 72.8,
        "timeSpent": 3600,
        "videosWatched": 22,
        "totalVideos": 30,
        "testsAttempted": 6,
        "totalTests": 8,
        "averageTestScore": 76.2,
        "rank": 28,
        "percentile": 72,
        "strongTopics": ["Fundamental Rights"],
        "weakTopics": ["Constitutional Framework", "Directive Principles"],
        "trend": "stable"
      }
    ],
    "weeklyProgress": [
      {
        "week": "2024-W03",
        "startDate": "2024-01-15",
        "endDate": "2024-01-21",
        "videosWatched": 8,
        "testsAttempted": 3,
        "timeSpent": 420,
        "averageScore": 85.3,
        "activeDays": 6
      }
    ],
    "achievements": [
      {
        "id": "achievement_week_warrior",
        "title": "Week Warrior",
        "description": "Studied for 7 consecutive days",
        "iconUrl": "https://cdn.example.com/achievements/week-warrior.png",
        "earnedAt": "2024-01-20T00:00:00Z",
        "points": 100
      }
    ],
    "peerComparison": {
      "overallRank": 45,
      "totalStudents": 1250,
      "percentile": 78,
      "averagePeerProgress": 62.3,
      "yourProgress": 68.5,
      "performanceBetter": 72.5
    }
  }
}
```

**Business Logic:**

- Calculate comprehensive progress across all content types
- Track time-based learning patterns and trends
- Compare performance with peer groups
- Identify strong and weak subject areas
- Generate achievement notifications

---

### **11.2 Get Subject-wise Performance Analysis**

**Endpoint:** `GET /api/analytics/student/subjects`

**Purpose:** Get detailed subject-wise performance analysis with insights

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
courseId: string (optional) - Filter by specific course
timeRange: string (default: "month") - Time range: week, month, quarter, year
includeTopics: boolean (default: true) - Include topic-wise breakdown
```

**Response:**

```json
{
  "success": true,
  "message": "Subject-wise performance retrieved successfully",
  "data": {
    "subjects": [
      {
        "subjectId": "subject_history",
        "subjectName": "History",
        "overallPerformance": {
          "completionPercentage": 85.2,
          "averageScore": 88.5,
          "timeSpent": 4800,
          "rank": 15,
          "percentile": 85,
          "grade": "A+",
          "trend": "improving",
          "improvementRate": 12.5
        },
        "contentBreakdown": {
          "videos": {
            "watched": 28,
            "total": 32,
            "completionRate": 87.5,
            "averageWatchTime": 95.2
          },
          "pdfs": {
            "opened": 12,
            "total": 15,
            "completionRate": 80.0,
            "averageReadTime": 125.5
          },
          "tests": {
            "attempted": 8,
            "total": 10,
            "completionRate": 80.0,
            "averageScore": 88.5,
            "bestScore": 96,
            "passRate": 100
          },
          "liveClasses": {
            "attended": 4,
            "total": 5,
            "attendanceRate": 80.0,
            "averageAttendanceTime": 85.5
          }
        },
        "topicPerformance": [
          {
            "topicId": "topic_ancient_history",
            "topicName": "Ancient History",
            "completionPercentage": 100,
            "averageScore": 92.0,
            "timeSpent": 1800,
            "difficulty": "medium",
            "mastery": "excellent",
            "lastStudied": "2024-01-18T14:30:00Z"
          },
          {
            "topicId": "topic_medieval_history",
            "topicName": "Medieval History",
            "completionPercentage": 85,
            "averageScore": 85.0,
            "timeSpent": 1500,
            "difficulty": "medium",
            "mastery": "good",
            "lastStudied": "2024-01-19T16:00:00Z"
          },
          {
            "topicId": "topic_modern_history",
            "topicName": "Modern History",
            "completionPercentage": 70,
            "averageScore": 78.0,
            "timeSpent": 1200,
            "difficulty": "hard",
            "mastery": "needs_improvement",
            "lastStudied": "2024-01-20T15:30:00Z"
          }
        ],
        "strengths": [
          "Excellent understanding of Ancient History concepts",
          "Consistent performance in chronological events",
          "Strong analytical skills in historical interpretation"
        ],
        "improvements": [
          "Focus more on Modern History topics",
          "Practice more questions on Freedom Struggle",
          "Review important dates and events"
        ],
        "recommendations": [
          {
            "type": "content",
            "title": "Watch Modern History Videos",
            "description": "Complete remaining Modern History video lectures",
            "priority": "high",
            "estimatedTime": 120
          },
          {
            "type": "practice",
            "title": "Take Modern History Test",
            "description": "Attempt practice tests to improve weak areas",
            "priority": "medium",
            "estimatedTime": 45
          }
        ]
      }
    ],
    "overallInsights": {
      "strongestSubject": {
        "subjectId": "subject_history",
        "subjectName": "History",
        "score": 88.5
      },
      "weakestSubject": {
        "subjectId": "subject_geography",
        "subjectName": "Geography",
        "score": 65.2
      },
      "mostImprovedSubject": {
        "subjectId": "subject_polity",
        "subjectName": "Indian Polity",
        "improvementRate": 15.8
      },
      "totalStudyTime": 15600,
      "averageScore": 78.5,
      "overallGrade": "B+",
      "studyEfficiency": 82.3
    }
  }
}
```

**Business Logic:**

- Analyze performance across all subjects
- Calculate topic-wise mastery levels
- Identify strengths and improvement areas
- Generate personalized recommendations
- Track learning efficiency and trends

### **11.3 Get Monthly Leaderboards**

**Endpoint:** `GET /api/analytics/leaderboards`

**Purpose:** Get monthly leaderboards based on quiz and test performance

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
type: string (default: "overall") - Leaderboard type: overall, daily_quiz, test_performance, study_time
examId: string (optional) - Filter by specific exam
month: string (default: current) - Month in YYYY-MM format
limit: number (default: 50) - Number of top performers to return
```

**Response:**

```json
{
  "success": true,
  "message": "Leaderboards retrieved successfully",
  "data": {
    "leaderboard": {
      "type": "overall",
      "title": "Overall Performance Leaderboard",
      "month": "2024-01",
      "examId": "bpsc",
      "examName": "BPSC",
      "lastUpdated": "2024-01-20T18:00:00Z"
    },
    "currentUser": {
      "rank": 45,
      "score": 1250,
      "percentile": 78,
      "change": "+5",
      "trend": "improving"
    },
    "topPerformers": [
      {
        "rank": 1,
        "userId": "user_top_1",
        "name": "Rajesh Kumar",
        "profileImage": "https://cdn.example.com/profiles/user-top-1.jpg",
        "score": 2850,
        "metrics": {
          "testsCompleted": 45,
          "averageScore": 92.5,
          "studyHours": 120,
          "streakDays": 28
        },
        "badges": [
          {
            "id": "badge_top_performer",
            "title": "Top Performer",
            "iconUrl": "https://cdn.example.com/badges/top-performer.png"
          }
        ],
        "change": "0",
        "trend": "stable"
      },
      {
        "rank": 2,
        "userId": "user_top_2",
        "name": "Priya Sharma",
        "profileImage": "https://cdn.example.com/profiles/user-top-2.jpg",
        "score": 2720,
        "metrics": {
          "testsCompleted": 42,
          "averageScore": 90.8,
          "studyHours": 115,
          "streakDays": 25
        },
        "badges": [
          {
            "id": "badge_consistent_learner",
            "title": "Consistent Learner",
            "iconUrl": "https://cdn.example.com/badges/consistent-learner.png"
          }
        ],
        "change": "+1",
        "trend": "improving"
      }
    ],
    "nearbyRanks": [
      {
        "rank": 43,
        "userId": "user_nearby_1",
        "name": "Amit Singh",
        "score": 1280,
        "change": "-2"
      },
      {
        "rank": 44,
        "userId": "user_nearby_2",
        "name": "Neha Gupta",
        "score": 1265,
        "change": "+3"
      },
      {
        "rank": 45,
        "userId": "current_user",
        "name": "You",
        "score": 1250,
        "change": "+5",
        "isCurrentUser": true
      },
      {
        "rank": 46,
        "userId": "user_nearby_3",
        "name": "Vikash Kumar",
        "score": 1235,
        "change": "-1"
      }
    ],
    "leaderboardTypes": [
      {
        "type": "overall",
        "title": "Overall Performance",
        "description": "Based on test scores, study time, and consistency"
      },
      {
        "type": "daily_quiz",
        "title": "Daily Quiz Champion",
        "description": "Based on daily quiz performance and streaks"
      },
      {
        "type": "test_performance",
        "title": "Test Master",
        "description": "Based on test scores and completion rate"
      },
      {
        "type": "study_time",
        "title": "Study Warrior",
        "description": "Based on total study hours and consistency"
      }
    ],
    "statistics": {
      "totalParticipants": 1250,
      "averageScore": 850,
      "topScore": 2850,
      "yourPercentile": 78,
      "improvementPossible": 22
    }
  }
}
```

**Business Logic:**

- Calculate leaderboard rankings based on multiple metrics
- Track monthly performance changes and trends
- Show nearby ranks for competitive motivation
- Support different leaderboard categories
- Include achievement badges and recognition

---

### **11.4 Get Completion Badges and Achievements**

**Endpoint:** `GET /api/analytics/student/badges`

**Purpose:** Get student's earned badges and available achievements

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
status: string (default: "all") - Filter by status: earned, available, locked
category: string (optional) - Filter by category: progress, performance, consistency, special
```

**Response:**

```json
{
  "success": true,
  "message": "Badges and achievements retrieved successfully",
  "data": {
    "earnedBadges": [
      {
        "id": "badge_first_test",
        "title": "First Test Completed",
        "description": "Completed your first test with 80%+ score",
        "category": "progress",
        "iconUrl": "https://cdn.example.com/badges/first-test.png",
        "rarity": "common",
        "points": 50,
        "earnedAt": "2024-01-02T15:30:00Z",
        "progress": {
          "current": 1,
          "required": 1,
          "percentage": 100
        }
      },
      {
        "id": "badge_week_warrior",
        "title": "Week Warrior",
        "description": "Studied for 7 consecutive days",
        "category": "consistency",
        "iconUrl": "https://cdn.example.com/badges/week-warrior.png",
        "rarity": "uncommon",
        "points": 100,
        "earnedAt": "2024-01-15T00:00:00Z",
        "progress": {
          "current": 7,
          "required": 7,
          "percentage": 100
        }
      },
      {
        "id": "badge_subject_master",
        "title": "History Master",
        "description": "Achieved 90%+ average in History subject",
        "category": "performance",
        "iconUrl": "https://cdn.example.com/badges/history-master.png",
        "rarity": "rare",
        "points": 200,
        "earnedAt": "2024-01-18T20:00:00Z",
        "progress": {
          "current": 92.5,
          "required": 90,
          "percentage": 100
        }
      }
    ],
    "availableBadges": [
      {
        "id": "badge_month_champion",
        "title": "Month Champion",
        "description": "Study for 30 consecutive days",
        "category": "consistency",
        "iconUrl": "https://cdn.example.com/badges/month-champion.png",
        "rarity": "epic",
        "points": 500,
        "isLocked": false,
        "progress": {
          "current": 12,
          "required": 30,
          "percentage": 40
        },
        "estimatedCompletion": "2024-02-15T00:00:00Z"
      },
      {
        "id": "badge_perfect_score",
        "title": "Perfect Score",
        "description": "Score 100% in any test",
        "category": "performance",
        "iconUrl": "https://cdn.example.com/badges/perfect-score.png",
        "rarity": "legendary",
        "points": 1000,
        "isLocked": false,
        "progress": {
          "current": 96,
          "required": 100,
          "percentage": 96
        },
        "tips": [
          "Review all topics thoroughly before attempting tests",
          "Practice more MCQ questions",
          "Focus on time management during tests"
        ]
      }
    ],
    "lockedBadges": [
      {
        "id": "badge_course_completion",
        "title": "Course Completion Master",
        "description": "Complete 5 full courses with 85%+ score",
        "category": "progress",
        "iconUrl": "https://cdn.example.com/badges/course-master.png",
        "rarity": "legendary",
        "points": 2000,
        "isLocked": true,
        "unlockRequirement": "Complete at least 2 courses first",
        "progress": {
          "current": 1,
          "required": 5,
          "percentage": 20
        }
      }
    ],
    "summary": {
      "totalBadges": 25,
      "earnedBadges": 8,
      "availableBadges": 12,
      "lockedBadges": 5,
      "totalPoints": 1250,
      "nextMilestone": {
        "points": 1500,
        "reward": "Premium Badge Collection"
      },
      "rarityBreakdown": {
        "common": 3,
        "uncommon": 2,
        "rare": 2,
        "epic": 1,
        "legendary": 0
      }
    },
    "recentAchievements": [
      {
        "id": "badge_subject_master",
        "title": "History Master",
        "earnedAt": "2024-01-18T20:00:00Z",
        "isNew": true
      }
    ]
  }
}
```

**Business Logic:**

- Track badge progress and completion criteria
- Categorize badges by type and rarity
- Provide tips for earning difficult badges
- Calculate achievement points and milestones
- Show recent achievements for motivation

---

### **11.5 Get Comparative Analytics**

**Endpoint:** `GET /api/analytics/student/comparison`

**Purpose:** Get comparative analytics with peer groups and benchmarks

**Headers:**

```
Authorization: Bearer <access_token>
```

**Query Parameters:**

```
comparisonType: string (default: "peers") - Type: peers, top_performers, course_average
examId: string (optional) - Filter by specific exam
timeRange: string (default: "month") - Time range: week, month, quarter
```

**Response:**

```json
{
  "success": true,
  "message": "Comparative analytics retrieved successfully",
  "data": {
    "userPerformance": {
      "overallScore": 78.5,
      "rank": 45,
      "percentile": 78,
      "studyHours": 156,
      "testsCompleted": 28,
      "averageTestScore": 82.5
    },
    "peerComparison": {
      "averagePeerScore": 72.3,
      "yourAdvantage": 6.2,
      "betterThanPeers": 78,
      "peerGroup": "BPSC Aspirants",
      "totalPeers": 1250
    },
    "topPerformersComparison": {
      "top10AverageScore": 95.2,
      "yourGap": 16.7,
      "improvementNeeded": 17.5,
      "topPerformerInsights": [
        "Top performers study 3.2x more hours daily",
        "They maintain 95%+ test completion rate",
        "Average study streak of 25+ days"
      ]
    },
    "subjectComparison": [
      {
        "subjectId": "subject_history",
        "subjectName": "History",
        "yourScore": 88.5,
        "peerAverage": 75.2,
        "topPerformerAverage": 94.8,
        "yourRank": 15,
        "performance": "above_average",
        "gap": 6.3
      },
      {
        "subjectId": "subject_geography",
        "subjectName": "Geography",
        "yourScore": 65.2,
        "peerAverage": 68.5,
        "topPerformerAverage": 89.2,
        "yourRank": 85,
        "performance": "below_average",
        "gap": -3.3
      }
    ],
    "improvementOpportunities": [
      {
        "area": "Geography",
        "currentScore": 65.2,
        "targetScore": 75.0,
        "improvementNeeded": 9.8,
        "estimatedTime": "2 weeks",
        "recommendations": [
          "Complete remaining Geography video lectures",
          "Practice 50+ Geography MCQs",
          "Take 3 Geography mock tests"
        ]
      }
    ],
    "strengthAreas": [
      {
        "area": "History",
        "currentScore": 88.5,
        "peerAdvantage": 13.3,
        "rank": 15,
        "insight": "You're performing exceptionally well in History. Consider helping peers or focusing on weaker subjects."
      }
    ],
    "studyPatterns": {
      "yourDailyAverage": 2.5,
      "peerDailyAverage": 2.1,
      "topPerformerAverage": 4.2,
      "yourStudyEfficiency": 82.3,
      "peerEfficiency": 76.8,
      "recommendations": [
        "Increase daily study time by 30 minutes",
        "Focus on consistent daily practice",
        "Improve study efficiency through better planning"
      ]
    }
  }
}
```

**Business Logic:**

- Compare performance with relevant peer groups
- Identify improvement opportunities and strengths
- Provide actionable insights and recommendations
- Track relative performance trends
- Calculate study efficiency and patterns

## 📋 **PERFORMANCE & ANALYTICS MODULE API IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 12 (Performance & Analytics Module):**

1. **`GET /api/analytics/student/progress`** - Get comprehensive student progress analytics (PRIMARY)
2. **`GET /api/analytics/student/subjects`** - Get detailed subject-wise performance analysis
3. **`GET /api/analytics/leaderboards`** - Get monthly leaderboards and rankings
4. **`GET /api/analytics/student/badges`** - Get completion badges and achievements
5. **`GET /api/analytics/student/comparison`** - Get comparative analytics with peers

### **🏗️ Database Schema Requirements:**

#### **User Analytics Table:**

```sql
CREATE TABLE user_analytics (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  date DATE NOT NULL,
  videos_watched INT DEFAULT 0,
  video_watch_time INT DEFAULT 0, -- in seconds
  pdfs_opened INT DEFAULT 0,
  pdf_read_time INT DEFAULT 0, -- in seconds
  tests_attempted INT DEFAULT 0,
  tests_completed INT DEFAULT 0,
  total_test_score INT DEFAULT 0,
  live_classes_attended INT DEFAULT 0,
  live_class_time INT DEFAULT 0, -- in seconds
  total_study_time INT DEFAULT 0, -- in seconds
  daily_quiz_attempted BOOLEAN DEFAULT false,
  daily_quiz_score INT DEFAULT 0,
  streak_maintained BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  UNIQUE KEY unique_user_date (user_id, date)
);
```

#### **Subject Performance Table:**

```sql
CREATE TABLE subject_performance (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  subject_id VARCHAR(36) NOT NULL,
  course_id VARCHAR(36) NOT NULL,
  videos_watched INT DEFAULT 0,
  total_videos INT DEFAULT 0,
  pdfs_opened INT DEFAULT 0,
  total_pdfs INT DEFAULT 0,
  tests_attempted INT DEFAULT 0,
  total_tests INT DEFAULT 0,
  total_test_score INT DEFAULT 0,
  best_test_score INT DEFAULT 0,
  time_spent INT DEFAULT 0, -- in seconds
  mastery_level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'beginner',
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  last_studied_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (subject_id) REFERENCES subjects(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  UNIQUE KEY unique_user_subject_course (user_id, subject_id, course_id)
);
```

#### **Leaderboards Table:**

```sql
CREATE TABLE leaderboards (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  exam_id VARCHAR(36) NOT NULL,
  leaderboard_type ENUM('overall', 'daily_quiz', 'test_performance', 'study_time') NOT NULL,
  month VARCHAR(7) NOT NULL, -- YYYY-MM format
  score INT NOT NULL,
  rank_position INT NOT NULL,
  percentile DECIMAL(5,2) NOT NULL,
  metrics JSON, -- Additional metrics based on leaderboard type
  calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (exam_id) REFERENCES exams(id),
  UNIQUE KEY unique_user_exam_type_month (user_id, exam_id, leaderboard_type, month)
);
```

#### **Badges Table:**

```sql
CREATE TABLE badges (
  id VARCHAR(36) PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  category ENUM('progress', 'performance', 'consistency', 'special') NOT NULL,
  rarity ENUM('common', 'uncommon', 'rare', 'epic', 'legendary') DEFAULT 'common',
  icon_url VARCHAR(500) NOT NULL,
  points INT DEFAULT 0,
  criteria JSON NOT NULL, -- Badge earning criteria
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **User Badges Table:**

```sql
CREATE TABLE user_badges (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  badge_id VARCHAR(36) NOT NULL,
  progress_current DECIMAL(10,2) DEFAULT 0,
  progress_required DECIMAL(10,2) NOT NULL,
  is_earned BOOLEAN DEFAULT false,
  earned_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (badge_id) REFERENCES badges(id),
  UNIQUE KEY unique_user_badge (user_id, badge_id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Analytics Data Processing:**

- Real-time analytics calculation and aggregation
- Daily batch processing for performance metrics
- Efficient data aggregation for large datasets
- Time-series data analysis for trends

#### **Leaderboard System:**

- Monthly leaderboard calculation and ranking
- Multiple leaderboard categories with different scoring
- Real-time rank updates and notifications
- Peer group comparison and percentile calculation

#### **Badge System:**

- Dynamic badge progress tracking
- Automated badge awarding based on criteria
- Rarity-based point system and rewards
- Achievement notification system

#### **Performance Optimization:**

- Index on user_id, date, and subject_id fields
- Use Redis for caching leaderboard data
- Implement efficient aggregation queries
- Optimize database queries with proper indexing

#### **Security Considerations:**

- Validate user access to analytics data
- Implement rate limiting on analytics queries
- Secure leaderboard data integrity
- Track analytics access for audit

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- **Progress Charts**: Visual progress tracking with charts and graphs
- **Subject Analysis**: Detailed subject-wise performance breakdown
- **Leaderboards**: Competitive rankings with peer comparison
- **Badge Collection**: Achievement system with progress tracking
- **Comparative Analytics**: Peer comparison and improvement insights

### **🚀 Implementation Priority:**

1. **HIGH**: Student progress analytics (core user engagement)
2. **HIGH**: Subject-wise performance analysis (learning insights)
3. **MEDIUM**: Leaderboards and rankings (competitive motivation)
4. **MEDIUM**: Badge system and achievements (gamification)
5. **LOW**: Advanced comparative analytics (optimization features)

### **📊 Analytics Features Implementation:**

#### **Student-wise Progress Charts:**

- **Video Progress**: % videos watched with time tracking
- **PDF Progress**: PDFs opened with reading time analysis
- **Test Progress**: Tests attempted with score trends
- **Overall Progress**: Combined progress across all content types

#### **Subject-wise Performance:**

- **Completion Tracking**: Progress percentage by subject
- **Score Analysis**: Average scores and improvement trends
- **Time Distribution**: Study time allocation across subjects
- **Mastery Levels**: Subject expertise classification

#### **Monthly Leaderboards:**

- **Overall Performance**: Combined metrics ranking
- **Daily Quiz Champions**: Quiz performance and streaks
- **Test Masters**: Test scores and completion rates
- **Study Warriors**: Study time and consistency

#### **Completion Badges:**

- **Progress Badges**: Course and content completion milestones
- **Performance Badges**: Score-based achievements
- **Consistency Badges**: Study streak and regularity rewards
- **Special Badges**: Unique achievements and recognitions

**Ready for Backend Implementation!** 🎯
