import 'dart:async';
import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/custom_button.dart';
import 'test_result_screen.dart';

class TestScreen extends StatefulWidget {
  final Map<String, dynamic> test;
  final String subjectName;
  final String courseName;

  const TestScreen({
    super.key,
    required this.test,
    required this.subjectName,
    required this.courseName,
  });

  @override
  State<TestScreen> createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  int _currentQuestionIndex = 0;
  Map<int, dynamic> _answers = {};
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isTestCompleted = false;
  bool _showExplanation = false;

  // Sample questions data
  List<Map<String, dynamic>> _questions = [];

  @override
  void initState() {
    super.initState();
    _loadQuestions();
    _startTimer();
  }

  void _loadQuestions() {
    // Sample questions based on test type
    _questions = [
      {
        'type': 'MCQ',
        'question': 'What is the fundamental structure of the Indian Constitution?',
        'options': [
          'Parliamentary System',
          'Federal System',
          'Secular Nature',
          'All of the above'
        ],
        'correctAnswer': 3,
        'explanation': 'The fundamental structure includes parliamentary system, federal system, secular nature, and other basic features that cannot be altered.',
        'marks': 2,
      },
      {
        'type': 'True/False',
        'question': 'The President of India is directly elected by the people.',
        'options': ['True', 'False'],
        'correctAnswer': 1,
        'explanation': 'False. The President is elected by an electoral college consisting of elected members of both houses of Parliament and state assemblies.',
        'marks': 1,
      },
      {
        'type': 'Fill in the Blanks',
        'question': 'Article _____ of the Indian Constitution deals with Right to Equality.',
        'correctAnswer': '14',
        'explanation': 'Article 14 guarantees equality before law and equal protection of laws.',
        'marks': 2,
      },
      {
        'type': 'MCQ',
        'question': 'Which of the following is NOT a Fundamental Right?',
        'options': [
          'Right to Education',
          'Right to Property',
          'Right to Freedom',
          'Right to Constitutional Remedies'
        ],
        'correctAnswer': 1,
        'explanation': 'Right to Property was removed from Fundamental Rights by the 44th Amendment in 1978.',
        'marks': 2,
      },
      {
        'type': 'True/False',
        'question': 'Fundamental Duties were added to the Constitution by the 42nd Amendment.',
        'options': ['True', 'False'],
        'correctAnswer': 0,
        'explanation': 'True. Fundamental Duties were added by the 42nd Constitutional Amendment in 1976.',
        'marks': 1,
      },
    ];

    _remainingSeconds = (widget.test['duration'] ?? 30) * 60; // Convert minutes to seconds
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds > 0) {
        setState(() {
          _remainingSeconds--;
        });
      } else {
        _submitTest();
      }
    });
  }

  void _selectAnswer(dynamic answer) {
    setState(() {
      _answers[_currentQuestionIndex] = answer;
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _showExplanation = false;
      });
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
        _showExplanation = false;
      });
    }
  }

  void _goToQuestion(int index) {
    setState(() {
      _currentQuestionIndex = index;
      _showExplanation = false;
    });
    Navigator.pop(context); // Close question navigator
  }

  void _submitTest() {
    _timer?.cancel();
    setState(() {
      _isTestCompleted = true;
    });

    // Calculate score
    int totalMarks = 0;
    int obtainedMarks = 0;

    for (int i = 0; i < _questions.length; i++) {
      final question = _questions[i];
      totalMarks += (question['marks'] as int);

      if (_answers.containsKey(i)) {
        final userAnswer = _answers[i];
        final correctAnswer = question['correctAnswer'];

        bool isCorrect = false;
        if (question['type'] == 'Fill in the Blanks') {
          isCorrect = userAnswer.toString().toLowerCase().trim() == 
                     correctAnswer.toString().toLowerCase().trim();
        } else {
          isCorrect = userAnswer == correctAnswer;
        }

        if (isCorrect) {
          obtainedMarks += (question['marks'] as int);
        }
      }
    }

    // Navigate to result screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => TestResultScreen(
          test: widget.test,
          questions: _questions,
          answers: _answers,
          obtainedMarks: obtainedMarks,
          totalMarks: totalMarks,
          timeTaken: (widget.test['duration'] * 60) - _remainingSeconds,
          subjectName: widget.subjectName,
          courseName: widget.courseName,
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    if (_questions.isEmpty) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final currentQuestion = _questions[_currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.test['title']),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          // Timer
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: _remainingSeconds < 300 ? AppColors.error : AppColors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _formatTime(_remainingSeconds),
              style: const TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Question Navigator
          IconButton(
            icon: const Icon(Icons.list),
            onPressed: _showQuestionNavigator,
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress Bar
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${currentQuestion['marks']} marks',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: (_currentQuestionIndex + 1) / _questions.length,
                  backgroundColor: AppColors.greyLight,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ],
            ),
          ),

          // Question Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question Type Badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getQuestionTypeColor(currentQuestion['type']),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      currentQuestion['type'],
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Question Text
                  Text(
                    currentQuestion['question'],
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      height: 1.4,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Answer Options
                  _buildAnswerOptions(currentQuestion),

                  const SizedBox(height: 24),

                  // Explanation (if shown)
                  if (_showExplanation)
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.info.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.info.withOpacity(0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.lightbulb, color: AppColors.info, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'Explanation',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.info,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            currentQuestion['explanation'],
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              border: Border(top: BorderSide(color: AppColors.greyLight)),
            ),
            child: Row(
              children: [
                if (_currentQuestionIndex > 0)
                  Expanded(
                    child: CustomButton(
                      text: 'Previous',
                      onPressed: _previousQuestion,
                      backgroundColor: AppColors.greyDark,
                      icon: Icons.chevron_left,
                    ),
                  ),
                if (_currentQuestionIndex > 0) const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: _showExplanation ? 'Hide Explanation' : 'Show Explanation',
                    onPressed: () {
                      setState(() {
                        _showExplanation = !_showExplanation;
                      });
                    },
                    backgroundColor: AppColors.info,
                    icon: Icons.lightbulb,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: _currentQuestionIndex == _questions.length - 1 ? 'Submit' : 'Next',
                    onPressed: _currentQuestionIndex == _questions.length - 1 
                        ? _submitTest 
                        : _nextQuestion,
                    backgroundColor: _currentQuestionIndex == _questions.length - 1 
                        ? AppColors.success 
                        : AppColors.primary,
                    icon: _currentQuestionIndex == _questions.length - 1 
                        ? Icons.check 
                        : Icons.chevron_right,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOptions(Map<String, dynamic> question) {
    switch (question['type']) {
      case 'MCQ':
      case 'True/False':
        return _buildMultipleChoiceOptions(question);
      case 'Fill in the Blanks':
        return _buildFillInTheBlanksOption(question);
      default:
        return const SizedBox();
    }
  }

  Widget _buildMultipleChoiceOptions(Map<String, dynamic> question) {
    final options = question['options'] as List<String>;
    final selectedAnswer = _answers[_currentQuestionIndex];

    return Column(
      children: options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = selectedAnswer == index;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: InkWell(
            onTap: () => _selectAnswer(index),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? AppColors.primary : AppColors.greyLight,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected ? AppColors.primary : Colors.transparent,
                      border: Border.all(
                        color: isSelected ? AppColors.primary : AppColors.grey,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: AppColors.white, size: 16)
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      '${String.fromCharCode(65 + index)}. $option',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        color: isSelected ? AppColors.primary : AppColors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFillInTheBlanksOption(Map<String, dynamic> question) {
    final controller = TextEditingController();
    if (_answers.containsKey(_currentQuestionIndex)) {
      controller.text = _answers[_currentQuestionIndex].toString();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.greyLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Answer:',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: controller,
            onChanged: (value) => _selectAnswer(value),
            decoration: InputDecoration(
              hintText: 'Enter your answer here...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.primary, width: 2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getQuestionTypeColor(String type) {
    switch (type) {
      case 'MCQ':
        return AppColors.primary;
      case 'True/False':
        return AppColors.secondary;
      case 'Fill in the Blanks':
        return AppColors.accent;
      default:
        return AppColors.grey;
    }
  }

  void _showQuestionNavigator() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Question Navigator',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 1,
                ),
                itemCount: _questions.length,
                itemBuilder: (context, index) {
                  final isAnswered = _answers.containsKey(index);
                  final isCurrent = index == _currentQuestionIndex;

                  return InkWell(
                    onTap: () => _goToQuestion(index),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isCurrent
                            ? AppColors.primary
                            : isAnswered
                                ? AppColors.success
                                : AppColors.greyLight,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: TextStyle(
                            color: isCurrent || isAnswered
                                ? AppColors.white
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildLegendItem(AppColors.primary, 'Current'),
                const SizedBox(width: 16),
                _buildLegendItem(AppColors.success, 'Answered'),
                const SizedBox(width: 16),
                _buildLegendItem(AppColors.greyLight, 'Not Answered'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
