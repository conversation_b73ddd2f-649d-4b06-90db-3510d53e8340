```markdown
# 📱 Utkrishta Classes - Android App Feature Document

## 🏫 About the App

**Utkrishta Classes** is an online/offline hybrid coaching platform focused on:

- Competitive Exams (BPSC, SSC, Banking, Railway, CTET, etc.)
- Bihar Board Academic Exams (10th, 12th)

This app is designed for Android (later scalable to iOS) with a clean UI/UX and smart navigation that allows students to learn with structure, clarity, and personalization.

---

## 🎯 Project Goals

- Deliver structured content hierarchy: `Exam ➝ Course ➝ Subject ➝ Class`
- Enable seamless access to:
  - Video lectures
  - Study PDFs
  - Topic-wise MCQ tests
- Track learning progress
- Provide both free and paid course models

---

## 🔗 Content Navigation Flow
```

Home ➝ Select Exam ➝ View Courses ➝ Subjects ➝ \[Videos | PDFs | Tests]

````

---

## 📦 Feature Set

### 1. 📍 Home Screen

- Dropdown or Grid UI for **Exam Categories**
- Banner Slider for:
  - Announcements
  - New Courses
  - Special Offers
- Quick access to:
  - My Courses
  - Live Class
  - Daily Quiz
  - Test Series

---

### 2. 🧭 Exam & Course Hierarchy

#### Step 1: Exam Selection
- BPSC
- SSC
- CTET
- Railway
- Banking
- Bihar Board 10th
- Bihar Board 12th

#### Step 2: Course List (Dynamic based on Exam)
- BPSC 70th Prelims
- SSC CGL Tier 1
- Bihar Board 10th Science
- Bihar Board 12th Math
- etc.

#### Step 3: Subject List
- Based on Course (e.g., for BPSC: Polity, History, Economy, etc.)

#### Step 4: Class Content
Each subject has:
- 📹 Video Lectures (with title, duration, progress tracking)
- 📄 PDFs/Notes (PDF viewer + download)
- 🧪 MCQ Tests (topic-based)

---

### 3. 📹 Video Module

- Upload via Admin Panel
- Streaming player with:
  - Resume playback
  - Progress bar
  - Bookmark option
  - Offline download (optional for paid users)
- Videos tagged with:
  - Class Name
  - Subject
  - Topic
  - Duration

---

### 4. 📄 PDF Notes Module

- Upload PDF to specific topic/class
- Display via in-app PDF viewer
- Allow download (admin-configurable)
- Notes categorized:
  - By Subject
  - By Class/Topic

---

### 5. 🧪 Test Module

- Question Types:
  - MCQ
  - True/False
  - Fill in the Blanks (optional)
- Sections:
  - Subject-wise
  - Topic-wise
  - Full Mock Test
- Timer
- Score analysis
- Show correct answers + explanations
- Store user performance in database
- Enable Test Retake

---

### 6. 📅 Live Classes (Optional)

- Scheduled class calendar
- Zoom/Jitsi integration
- Push reminders
- Link embedded in respective topic/class

---

### 7. 🧑‍🎓 Student Dashboard

- View Purchased/Free Courses
- Track Class Completion
- Attempted Tests with Score History
- Bookmark Section (Saved Videos/Notes)
- Notifications Section
- Doubt Corner (Chat-based or comment system)

---

### 8. 🧑‍🏫 Admin Panel (Web-Based)

- Add/Edit/Delete:
  - Exams
  - Courses
  - Subjects
  - Classes (Video/PDF/Test)
- Upload videos (YouTube, Cloud, or local)
- Upload PDFs
- Create/Import MCQ Test Sets
- Schedule Live Classes
- View student analytics (attendance, test performance, course completion)
- Push notifications to students

---

### 9. 🔔 Notifications

- Firebase Push Notifications for:
  - New Class Upload
  - Upcoming Live Class
  - Test Reminders
  - Motivational Quotes / Tips
- Grouped by exam/course

---

### 10. 🔐 User Authentication

- Firebase Auth
- OTP Login via mobile number
- Device Binding (limit login to 1–2 devices)
- Profile Management:
  - Name
  - Email
  - Exam preference
  - Language (Hindi/English)

---

### 11. 💳 Payment Gateway

- Razorpay/Stripe Integration
- Payment Flow:
  - Course ➝ Price ➝ Buy ➝ Payment ➝ Unlock content
- Invoice Generation
- Referral Code System (optional)
- Free + Paid + EMI Course Structure

---

### 12. 📊 Performance & Analytics

- Student-wise progress chart:
  - % video watched
  - PDFs opened
  - Tests attempted
- Subject-wise performance
- Monthly Leaderboard (Daily quiz, Test performance)
- Completion Badges

---

### 13. 💡 Smart Add-ons (For Later Phase)

- AI-based course recommendation based on performance
- Daily Study Reminders
- Chatbot for general FAQs
- Voice Search for class names or topics
- Dark Mode UI

---

## 🧰 Suggested Tech Stack

| Layer            | Technology                              |
|------------------|------------------------------------------|
| Frontend (App)   | Flutter / React Native                  |
| Backend          | Node.js + Express + MongoDB / PostgreSQL |
| Authentication   | Firebase Auth                           |
| Video Hosting    | Firebase Storage / AWS S3 / Vimeo API   |
| PDF Rendering    | In-App Viewer (flutter_pdfview)         |
| Test Engine      | Custom JSON-based engine or Firestore   |
| Admin Panel      | React.js Web Panel                      |
| Notifications    | Firebase Cloud Messaging (FCM)          |
| Payments         | Razorpay / Stripe                       |

---

## 📂 Sample Data Structure

```json
{
  "exam": "BPSC",
  "course": "70th BPSC Prelims",
  "subject": "Indian Polity",
  "classes": [
    {
      "title": "Fundamental Rights",
      "video_url": "https://...",
      "pdf_url": "https://...",
      "test_id": "test_12345"
    }
  ]
}
````

---

## 🚀 MVP Scope (Minimum Viable Product)

- Home Screen with Exam Selection
- Course ➝ Subject ➝ Class Navigation
- Video + PDF Display
- Basic MCQ Test Functionality
- User Login + Dashboard
- Admin Upload Panel
- Firebase Notification Integration

---

## 📘 Naming Guidelines

- App Name: **Utkrishta Classes**
- Package: `com.utkrishta.app`
- Logo: Orange/Blue Book + Torch Concept (symbolizing excellence)
- Font: Clean Sans Serif (for education brand)

---

## 🧠 Notes for AI / Developer

- Maintain modularity (each module isolated)
- Code comments & documentation mandatory
- Use version control with Git/GitHub
- Keep future scalability in mind (e.g., iOS support, AI integration)

---
