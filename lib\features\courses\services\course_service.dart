import '../../../core/services/api_service.dart';
import '../../../core/constants/app_constants.dart';
import '../models/course_model.dart';

class CourseService {
  static const String _coursesEndpoint = AppConstants.coursesEndpoint;
  static const String _enrollEndpoint = '/enroll';
  static const String _unenrollEndpoint = '/unenroll';

  // Get All Courses
  static Future<ApiResponse> getAllCourses({
    String? examId,
    int page = 1,
    int limit = 10,
    String? search,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (examId != null) queryParams['examId'] = examId;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (sortBy != null) queryParams['sortBy'] = sortBy;
      if (sortOrder != null) queryParams['sortOrder'] = sortOrder;

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_coursesEndpoint?$queryString',
        requiresAuth: false,
      );

      if (response.success) {
        final courses = (response.data['courses'] as List<dynamic>)
            .map((course) => CourseModel.fromJson(course))
            .toList();

        return ApiResponse.success(
          data: {
            'courses': courses,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch courses: ${e.toString()}',
      );
    }
  }

  // Get Course by ID
  static Future<ApiResponse> getCourseById(String courseId) async {
    try {
      final response = await ApiService.get(
        '$_coursesEndpoint/$courseId',
        requiresAuth: false,
      );

      if (response.success) {
        final course = CourseModel.fromJson(response.data['course']);
        
        return ApiResponse.success(
          data: course,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch course details: ${e.toString()}',
      );
    }
  }

  // Get Enrolled Courses
  static Future<ApiResponse> getEnrolledCourses({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_coursesEndpoint/enrolled?$queryString',
      );

      if (response.success) {
        final courses = (response.data['courses'] as List<dynamic>)
            .map((course) => CourseModel.fromJson(course))
            .toList();

        return ApiResponse.success(
          data: {
            'courses': courses,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch enrolled courses: ${e.toString()}',
      );
    }
  }

  // Get Recommended Courses
  static Future<ApiResponse> getRecommendedCourses({
    int limit = 5,
  }) async {
    try {
      final response = await ApiService.get(
        '$_coursesEndpoint/recommended?limit=$limit',
      );

      if (response.success) {
        final courses = (response.data['courses'] as List<dynamic>)
            .map((course) => CourseModel.fromJson(course))
            .toList();

        return ApiResponse.success(
          data: courses,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch recommended courses: ${e.toString()}',
      );
    }
  }

  // Enroll in Course
  static Future<ApiResponse> enrollInCourse(String courseId) async {
    try {
      final response = await ApiService.post(
        '$_coursesEndpoint/$courseId$_enrollEndpoint',
        body: {},
      );

      if (response.success) {
        return ApiResponse.success(
          data: response.data,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to enroll in course: ${e.toString()}',
      );
    }
  }

  // Unenroll from Course
  static Future<ApiResponse> unenrollFromCourse(String courseId) async {
    try {
      final response = await ApiService.post(
        '$_coursesEndpoint/$courseId$_unenrollEndpoint',
        body: {},
      );

      if (response.success) {
        return ApiResponse.success(
          data: response.data,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to unenroll from course: ${e.toString()}',
      );
    }
  }

  // Get Course Progress
  static Future<ApiResponse> getCourseProgress(String courseId) async {
    try {
      final response = await ApiService.get(
        '$_coursesEndpoint/$courseId/progress',
      );

      if (response.success) {
        final progress = UserProgress.fromJson(response.data['progress']);
        
        return ApiResponse.success(
          data: progress,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch course progress: ${e.toString()}',
      );
    }
  }

  // Get Course Subjects
  static Future<ApiResponse> getCourseSubjects(String courseId) async {
    try {
      final response = await ApiService.get(
        '$_coursesEndpoint/$courseId/subjects',
      );

      if (response.success) {
        final subjects = (response.data['subjects'] as List<dynamic>)
            .map((subject) => SubjectModel.fromJson(subject))
            .toList();

        return ApiResponse.success(
          data: subjects,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch course subjects: ${e.toString()}',
      );
    }
  }

  // Search Courses
  static Future<ApiResponse> searchCourses({
    required String query,
    String? examId,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, String>{
        'q': query,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (examId != null) queryParams['examId'] = examId;

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '${AppConstants.searchEndpoint}/courses?$queryString',
        requiresAuth: false,
      );

      if (response.success) {
        final courses = (response.data['courses'] as List<dynamic>)
            .map((course) => CourseModel.fromJson(course))
            .toList();

        return ApiResponse.success(
          data: {
            'courses': courses,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to search courses: ${e.toString()}',
      );
    }
  }

  // Get Popular Courses
  static Future<ApiResponse> getPopularCourses({
    int limit = 10,
  }) async {
    try {
      final response = await ApiService.get(
        '$_coursesEndpoint/popular?limit=$limit',
        requiresAuth: false,
      );

      if (response.success) {
        final courses = (response.data['courses'] as List<dynamic>)
            .map((course) => CourseModel.fromJson(course))
            .toList();

        return ApiResponse.success(
          data: courses,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch popular courses: ${e.toString()}',
      );
    }
  }

  // Get Free Courses
  static Future<ApiResponse> getFreeCourses({
    String? examId,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
        'isFree': 'true',
      };

      if (examId != null) queryParams['examId'] = examId;

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_coursesEndpoint?$queryString',
        requiresAuth: false,
      );

      if (response.success) {
        final courses = (response.data['courses'] as List<dynamic>)
            .map((course) => CourseModel.fromJson(course))
            .toList();

        return ApiResponse.success(
          data: {
            'courses': courses,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch free courses: ${e.toString()}',
      );
    }
  }
}
