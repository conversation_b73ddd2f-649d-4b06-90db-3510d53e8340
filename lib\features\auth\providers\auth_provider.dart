import 'package:flutter/foundation.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/user_service.dart';

class AuthProvider extends ChangeNotifier {
  // State
  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _error;
  bool _isLoading = false;

  // OTP related
  String? _otpPhone;
  bool _isOtpSent = false;
  int _otpExpiresIn = 0;

  // Getters
  AuthState get state => _state;
  UserModel? get user => _user;
  String? get error => _error;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _state == AuthState.authenticated && _user != null;
  String? get otpPhone => _otpPhone;
  bool get isOtpSent => _isOtpSent;
  int get otpExpiresIn => _otpExpiresIn;

  // Initialize - Check if user is already logged in
  Future<void> initialize() async {
    try {
      _setLoading(true);
      
      final isAuth = await AuthService.isAuthenticated();
      if (isAuth) {
        final user = await AuthService.getCurrentUser();
        if (user != null) {
          _user = user;
          _setState(AuthState.authenticated);
        } else {
          _setState(AuthState.unauthenticated);
        }
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('Failed to initialize: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Send OTP
  Future<bool> sendOtp(String phone) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await AuthService.sendOtp(phone);
      
      if (response.success) {
        final otpResponse = response.data as SendOtpResponse;
        _otpPhone = otpResponse.phone;
        _otpExpiresIn = otpResponse.expiresIn;
        _isOtpSent = true;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to send OTP: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Verify OTP
  Future<bool> verifyOtp({
    required String phone,
    required String otp,
    String? name,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await AuthService.verifyOtp(
        phone: phone,
        otp: otp,
        name: name,
      );
      
      if (response.success) {
        final verifyResponse = response.data as VerifyOtpResponse;
        _user = verifyResponse.user;
        _setState(AuthState.authenticated);
        _isOtpSent = false;
        _otpPhone = null;
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to verify OTP: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Google Login
  Future<bool> googleLogin(String accessToken) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await AuthService.googleLogin(accessToken);
      
      if (response.success) {
        final verifyResponse = response.data as VerifyOtpResponse;
        _user = verifyResponse.user;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to login with Google: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      _setLoading(true);
      
      await AuthService.logout();
      
      _user = null;
      _setState(AuthState.unauthenticated);
      _isOtpSent = false;
      _otpPhone = null;
      _clearError();
    } catch (e) {
      // Even if logout API fails, clear local state
      _user = null;
      _setState(AuthState.unauthenticated);
      _isOtpSent = false;
      _otpPhone = null;
      _setError('Logout completed with errors: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Update Profile
  Future<bool> updateProfile({
    String? name,
    String? email,
    List<String>? examPreference,
    UserPreferences? preferences,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await UserService.updateUserProfile(
        name: name,
        email: email,
        examPreference: examPreference,
        preferences: preferences,
      );
      
      if (response.success) {
        _user = response.data as UserModel;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to update profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh User Data
  Future<void> refreshUserData() async {
    try {
      final response = await UserService.getUserProfile();
      
      if (response.success) {
        _user = response.data as UserModel;
        notifyListeners();
      }
    } catch (e) {
      // Silently fail for refresh
      debugPrint('Failed to refresh user data: ${e.toString()}');
    }
  }

  // Update Notification Preferences
  Future<bool> updateNotificationPreferences(NotificationPreferences preferences) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await UserService.updateNotificationPreferences(preferences);
      
      if (response.success) {
        _user = response.data as UserModel;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to update notification preferences: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update Language Preference
  Future<bool> updateLanguagePreference(String language) async {
    try {
      final response = await UserService.updateLanguagePreference(language);

      if (response.success) {
        _user = response.data as UserModel;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to update language preference: ${e.toString()}');
      return false;
    }
  }

  // Update Exam Preferences
  Future<bool> updateExamPreferences(List<String> examPreferences) async {
    try {
      _setLoading(true);
      _clearError();

      final response = await UserService.updateExamPreferences(examPreferences);

      if (response.success) {
        _user = response.data as UserModel;
        notifyListeners();
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to update exam preferences: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Reset OTP State
  void resetOtpState() {
    _isOtpSent = false;
    _otpPhone = null;
    _otpExpiresIn = 0;
    notifyListeners();
  }

  // Private Methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _state = AuthState.error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    if (_state == AuthState.error) {
      _state = AuthState.initial;
    }
  }

  // Helper Methods
  bool get isPremiumUser => _user?.subscription.isActive ?? false;
  List<String> get purchasedCourses => _user?.subscription.purchasedCourses ?? [];
  List<String> get examPreferences => _user?.examPreference ?? [];
  String get userLanguage => _user?.preferences.language ?? 'hindi';
  NotificationPreferences? get notificationPreferences => _user?.preferences.notifications;
}
