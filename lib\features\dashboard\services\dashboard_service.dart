import '../../../core/services/api_service.dart';
import '../../../core/constants/app_constants.dart';
import '../models/dashboard_model.dart';

class DashboardService {
  static const String _dashboardEndpoint = '/api/dashboard';
  static const String _statsEndpoint = '/api/user/stats';
  static const String _activitiesEndpoint = '/api/user/activities';
  static const String _announcementsEndpoint = AppConstants.notificationsEndpoint;

  // Get Dashboard Data
  static Future<ApiResponse> getDashboardData() async {
    try {
      final response = await ApiService.get(_dashboardEndpoint);

      if (response.success) {
        final dashboardData = DashboardData.fromJson(response.data);
        
        return ApiResponse.success(
          data: dashboardData,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch dashboard data: ${e.toString()}',
      );
    }
  }

  // Get User Statistics
  static Future<ApiResponse> getUserStats() async {
    try {
      final response = await ApiService.get(_statsEndpoint);

      if (response.success) {
        final userStats = UserStats.fromJson(response.data['stats']);
        
        return ApiResponse.success(
          data: userStats,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch user statistics: ${e.toString()}',
      );
    }
  }

  // Get Recent Activities
  static Future<ApiResponse> getRecentActivities({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_activitiesEndpoint?$queryString',
      );

      if (response.success) {
        final activities = (response.data['activities'] as List<dynamic>)
            .map((activity) => RecentActivity.fromJson(activity))
            .toList();

        return ApiResponse.success(
          data: {
            'activities': activities,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch recent activities: ${e.toString()}',
      );
    }
  }

  // Get Upcoming Classes
  static Future<ApiResponse> getUpcomingClasses({
    int limit = 5,
  }) async {
    try {
      final response = await ApiService.get(
        '${AppConstants.liveClassesEndpoint}/upcoming?limit=$limit',
      );

      if (response.success) {
        final classes = (response.data['classes'] as List<dynamic>)
            .map((classItem) => UpcomingClass.fromJson(classItem))
            .toList();

        return ApiResponse.success(
          data: classes,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch upcoming classes: ${e.toString()}',
      );
    }
  }

  // Get Announcements
  static Future<ApiResponse> getAnnouncements({
    int page = 1,
    int limit = 10,
    bool unreadOnly = false,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (unreadOnly) queryParams['unreadOnly'] = 'true';

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_announcementsEndpoint/announcements?$queryString',
      );

      if (response.success) {
        final announcements = (response.data['announcements'] as List<dynamic>)
            .map((announcement) => AnnouncementModel.fromJson(announcement))
            .toList();

        return ApiResponse.success(
          data: {
            'announcements': announcements,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
            'unreadCount': response.data['unreadCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch announcements: ${e.toString()}',
      );
    }
  }

  // Mark Announcement as Read
  static Future<ApiResponse> markAnnouncementAsRead(String announcementId) async {
    try {
      final response = await ApiService.put(
        '$_announcementsEndpoint/announcements/$announcementId/read',
        body: {},
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to mark announcement as read: ${e.toString()}',
      );
    }
  }

  // Get Learning Streak
  static Future<ApiResponse> getLearningStreak() async {
    try {
      final response = await ApiService.get('/api/user/streak');

      if (response.success) {
        final streak = LearningStreak.fromJson(response.data['streak']);
        
        return ApiResponse.success(
          data: streak,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch learning streak: ${e.toString()}',
      );
    }
  }

  // Update Learning Streak
  static Future<ApiResponse> updateLearningStreak() async {
    try {
      final response = await ApiService.post(
        '/api/user/streak/update',
        body: {
          'studyDate': DateTime.now().toIso8601String(),
        },
      );

      if (response.success) {
        final streak = LearningStreak.fromJson(response.data['streak']);
        
        return ApiResponse.success(
          data: streak,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update learning streak: ${e.toString()}',
      );
    }
  }

  // Log Activity
  static Future<ApiResponse> logActivity({
    required ActivityType type,
    required String title,
    required String description,
    String? courseId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await ApiService.post(
        _activitiesEndpoint,
        body: {
          'type': type.value,
          'title': title,
          'description': description,
          'courseId': courseId,
          'metadata': metadata,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (response.success) {
        final activity = RecentActivity.fromJson(response.data['activity']);
        
        return ApiResponse.success(
          data: activity,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to log activity: ${e.toString()}',
      );
    }
  }

  // Get Quick Actions
  static Future<ApiResponse> getQuickActions() async {
    try {
      final response = await ApiService.get('/api/user/quick-actions');

      if (response.success) {
        final quickActions = (response.data['quickActions'] as List<dynamic>)
            .map((action) => QuickAction.fromJson(action))
            .toList();

        return ApiResponse.success(
          data: quickActions,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch quick actions: ${e.toString()}',
      );
    }
  }

  // Get Study Analytics
  static Future<ApiResponse> getStudyAnalytics({
    String period = 'week', // week, month, year
  }) async {
    try {
      final response = await ApiService.get(
        '/api/user/analytics?period=$period',
      );

      if (response.success) {
        return ApiResponse.success(
          data: response.data['analytics'],
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch study analytics: ${e.toString()}',
      );
    }
  }

  // Get Performance Summary
  static Future<ApiResponse> getPerformanceSummary() async {
    try {
      final response = await ApiService.get('/api/user/performance');

      if (response.success) {
        return ApiResponse.success(
          data: response.data['performance'],
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch performance summary: ${e.toString()}',
      );
    }
  }

  // Get Course Recommendations
  static Future<ApiResponse> getCourseRecommendations({
    int limit = 5,
  }) async {
    try {
      final response = await ApiService.get(
        '${AppConstants.coursesEndpoint}/recommendations?limit=$limit',
      );

      if (response.success) {
        return ApiResponse.success(
          data: response.data,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch course recommendations: ${e.toString()}',
      );
    }
  }

  // Refresh Dashboard Data
  static Future<ApiResponse> refreshDashboard() async {
    try {
      final response = await ApiService.post(
        '$_dashboardEndpoint/refresh',
        body: {},
      );

      if (response.success) {
        final dashboardData = DashboardData.fromJson(response.data);
        
        return ApiResponse.success(
          data: dashboardData,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to refresh dashboard: ${e.toString()}',
      );
    }
  }
}
