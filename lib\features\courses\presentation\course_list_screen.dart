import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/custom_button.dart';
import 'subjects_screen.dart';

class CourseListScreen extends StatelessWidget {
  final String examName;

  const CourseListScreen({
    super.key,
    required this.examName,
  });

  // Sample course data based on exam
  List<Map<String, dynamic>> _getCourses(String exam) {
    switch (exam) {
      case 'BPSC':
        return [
          {
            'title': '70th BPSC Prelims',
            'description': 'Complete preparation for BPSC Preliminary examination',
            'subjects': 8,
            'videos': 120,
            'tests': 25,
            'price': 2999,
            'originalPrice': 4999,
            'rating': 4.8,
            'students': 1250,
            'isFree': false,
          },
          {
            'title': 'BPSC Mains Preparation',
            'description': 'Comprehensive course for BPSC Mains examination',
            'subjects': 6,
            'videos': 80,
            'tests': 15,
            'price': 3999,
            'originalPrice': 5999,
            'rating': 4.9,
            'students': 850,
            'isFree': false,
          },
          {
            'title': 'BPSC Free Course',
            'description': 'Basic preparation materials for BPSC',
            'subjects': 3,
            'videos': 30,
            'tests': 5,
            'price': 0,
            'originalPrice': 0,
            'rating': 4.5,
            'students': 5000,
            'isFree': true,
          },
        ];
      case 'SSC':
        return [
          {
            'title': 'SSC CGL Tier 1',
            'description': 'Complete preparation for SSC CGL Tier 1',
            'subjects': 4,
            'videos': 100,
            'tests': 30,
            'price': 2499,
            'originalPrice': 3999,
            'rating': 4.7,
            'students': 2100,
            'isFree': false,
          },
          {
            'title': 'SSC CHSL',
            'description': 'Comprehensive course for SSC CHSL',
            'subjects': 4,
            'videos': 85,
            'tests': 25,
            'price': 1999,
            'originalPrice': 2999,
            'rating': 4.6,
            'students': 1800,
            'isFree': false,
          },
        ];
      default:
        return [
          {
            'title': '$exam Foundation Course',
            'description': 'Complete preparation for $exam examination',
            'subjects': 6,
            'videos': 90,
            'tests': 20,
            'price': 2999,
            'originalPrice': 4999,
            'rating': 4.7,
            'students': 1500,
            'isFree': false,
          },
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final courses = _getCourses(examName);

    return Scaffold(
      appBar: AppBar(
        title: Text('$examName Courses'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    examName,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Choose the perfect course for your preparation',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.accentLight,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Courses List
            Text(
              'Available Courses',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: courses.length,
              itemBuilder: (context, index) {
                final course = courses[index];
                return _buildCourseCard(context, course);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseCard(BuildContext context, Map<String, dynamic> course) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withOpacity(0.1),
                  AppColors.secondary.withOpacity(0.1),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              course['title'],
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ),
                          if (course['isFree'])
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.success,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'FREE',
                                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        course['description'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Course Stats
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    _buildStatItem(context, Icons.book, '${course['subjects']} Subjects'),
                    const SizedBox(width: 16),
                    _buildStatItem(context, Icons.play_circle, '${course['videos']} Videos'),
                    const SizedBox(width: 16),
                    _buildStatItem(context, Icons.quiz, '${course['tests']} Tests'),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(Icons.star, color: AppColors.warning, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${course['rating']}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.people, color: AppColors.textSecondary, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${course['students']} students',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!course['isFree']) ...[
                            Row(
                              children: [
                                Text(
                                  '₹${course['price']}',
                                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                if (course['originalPrice'] > course['price'])
                                  Text(
                                    '₹${course['originalPrice']}',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      decoration: TextDecoration.lineThrough,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                              ],
                            ),
                          ] else
                            Text(
                              'FREE',
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.success,
                              ),
                            ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 110,
                      height: 36,
                      child: CustomButton(
                        text: course['isFree'] ? 'Start' : 'Enroll',
                        onPressed: () {
                          _navigateToSubjects(context, course);
                        },
                        backgroundColor: course['isFree'] ? AppColors.success : AppColors.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  void _navigateToSubjects(BuildContext context, Map<String, dynamic> course) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubjectsScreen(
          courseName: course['title'],
          examName: examName,
        ),
      ),
    );
  }
}
