import 'package:flutter/foundation.dart';
import '../models/dashboard_model.dart';
import '../services/dashboard_service.dart';
import '../../courses/models/course_model.dart';
import '../../courses/services/course_service.dart';
import '../../exams/services/exam_service.dart';

class DashboardProvider extends ChangeNotifier {
  // State
  bool _isLoading = false;
  bool _isRefreshing = false;
  String? _error;

  // Dashboard Data
  DashboardData? _dashboardData;
  UserStats? _userStats;
  List<RecentActivity> _recentActivities = [];
  List<UpcomingClass> _upcomingClasses = [];
  List<AnnouncementModel> _announcements = [];
  LearningStreak? _learningStreak;

  // Courses Data
  List<CourseModel> _enrolledCourses = [];
  List<CourseModel> _recommendedCourses = [];
  List<CourseModel> _popularCourses = [];

  // Exams Data
  List<ExamModel> _popularExams = [];
  List<ExamCategory> _examCategories = [];

  // Getters
  bool get isLoading => _isLoading;
  bool get isRefreshing => _isRefreshing;
  String? get error => _error;
  
  DashboardData? get dashboardData => _dashboardData;
  UserStats? get userStats => _userStats;
  List<RecentActivity> get recentActivities => _recentActivities;
  List<UpcomingClass> get upcomingClasses => _upcomingClasses;
  List<AnnouncementModel> get announcements => _announcements;
  LearningStreak? get learningStreak => _learningStreak;
  
  List<CourseModel> get enrolledCourses => _enrolledCourses;
  List<CourseModel> get recommendedCourses => _recommendedCourses;
  List<CourseModel> get popularCourses => _popularCourses;
  
  List<ExamModel> get popularExams => _popularExams;
  List<ExamCategory> get examCategories => _examCategories;

  // Computed getters
  int get unreadAnnouncementsCount => 
      _announcements.where((announcement) => !announcement.isRead).length;
  
  bool get hasActiveStreak => (_learningStreak?.currentStreak ?? 0) > 0;
  
  double get overallProgress => _userStats?.overallProgress ?? 0.0;

  // Initialize Dashboard
  Future<void> initializeDashboard() async {
    try {
      _setLoading(true);
      _clearError();

      // Load all dashboard data concurrently
      await Future.wait([
        _loadDashboardData(),
        _loadEnrolledCourses(),
        _loadRecommendedCourses(),
        _loadPopularExams(),
        _loadExamCategories(),
      ]);

    } catch (e) {
      _setError('Failed to initialize dashboard: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load Dashboard Data
  Future<void> _loadDashboardData() async {
    try {
      final response = await DashboardService.getDashboardData();
      
      if (response.success) {
        _dashboardData = response.data as DashboardData;
        _userStats = _dashboardData!.userStats;
        _recentActivities = _dashboardData!.recentActivities;
        _upcomingClasses = _dashboardData!.upcomingClasses;
        _announcements = _dashboardData!.announcements;
        _learningStreak = _dashboardData!.learningStreak;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load dashboard data: ${e.toString()}');
    }
  }

  // Load Enrolled Courses
  Future<void> _loadEnrolledCourses() async {
    try {
      final response = await CourseService.getEnrolledCourses(limit: 5);
      
      if (response.success) {
        _enrolledCourses = response.data['courses'] as List<CourseModel>;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load enrolled courses: ${e.toString()}');
    }
  }

  // Load Recommended Courses
  Future<void> _loadRecommendedCourses() async {
    try {
      final response = await CourseService.getRecommendedCourses(limit: 5);
      
      if (response.success) {
        _recommendedCourses = response.data as List<CourseModel>;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load recommended courses: ${e.toString()}');
    }
  }

  // Load Popular Courses
  Future<void> loadPopularCourses() async {
    try {
      final response = await CourseService.getPopularCourses(limit: 10);
      
      if (response.success) {
        _popularCourses = response.data as List<CourseModel>;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load popular courses: ${e.toString()}');
    }
  }

  // Load Popular Exams
  Future<void> _loadPopularExams() async {
    try {
      final response = await ExamService.getPopularExams(limit: 10);
      
      if (response.success) {
        _popularExams = response.data as List<ExamModel>;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load popular exams: ${e.toString()}');
    }
  }

  // Load Exam Categories
  Future<void> _loadExamCategories() async {
    try {
      final response = await ExamService.getExamCategories();
      
      if (response.success) {
        _examCategories = response.data as List<ExamCategory>;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load exam categories: ${e.toString()}');
    }
  }

  // Refresh Dashboard
  Future<void> refreshDashboard() async {
    try {
      _setRefreshing(true);
      _clearError();

      await initializeDashboard();
      
    } catch (e) {
      _setError('Failed to refresh dashboard: ${e.toString()}');
    } finally {
      _setRefreshing(false);
    }
  }

  // Load More Recent Activities
  Future<void> loadMoreActivities({int page = 2}) async {
    try {
      final response = await DashboardService.getRecentActivities(
        page: page,
        limit: 10,
      );
      
      if (response.success) {
        final newActivities = response.data['activities'] as List<RecentActivity>;
        _recentActivities.addAll(newActivities);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to load more activities: ${e.toString()}');
    }
  }

  // Mark Announcement as Read
  Future<void> markAnnouncementAsRead(String announcementId) async {
    try {
      final response = await DashboardService.markAnnouncementAsRead(announcementId);
      
      if (response.success) {
        // Update local state
        final index = _announcements.indexWhere((a) => a.id == announcementId);
        if (index != -1) {
          _announcements[index] = AnnouncementModel(
            id: _announcements[index].id,
            title: _announcements[index].title,
            content: _announcements[index].content,
            type: _announcements[index].type,
            priority: _announcements[index].priority,
            createdAt: _announcements[index].createdAt,
            expiresAt: _announcements[index].expiresAt,
            isRead: true,
            courseId: _announcements[index].courseId,
            courseName: _announcements[index].courseName,
          );
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Failed to mark announcement as read: ${e.toString()}');
    }
  }

  // Update Learning Streak
  Future<void> updateLearningStreak() async {
    try {
      final response = await DashboardService.updateLearningStreak();
      
      if (response.success) {
        _learningStreak = response.data as LearningStreak;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to update learning streak: ${e.toString()}');
    }
  }

  // Log Activity
  Future<void> logActivity({
    required ActivityType type,
    required String title,
    required String description,
    String? courseId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await DashboardService.logActivity(
        type: type,
        title: title,
        description: description,
        courseId: courseId,
        metadata: metadata,
      );
      
      if (response.success) {
        final newActivity = response.data as RecentActivity;
        _recentActivities.insert(0, newActivity);
        
        // Keep only latest 20 activities in memory
        if (_recentActivities.length > 20) {
          _recentActivities = _recentActivities.take(20).toList();
        }
        
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Failed to log activity: ${e.toString()}');
    }
  }

  // Enroll in Course
  Future<bool> enrollInCourse(String courseId) async {
    try {
      final response = await CourseService.enrollInCourse(courseId);
      
      if (response.success) {
        // Refresh enrolled courses
        await _loadEnrolledCourses();
        
        // Log activity
        await logActivity(
          type: ActivityType.courseEnrolled,
          title: 'Course Enrolled',
          description: 'Successfully enrolled in a new course',
          courseId: courseId,
        );
        
        return true;
      } else {
        _setError(response.message);
        return false;
      }
    } catch (e) {
      _setError('Failed to enroll in course: ${e.toString()}');
      return false;
    }
  }

  // Get Course by ID
  Future<CourseModel?> getCourseById(String courseId) async {
    try {
      final response = await CourseService.getCourseById(courseId);
      
      if (response.success) {
        return response.data as CourseModel;
      }
      return null;
    } catch (e) {
      debugPrint('Failed to get course: ${e.toString()}');
      return null;
    }
  }

  // Private Methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setRefreshing(bool refreshing) {
    _isRefreshing = refreshing;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Clear all data (for logout)
  void clearData() {
    _dashboardData = null;
    _userStats = null;
    _recentActivities.clear();
    _upcomingClasses.clear();
    _announcements.clear();
    _learningStreak = null;
    _enrolledCourses.clear();
    _recommendedCourses.clear();
    _popularCourses.clear();
    _popularExams.clear();
    _examCategories.clear();
    _error = null;
    _isLoading = false;
    _isRefreshing = false;
    notifyListeners();
  }
}
