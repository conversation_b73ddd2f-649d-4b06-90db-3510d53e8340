import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../video/presentation/video_player_screen.dart';
import '../../pdf/presentation/pdf_viewer_screen.dart';
import '../../test/presentation/test_screen.dart';
import '../../test/presentation/test_result_screen.dart';

class ClassContentScreen extends StatefulWidget {
  final String subjectName;
  final String courseName;

  const ClassContentScreen({
    super.key,
    required this.subjectName,
    required this.courseName,
  });

  @override
  State<ClassContentScreen> createState() => _ClassContentScreenState();
}

class _ClassContentScreenState extends State<ClassContentScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Sample video lectures data
  List<Map<String, dynamic>> get videoLectures => [
    {
      'title': 'Introduction to ${widget.subjectName}',
      'duration': '45:30',
      'progress': 1.0,
      'isCompleted': true,
      'thumbnail': 'assets/images/video_thumb1.jpg',
      'description': 'Basic concepts and fundamentals',
      'instructor': 'Dr. Rajesh Kumar',
      'views': 1250,
    },
    {
      'title': 'Chapter 1: Fundamental Concepts',
      'duration': '38:15',
      'progress': 0.7,
      'isCompleted': false,
      'thumbnail': 'assets/images/video_thumb2.jpg',
      'description': 'Deep dive into core principles',
      'instructor': 'Dr. Rajesh Kumar',
      'views': 980,
    },
    {
      'title': 'Chapter 2: Advanced Topics',
      'duration': '52:20',
      'progress': 0.0,
      'isCompleted': false,
      'thumbnail': 'assets/images/video_thumb3.jpg',
      'description': 'Complex concepts and applications',
      'instructor': 'Dr. Rajesh Kumar',
      'views': 750,
    },
    {
      'title': 'Practice Session 1',
      'duration': '30:45',
      'progress': 0.0,
      'isCompleted': false,
      'thumbnail': 'assets/images/video_thumb4.jpg',
      'description': 'Hands-on practice and examples',
      'instructor': 'Prof. Anita Sharma',
      'views': 650,
    },
  ];

  // Sample PDF notes data
  List<Map<String, dynamic>> get pdfNotes => [
    {
      'title': '${widget.subjectName} Complete Notes',
      'pages': 120,
      'size': '15.2 MB',
      'downloadCount': 2500,
      'isDownloaded': true,
      'description': 'Comprehensive study material covering all topics',
      'lastUpdated': '2 days ago',
    },
    {
      'title': 'Quick Revision Notes',
      'pages': 45,
      'size': '5.8 MB',
      'downloadCount': 1800,
      'isDownloaded': false,
      'description': 'Concise notes for quick revision',
      'lastUpdated': '1 week ago',
    },
    {
      'title': 'Important Formulas & Facts',
      'pages': 25,
      'size': '3.2 MB',
      'downloadCount': 3200,
      'isDownloaded': true,
      'description': 'Key formulas and important facts',
      'lastUpdated': '3 days ago',
    },
    {
      'title': 'Previous Year Questions',
      'pages': 80,
      'size': '12.5 MB',
      'downloadCount': 2100,
      'isDownloaded': false,
      'description': 'Collection of previous year exam questions',
      'lastUpdated': '5 days ago',
    },
  ];

  // Sample MCQ tests data
  List<Map<String, dynamic>> get mcqTests => [
    {
      'title': 'Basic Concepts Test',
      'questions': 25,
      'duration': 30,
      'maxMarks': 50,
      'attempts': 2,
      'bestScore': 42,
      'difficulty': 'Easy',
      'isAttempted': true,
      'description': 'Test your understanding of basic concepts',
    },
    {
      'title': 'Chapter 1 Assessment',
      'questions': 30,
      'duration': 45,
      'maxMarks': 60,
      'attempts': 1,
      'bestScore': 35,
      'difficulty': 'Medium',
      'isAttempted': true,
      'description': 'Comprehensive test on Chapter 1',
    },
    {
      'title': 'Advanced Topics Quiz',
      'questions': 40,
      'duration': 60,
      'maxMarks': 80,
      'attempts': 0,
      'bestScore': 0,
      'difficulty': 'Hard',
      'isAttempted': false,
      'description': 'Challenge yourself with advanced questions',
    },
    {
      'title': 'Mock Test 1',
      'questions': 50,
      'duration': 90,
      'maxMarks': 100,
      'attempts': 0,
      'bestScore': 0,
      'difficulty': 'Medium',
      'isAttempted': false,
      'description': 'Full-length mock test simulation',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.subjectName),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.play_circle), text: 'Videos'),
            Tab(icon: Icon(Icons.picture_as_pdf), text: 'PDFs'),
            Tab(icon: Icon(Icons.quiz), text: 'Tests'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildVideoLecturesTab(),
          _buildPDFNotesTab(),
          _buildMCQTestsTab(),
        ],
      ),
    );
  }

  Widget _buildVideoLecturesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress Overview
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Video Progress',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_getCompletedVideos()}/${videoLectures.length} videos completed',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.accentLight,
                  ),
                ),
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: _getOverallProgress(),
                  backgroundColor: AppColors.white.withOpacity(0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.accentLight),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          Text(
            'Video Lectures',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: videoLectures.length,
            itemBuilder: (context, index) {
              final video = videoLectures[index];
              return _buildVideoCard(context, video, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildVideoCard(BuildContext context, Map<String, dynamic> video, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _playVideo(context, video),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Video Thumbnail
              Container(
                width: 80,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.greyLight,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Icon(
                      Icons.play_circle_filled,
                      size: 32,
                      color: video['isCompleted'] ? AppColors.success : AppColors.primary,
                    ),
                    if (video['progress'] > 0 && !video['isCompleted'])
                      Positioned(
                        bottom: 4,
                        left: 4,
                        right: 4,
                        child: LinearProgressIndicator(
                          value: video['progress'],
                          backgroundColor: AppColors.white.withOpacity(0.5),
                          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                        ),
                      ),
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Video Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      video['title'],
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      video['description'],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(Icons.access_time, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          video['duration'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.visibility, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          '${video['views']} views',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Status Icon
              Icon(
                video['isCompleted'] ? Icons.check_circle : Icons.play_arrow,
                color: video['isCompleted'] ? AppColors.success : AppColors.primary,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPDFNotesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Download Stats
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.secondary, AppColors.accent],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Study Materials',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_getDownloadedPDFs()}/${pdfNotes.length} downloaded',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.picture_as_pdf,
                  size: 40,
                  color: AppColors.white,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          Text(
            'PDF Notes & Materials',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: pdfNotes.length,
            itemBuilder: (context, index) {
              final pdf = pdfNotes[index];
              return _buildPDFCard(context, pdf);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPDFCard(BuildContext context, Map<String, dynamic> pdf) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.picture_as_pdf,
                    color: AppColors.error,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pdf['title'],
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        pdf['description'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (pdf['isDownloaded'])
                  const Icon(
                    Icons.download_done,
                    color: AppColors.success,
                    size: 24,
                  ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                _buildPDFStat(Icons.description, '${pdf['pages']} pages'),
                const SizedBox(width: 16),
                _buildPDFStat(Icons.file_download, '${pdf['downloadCount']} downloads'),
                const SizedBox(width: 16),
                _buildPDFStat(Icons.storage, pdf['size']),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'View PDF',
                    onPressed: () => _viewPDF(context, pdf),
                    backgroundColor: AppColors.primary,
                    height: 36,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: pdf['isDownloaded'] ? 'Downloaded' : 'Download',
                    onPressed: pdf['isDownloaded'] ? null : () => _downloadPDF(context, pdf),
                    backgroundColor: pdf['isDownloaded'] ? AppColors.success : AppColors.secondary,
                    height: 36,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPDFStat(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMCQTestsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Stats
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.accent, AppColors.accentLight],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Performance',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_getAttemptedTests()}/${mcqTests.length} tests attempted',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.quiz,
                  size: 40,
                  color: AppColors.white,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          Text(
            'MCQ Tests & Quizzes',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: mcqTests.length,
            itemBuilder: (context, index) {
              final test = mcqTests[index];
              return _buildTestCard(context, test);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTestCard(BuildContext context, Map<String, dynamic> test) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getDifficultyColor(test['difficulty']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.quiz,
                    color: _getDifficultyColor(test['difficulty']),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              test['title'],
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getDifficultyColor(test['difficulty']),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              test['difficulty'],
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        test['description'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                _buildTestStat(Icons.quiz, '${test['questions']} Questions'),
                const SizedBox(width: 16),
                _buildTestStat(Icons.access_time, '${test['duration']} min'),
                const SizedBox(width: 16),
                _buildTestStat(Icons.grade, '${test['maxMarks']} marks'),
              ],
            ),

            if (test['isAttempted']) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    'Best Score: ${test['bestScore']}/${test['maxMarks']}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.success,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Attempts: ${test['attempts']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: test['isAttempted'] ? 'Retake Test' : 'Start Test',
                    onPressed: () => _startTest(context, test),
                    backgroundColor: AppColors.primary,
                    height: 36,
                  ),
                ),
                if (test['isAttempted']) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomButton(
                      text: 'View Results',
                      onPressed: () => _viewTestResults(context, test),
                      backgroundColor: AppColors.secondary,
                      height: 36,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestStat(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return AppColors.success;
      case 'medium':
        return AppColors.warning;
      case 'hard':
        return AppColors.error;
      default:
        return AppColors.primary;
    }
  }

  // Helper methods
  int _getCompletedVideos() {
    return videoLectures.where((video) => video['isCompleted']).length;
  }

  double _getOverallProgress() {
    if (videoLectures.isEmpty) return 0.0;
    double totalProgress = videoLectures.fold(0.0, (sum, video) => sum + video['progress']);
    return totalProgress / videoLectures.length;
  }

  int _getDownloadedPDFs() {
    return pdfNotes.where((pdf) => pdf['isDownloaded']).length;
  }

  int _getAttemptedTests() {
    return mcqTests.where((test) => test['isAttempted']).length;
  }

  // Action methods
  void _playVideo(BuildContext context, Map<String, dynamic> video) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(
          video: video,
          subjectName: widget.subjectName,
          courseName: widget.courseName,
        ),
      ),
    );
  }

  void _viewPDF(BuildContext context, Map<String, dynamic> pdf) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PDFViewerScreen(
          pdf: pdf,
          subjectName: widget.subjectName,
          courseName: widget.courseName,
        ),
      ),
    );
  }

  void _downloadPDF(BuildContext context, Map<String, dynamic> pdf) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading: ${pdf['title']}'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _startTest(BuildContext context, Map<String, dynamic> test) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TestScreen(
          test: test,
          subjectName: widget.subjectName,
          courseName: widget.courseName,
        ),
      ),
    );
  }

  void _viewTestResults(BuildContext context, Map<String, dynamic> test) {
    // Create sample result data for demonstration
    final sampleQuestions = [
      {
        'type': 'MCQ',
        'question': 'What is the fundamental structure of the Indian Constitution?',
        'options': ['Parliamentary System', 'Federal System', 'Secular Nature', 'All of the above'],
        'correctAnswer': 3,
        'explanation': 'The fundamental structure includes parliamentary system, federal system, secular nature, and other basic features.',
        'marks': 2,
      },
      {
        'type': 'True/False',
        'question': 'The President of India is directly elected by the people.',
        'options': ['True', 'False'],
        'correctAnswer': 1,
        'explanation': 'False. The President is elected by an electoral college.',
        'marks': 1,
      },
    ];

    final sampleAnswers = {0: 3, 1: 1}; // Sample correct answers

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TestResultScreen(
          test: test,
          questions: sampleQuestions,
          answers: sampleAnswers,
          obtainedMarks: test['bestScore'],
          totalMarks: test['maxMarks'],
          timeTaken: 1200, // 20 minutes in seconds
          subjectName: widget.subjectName,
          courseName: widget.courseName,
        ),
      ),
    );
  }
}
