class AppConstants {
  AppConstants._();

  // App Information
  static const String appName = 'Utkrishta Classes';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Smart mobile app for competitive exam preparation';

  // App Store Information
  static const String playStoreUrl = '';
  static const String appStoreUrl = '';

  // API Configuration
  static const String baseUrl = 'http://localhost:5000'; // Development
  // static const String baseUrl = 'https://api.utkrishta.com'; // Production
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);

  // API Endpoints
  static const String authEndpoint = '/api/auth';
  static const String userEndpoint = '/api/user';
  static const String coursesEndpoint = '/api/courses';
  static const String examsEndpoint = '/api/exams';
  static const String testsEndpoint = '/api/tests';
  static const String notificationsEndpoint = '/api/notifications';
  static const String paymentsEndpoint = '/api/payments';
  static const String liveClassesEndpoint = '/api/live-classes';
  static const String notesEndpoint = '/api/notes';
  static const String progressEndpoint = '/api/progress';
  static const String bookmarksEndpoint = '/api/bookmarks';
  static const String downloadsEndpoint = '/api/downloads';
  static const String searchEndpoint = '/api/search';

  // Authentication
  static const String googleClientId = '';
  static const int otpLength = 6;
  static const Duration otpTimeout = Duration(minutes: 2);

  // Storage Keys
  static const String keyIsFirstTime = 'is_first_time';
  static const String keyUserToken = 'user_token';
  static const String keyUserData = 'user_data';
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultRadius = 12.0;
  static const double smallRadius = 8.0;
  static const double largeRadius = 16.0;

  // Exam Categories
  static const List<String> examCategories = [
    'BPSC',
    'SSC',
    'Bihar Board 10th',
    'Bihar Board 12th',
    'Banking',
    'Railway',
    'Police',
    'Teaching',
  ];

  // Languages
  static const List<Map<String, String>> supportedLanguages = [
    {'code': 'en', 'name': 'English'},
    {'code': 'hi', 'name': 'हिंदी'},
  ];

  // Error Messages
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'Something went wrong. Please try again.';
  static const String invalidCredentials = 'Invalid credentials. Please check your details.';
  static const String sessionExpired = 'Session expired. Please login again.';

  // Success Messages
  static const String loginSuccess = 'Login successful!';
  static const String registrationSuccess = 'Registration successful!';
  static const String otpSent = 'OTP sent successfully!';
  static const String otpVerified = 'OTP verified successfully!';

  // Validation Messages
  static const String requiredField = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email address';
  static const String invalidPhone = 'Please enter a valid phone number';
  static const String passwordTooShort = 'Password must be at least 6 characters';
  static const String passwordMismatch = 'Passwords do not match';
  static const String invalidOtp = 'Please enter a valid OTP';

  // Feature Flags
  static const bool enableGoogleSignIn = true;
  static const bool enableFacebookSignIn = false;
  static const bool enableBiometricAuth = true;
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableAnalytics = true;
}
