class UserModel {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String role;
  final bool isActive;
  final List<String> examPreference;
  final UserSubscription subscription;
  final UserPreferences preferences;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  UserModel({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    required this.role,
    required this.isActive,
    required this.examPreference,
    required this.subscription,
    required this.preferences,
    required this.createdAt,
    this.lastLoginAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      role: json['role'] ?? 'student',
      isActive: json['isActive'] ?? true,
      examPreference: List<String>.from(json['examPreference'] ?? []),
      subscription: UserSubscription.fromJson(json['subscription'] ?? {}),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'role': role,
      'isActive': isActive,
      'examPreference': examPreference,
      'subscription': subscription.toJson(),
      'preferences': preferences.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? role,
    bool? isActive,
    List<String>? examPreference,
    UserSubscription? subscription,
    UserPreferences? preferences,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      examPreference: examPreference ?? this.examPreference,
      subscription: subscription ?? this.subscription,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
}

class UserSubscription {
  final bool isActive;
  final List<String> purchasedCourses;
  final DateTime? expiresAt;
  final String? planType;

  UserSubscription({
    required this.isActive,
    required this.purchasedCourses,
    this.expiresAt,
    this.planType,
  });

  factory UserSubscription.fromJson(Map<String, dynamic> json) {
    return UserSubscription(
      isActive: json['isActive'] ?? false,
      purchasedCourses: List<String>.from(json['purchasedCourses'] ?? []),
      expiresAt: json['expiresAt'] != null 
          ? DateTime.parse(json['expiresAt']) 
          : null,
      planType: json['planType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isActive': isActive,
      'purchasedCourses': purchasedCourses,
      'expiresAt': expiresAt?.toIso8601String(),
      'planType': planType,
    };
  }
}

class UserPreferences {
  final String language;
  final NotificationPreferences notifications;
  final String? theme;

  UserPreferences({
    required this.language,
    required this.notifications,
    this.theme,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      language: json['language'] ?? 'hindi',
      notifications: NotificationPreferences.fromJson(json['notifications'] ?? {}),
      theme: json['theme'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'notifications': notifications.toJson(),
      'theme': theme,
    };
  }
}

class NotificationPreferences {
  final bool newClass;
  final bool liveClass;
  final bool testReminder;
  final bool announcements;

  NotificationPreferences({
    required this.newClass,
    required this.liveClass,
    required this.testReminder,
    required this.announcements,
  });

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    return NotificationPreferences(
      newClass: json['newClass'] ?? true,
      liveClass: json['liveClass'] ?? true,
      testReminder: json['testReminder'] ?? true,
      announcements: json['announcements'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'newClass': newClass,
      'liveClass': liveClass,
      'testReminder': testReminder,
      'announcements': announcements,
    };
  }
}

class DeviceInfo {
  final String deviceId;
  final String deviceType;
  final String? platform;
  final String? version;

  DeviceInfo({
    required this.deviceId,
    required this.deviceType,
    this.platform,
    this.version,
  });

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      deviceId: json['deviceId'] ?? '',
      deviceType: json['deviceType'] ?? 'mobile',
      platform: json['platform'],
      version: json['version'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deviceId': deviceId,
      'deviceType': deviceType,
      'platform': platform,
      'version': version,
    };
  }
}
