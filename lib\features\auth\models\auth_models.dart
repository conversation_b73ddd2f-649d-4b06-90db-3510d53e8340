import 'user_model.dart';

// Send OTP Request
class SendOtpRequest {
  final String phone;

  SendOtpRequest({required this.phone});

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
    };
  }
}

// Send OTP Response
class SendOtpResponse {
  final String phone;
  final int expiresIn;

  SendOtpResponse({
    required this.phone,
    required this.expiresIn,
  });

  factory SendOtpResponse.fromJson(Map<String, dynamic> json) {
    return SendOtpResponse(
      phone: json['phone'] ?? '',
      expiresIn: json['expiresIn'] ?? 600,
    );
  }
}

// Verify OTP Request
class VerifyOtpRequest {
  final String phone;
  final String otp;
  final String? name;
  final DeviceInfo deviceInfo;

  VerifyOtpRequest({
    required this.phone,
    required this.otp,
    this.name,
    required this.deviceInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'otp': otp,
      'name': name,
      'deviceInfo': deviceInfo.toJson(),
    };
  }
}

// Verify OTP Response
class VerifyOtpResponse {
  final UserModel user;
  final String token;
  final bool isNewUser;

  VerifyOtpResponse({
    required this.user,
    required this.token,
    required this.isNewUser,
  });

  factory VerifyOtpResponse.fromJson(Map<String, dynamic> json) {
    return VerifyOtpResponse(
      user: UserModel.fromJson(json['user'] ?? {}),
      token: json['token'] ?? '',
      isNewUser: json['isNewUser'] ?? false,
    );
  }
}

// Refresh Token Request
class RefreshTokenRequest {
  final String refreshToken;

  RefreshTokenRequest({required this.refreshToken});

  Map<String, dynamic> toJson() {
    return {
      'refreshToken': refreshToken,
    };
  }
}

// Refresh Token Response
class RefreshTokenResponse {
  final String token;
  final String refreshToken;
  final int expiresIn;

  RefreshTokenResponse({
    required this.token,
    required this.refreshToken,
    required this.expiresIn,
  });

  factory RefreshTokenResponse.fromJson(Map<String, dynamic> json) {
    return RefreshTokenResponse(
      token: json['token'] ?? '',
      refreshToken: json['refreshToken'] ?? '',
      expiresIn: json['expiresIn'] ?? 604800, // 7 days
    );
  }
}

// Logout Request
class LogoutRequest {
  final String? deviceId;

  LogoutRequest({this.deviceId});

  Map<String, dynamic> toJson() {
    return {
      'deviceId': deviceId,
    };
  }
}

// Update Profile Request
class UpdateProfileRequest {
  final String? name;
  final String? email;
  final List<String>? examPreference;
  final UserPreferences? preferences;

  UpdateProfileRequest({
    this.name,
    this.email,
    this.examPreference,
    this.preferences,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (name != null) data['name'] = name;
    if (email != null) data['email'] = email;
    if (examPreference != null) data['examPreference'] = examPreference;
    if (preferences != null) data['preferences'] = preferences!.toJson();
    
    return data;
  }
}

// Forgot Password Request
class ForgotPasswordRequest {
  final String email;

  ForgotPasswordRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

// Reset Password Request
class ResetPasswordRequest {
  final String email;
  final String token;
  final String newPassword;

  ResetPasswordRequest({
    required this.email,
    required this.token,
    required this.newPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'token': token,
      'newPassword': newPassword,
    };
  }
}

// Social Login Request
class SocialLoginRequest {
  final String provider; // 'google' or 'facebook'
  final String accessToken;
  final DeviceInfo deviceInfo;

  SocialLoginRequest({
    required this.provider,
    required this.accessToken,
    required this.deviceInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'provider': provider,
      'accessToken': accessToken,
      'deviceInfo': deviceInfo.toJson(),
    };
  }
}

// Auth State
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

// Auth Error
class AuthError {
  final String message;
  final String? code;
  final int? statusCode;

  AuthError({
    required this.message,
    this.code,
    this.statusCode,
  });

  factory AuthError.fromJson(Map<String, dynamic> json) {
    return AuthError(
      message: json['message'] ?? 'An error occurred',
      code: json['error'],
      statusCode: json['statusCode'],
    );
  }

  @override
  String toString() {
    return 'AuthError(message: $message, code: $code, statusCode: $statusCode)';
  }
}
