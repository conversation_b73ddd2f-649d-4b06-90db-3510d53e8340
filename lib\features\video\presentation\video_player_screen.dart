import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/custom_button.dart';

class VideoPlayerScreen extends StatefulWidget {
  final Map<String, dynamic> video;
  final String subjectName;
  final String courseName;

  const VideoPlayerScreen({
    super.key,
    required this.video,
    required this.subjectName,
    required this.courseName,
  });

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  bool _isPlaying = false;
  bool _isFullScreen = false;
  bool _showControls = true;
  bool _isBookmarked = false;
  double _currentPosition = 0.0;
  double _totalDuration = 100.0; // Sample duration in seconds
  
  // Sample bookmarks
  List<Map<String, dynamic>> _bookmarks = [];

  @override
  void initState() {
    super.initState();
    // Initialize with saved progress
    _currentPosition = (widget.video['progress'] * _totalDuration);
    _loadBookmarks();
  }

  void _loadBookmarks() {
    // Sample bookmarks data
    setState(() {
      _bookmarks = [
        {'time': 15.5, 'title': 'Introduction starts'},
        {'time': 45.2, 'title': 'Key concept explanation'},
        {'time': 78.8, 'title': 'Important formula'},
      ];
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
    });
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });
    
    if (_isFullScreen) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  void _addBookmark() {
    showDialog(
      context: context,
      builder: (context) => _BookmarkDialog(
        currentTime: _currentPosition,
        onSave: (title) {
          setState(() {
            _bookmarks.add({
              'time': _currentPosition,
              'title': title,
            });
          });
        },
      ),
    );
  }

  void _seekToPosition(double position) {
    setState(() {
      _currentPosition = position;
    });
  }

  String _formatDuration(double seconds) {
    int minutes = (seconds / 60).floor();
    int remainingSeconds = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _isFullScreen ? null : AppBar(
        title: Text(widget.video['title']),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: Icon(_isBookmarked ? Icons.bookmark : Icons.bookmark_border),
            onPressed: () {
              setState(() {
                _isBookmarked = !_isBookmarked;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _downloadVideo,
          ),
        ],
      ),
      body: Column(
        children: [
          // Video Player Area
          Expanded(
            flex: _isFullScreen ? 1 : 3,
            child: Container(
              width: double.infinity,
              color: Colors.black,
              child: Stack(
                children: [
                  // Video placeholder (would be actual video player)
                  Center(
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.3),
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _isPlaying ? Icons.pause_circle : Icons.play_circle,
                            size: 80,
                            color: AppColors.white,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            widget.video['title'],
                            style: const TextStyle(
                              color: AppColors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // Video Controls Overlay
                  if (_showControls)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.7),
                            ],
                          ),
                        ),
                        child: Column(
                          children: [
                            const Spacer(),
                            _buildVideoControls(),
                          ],
                        ),
                      ),
                    ),
                  
                  // Tap to toggle controls
                  Positioned.fill(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _showControls = !_showControls;
                        });
                      },
                      onDoubleTap: _togglePlayPause,
                      child: Container(color: Colors.transparent),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Video Info and Controls (only in portrait mode)
          if (!_isFullScreen)
            Expanded(
              flex: 2,
              child: Container(
                color: AppColors.background,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildVideoInfo(),
                      const SizedBox(height: 24),
                      _buildBookmarksSection(),
                      const SizedBox(height: 24),
                      _buildVideoActions(),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVideoControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress Bar
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(color: AppColors.white, fontSize: 12),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: AppColors.primary,
                    inactiveTrackColor: AppColors.white.withOpacity(0.3),
                    thumbColor: AppColors.primary,
                    overlayColor: AppColors.primary.withOpacity(0.2),
                    trackHeight: 3,
                  ),
                  child: Slider(
                    value: _currentPosition,
                    max: _totalDuration,
                    onChanged: _seekToPosition,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(color: AppColors.white, fontSize: 12),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Control Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                icon: const Icon(Icons.replay_10, color: AppColors.white),
                onPressed: () => _seekToPosition(_currentPosition - 10),
              ),
              IconButton(
                icon: Icon(
                  _isPlaying ? Icons.pause : Icons.play_arrow,
                  color: AppColors.white,
                  size: 40,
                ),
                onPressed: _togglePlayPause,
              ),
              IconButton(
                icon: const Icon(Icons.forward_10, color: AppColors.white),
                onPressed: () => _seekToPosition(_currentPosition + 10),
              ),
              IconButton(
                icon: const Icon(Icons.bookmark_add, color: AppColors.white),
                onPressed: _addBookmark,
              ),
              IconButton(
                icon: Icon(
                  _isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                  color: AppColors.white,
                ),
                onPressed: _toggleFullScreen,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVideoInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.video['title'],
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.video['description'],
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            _buildInfoChip(Icons.person, widget.video['instructor']),
            const SizedBox(width: 12),
            _buildInfoChip(Icons.access_time, widget.video['duration']),
            const SizedBox(width: 12),
            _buildInfoChip(Icons.visibility, '${widget.video['views']} views'),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.greyLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppColors.textSecondary),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Bookmarks',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: _addBookmark,
              icon: const Icon(Icons.add),
              label: const Text('Add'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_bookmarks.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text('No bookmarks yet. Add bookmarks to save important moments.'),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _bookmarks.length,
            itemBuilder: (context, index) {
              final bookmark = _bookmarks[index];
              return ListTile(
                leading: const Icon(Icons.bookmark, color: AppColors.primary),
                title: Text(bookmark['title']),
                subtitle: Text(_formatDuration(bookmark['time'])),
                trailing: IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () => _seekToPosition(bookmark['time']),
                ),
                onTap: () => _seekToPosition(bookmark['time']),
              );
            },
          ),
      ],
    );
  }

  Widget _buildVideoActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'Download Video',
                onPressed: _downloadVideo,
                backgroundColor: AppColors.secondary,
                icon: Icons.download,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: CustomButton(
                text: 'Share',
                onPressed: _shareVideo,
                backgroundColor: AppColors.accent,
                icon: Icons.share,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _downloadVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Video download started...'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _shareVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing video...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  @override
  void dispose() {
    // Reset orientation when leaving
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }
}

class _BookmarkDialog extends StatefulWidget {
  final double currentTime;
  final Function(String) onSave;

  const _BookmarkDialog({
    required this.currentTime,
    required this.onSave,
  });

  @override
  State<_BookmarkDialog> createState() => _BookmarkDialogState();
}

class _BookmarkDialogState extends State<_BookmarkDialog> {
  final _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Bookmark'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Time: ${_formatTime(widget.currentTime)}'),
          const SizedBox(height: 16),
          TextField(
            controller: _controller,
            decoration: const InputDecoration(
              labelText: 'Bookmark Title',
              hintText: 'Enter a description for this bookmark',
            ),
            autofocus: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_controller.text.isNotEmpty) {
              widget.onSave(_controller.text);
              Navigator.of(context).pop();
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }

  String _formatTime(double seconds) {
    int minutes = (seconds / 60).floor();
    int remainingSeconds = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
