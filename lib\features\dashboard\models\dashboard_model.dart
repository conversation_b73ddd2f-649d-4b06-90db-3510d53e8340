import '../../../features/courses/models/course_model.dart';

class DashboardData {
  final UserStats userStats;
  final List<CourseModel> enrolledCourses;
  final List<CourseModel> recommendedCourses;
  final List<RecentActivity> recentActivities;
  final List<UpcomingClass> upcomingClasses;
  final List<AnnouncementModel> announcements;
  final LearningStreak learningStreak;
  final List<QuickAction> quickActions;

  DashboardData({
    required this.userStats,
    required this.enrolledCourses,
    required this.recommendedCourses,
    required this.recentActivities,
    required this.upcomingClasses,
    required this.announcements,
    required this.learningStreak,
    required this.quickActions,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      userStats: UserStats.fromJson(json['userStats'] ?? {}),
      enrolledCourses: (json['enrolledCourses'] as List<dynamic>?)
          ?.map((course) => CourseModel.fromJson(course))
          .toList() ?? [],
      recommendedCourses: (json['recommendedCourses'] as List<dynamic>?)
          ?.map((course) => CourseModel.fromJson(course))
          .toList() ?? [],
      recentActivities: (json['recentActivities'] as List<dynamic>?)
          ?.map((activity) => RecentActivity.fromJson(activity))
          .toList() ?? [],
      upcomingClasses: (json['upcomingClasses'] as List<dynamic>?)
          ?.map((classItem) => UpcomingClass.fromJson(classItem))
          .toList() ?? [],
      announcements: (json['announcements'] as List<dynamic>?)
          ?.map((announcement) => AnnouncementModel.fromJson(announcement))
          .toList() ?? [],
      learningStreak: LearningStreak.fromJson(json['learningStreak'] ?? {}),
      quickActions: (json['quickActions'] as List<dynamic>?)
          ?.map((action) => QuickAction.fromJson(action))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userStats': userStats.toJson(),
      'enrolledCourses': enrolledCourses.map((course) => course.toJson()).toList(),
      'recommendedCourses': recommendedCourses.map((course) => course.toJson()).toList(),
      'recentActivities': recentActivities.map((activity) => activity.toJson()).toList(),
      'upcomingClasses': upcomingClasses.map((classItem) => classItem.toJson()).toList(),
      'announcements': announcements.map((announcement) => announcement.toJson()).toList(),
      'learningStreak': learningStreak.toJson(),
      'quickActions': quickActions.map((action) => action.toJson()).toList(),
    };
  }
}

class UserStats {
  final int totalCoursesEnrolled;
  final int totalCoursesCompleted;
  final int totalVideosWatched;
  final int totalTestsAttempted;
  final int totalTestsCompleted;
  final double averageTestScore;
  final int totalStudyHours;
  final int currentStreak;
  final int longestStreak;
  final double overallProgress;

  UserStats({
    required this.totalCoursesEnrolled,
    required this.totalCoursesCompleted,
    required this.totalVideosWatched,
    required this.totalTestsAttempted,
    required this.totalTestsCompleted,
    required this.averageTestScore,
    required this.totalStudyHours,
    required this.currentStreak,
    required this.longestStreak,
    required this.overallProgress,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalCoursesEnrolled: json['totalCoursesEnrolled'] ?? 0,
      totalCoursesCompleted: json['totalCoursesCompleted'] ?? 0,
      totalVideosWatched: json['totalVideosWatched'] ?? 0,
      totalTestsAttempted: json['totalTestsAttempted'] ?? 0,
      totalTestsCompleted: json['totalTestsCompleted'] ?? 0,
      averageTestScore: (json['averageTestScore'] ?? 0).toDouble(),
      totalStudyHours: json['totalStudyHours'] ?? 0,
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      overallProgress: (json['overallProgress'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCoursesEnrolled': totalCoursesEnrolled,
      'totalCoursesCompleted': totalCoursesCompleted,
      'totalVideosWatched': totalVideosWatched,
      'totalTestsAttempted': totalTestsAttempted,
      'totalTestsCompleted': totalTestsCompleted,
      'averageTestScore': averageTestScore,
      'totalStudyHours': totalStudyHours,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'overallProgress': overallProgress,
    };
  }
}

class RecentActivity {
  final String id;
  final ActivityType type;
  final String title;
  final String description;
  final String? courseId;
  final String? courseName;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  RecentActivity({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    this.courseId,
    this.courseName,
    required this.timestamp,
    this.metadata,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      id: json['id'] ?? '',
      type: ActivityType.fromString(json['type'] ?? 'video_watched'),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      courseId: json['courseId'],
      courseName: json['courseName'],
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'title': title,
      'description': description,
      'courseId': courseId,
      'courseName': courseName,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

enum ActivityType {
  videoWatched('video_watched'),
  testCompleted('test_completed'),
  courseEnrolled('course_enrolled'),
  courseCompleted('course_completed'),
  liveClassAttended('live_class_attended'),
  pdfDownloaded('pdf_downloaded'),
  noteAdded('note_added');

  const ActivityType(this.value);
  final String value;

  static ActivityType fromString(String value) {
    return ActivityType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ActivityType.videoWatched,
    );
  }
}

class UpcomingClass {
  final String id;
  final String title;
  final String description;
  final String instructorName;
  final String? instructorImage;
  final DateTime scheduledAt;
  final int duration; // in minutes
  final String? meetingUrl;
  final String courseId;
  final String courseName;
  final ClassStatus status;
  final bool isRegistered;

  UpcomingClass({
    required this.id,
    required this.title,
    required this.description,
    required this.instructorName,
    this.instructorImage,
    required this.scheduledAt,
    required this.duration,
    this.meetingUrl,
    required this.courseId,
    required this.courseName,
    required this.status,
    required this.isRegistered,
  });

  factory UpcomingClass.fromJson(Map<String, dynamic> json) {
    return UpcomingClass(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      instructorName: json['instructorName'] ?? '',
      instructorImage: json['instructorImage'],
      scheduledAt: DateTime.parse(json['scheduledAt'] ?? DateTime.now().toIso8601String()),
      duration: json['duration'] ?? 60,
      meetingUrl: json['meetingUrl'],
      courseId: json['courseId'] ?? '',
      courseName: json['courseName'] ?? '',
      status: ClassStatus.fromString(json['status'] ?? 'scheduled'),
      isRegistered: json['isRegistered'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'instructorName': instructorName,
      'instructorImage': instructorImage,
      'scheduledAt': scheduledAt.toIso8601String(),
      'duration': duration,
      'meetingUrl': meetingUrl,
      'courseId': courseId,
      'courseName': courseName,
      'status': status.value,
      'isRegistered': isRegistered,
    };
  }
}

enum ClassStatus {
  scheduled('scheduled'),
  live('live'),
  completed('completed'),
  cancelled('cancelled');

  const ClassStatus(this.value);
  final String value;

  static ClassStatus fromString(String value) {
    return ClassStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ClassStatus.scheduled,
    );
  }
}

class AnnouncementModel {
  final String id;
  final String title;
  final String content;
  final AnnouncementType type;
  final AnnouncementPriority priority;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final bool isRead;
  final String? courseId;
  final String? courseName;

  AnnouncementModel({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.createdAt,
    this.expiresAt,
    required this.isRead,
    this.courseId,
    this.courseName,
  });

  factory AnnouncementModel.fromJson(Map<String, dynamic> json) {
    return AnnouncementModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      type: AnnouncementType.fromString(json['type'] ?? 'general'),
      priority: AnnouncementPriority.fromString(json['priority'] ?? 'normal'),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      isRead: json['isRead'] ?? false,
      courseId: json['courseId'],
      courseName: json['courseName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.value,
      'priority': priority.value,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'isRead': isRead,
      'courseId': courseId,
      'courseName': courseName,
    };
  }
}

enum AnnouncementType {
  general('general'),
  courseUpdate('course_update'),
  examNotification('exam_notification'),
  maintenance('maintenance'),
  newFeature('new_feature');

  const AnnouncementType(this.value);
  final String value;

  static AnnouncementType fromString(String value) {
    return AnnouncementType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => AnnouncementType.general,
    );
  }
}

enum AnnouncementPriority {
  low('low'),
  normal('normal'),
  high('high'),
  urgent('urgent');

  const AnnouncementPriority(this.value);
  final String value;

  static AnnouncementPriority fromString(String value) {
    return AnnouncementPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => AnnouncementPriority.normal,
    );
  }
}

class LearningStreak {
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastStudyDate;
  final List<DateTime> studyDates;
  final bool isActiveToday;

  LearningStreak({
    required this.currentStreak,
    required this.longestStreak,
    this.lastStudyDate,
    required this.studyDates,
    required this.isActiveToday,
  });

  factory LearningStreak.fromJson(Map<String, dynamic> json) {
    return LearningStreak(
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      lastStudyDate: json['lastStudyDate'] != null 
          ? DateTime.parse(json['lastStudyDate']) 
          : null,
      studyDates: (json['studyDates'] as List<dynamic>?)
          ?.map((date) => DateTime.parse(date))
          .toList() ?? [],
      isActiveToday: json['isActiveToday'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastStudyDate': lastStudyDate?.toIso8601String(),
      'studyDates': studyDates.map((date) => date.toIso8601String()).toList(),
      'isActiveToday': isActiveToday,
    };
  }
}

class QuickAction {
  final String id;
  final String title;
  final String description;
  final String icon;
  final String action;
  final Map<String, dynamic>? parameters;
  final bool isEnabled;

  QuickAction({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.action,
    this.parameters,
    required this.isEnabled,
  });

  factory QuickAction.fromJson(Map<String, dynamic> json) {
    return QuickAction(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'] ?? '',
      action: json['action'] ?? '',
      parameters: json['parameters'],
      isEnabled: json['isEnabled'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon': icon,
      'action': action,
      'parameters': parameters,
      'isEnabled': isEnabled,
    };
  }
}
