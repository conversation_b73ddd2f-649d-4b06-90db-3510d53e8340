import '../../../core/services/api_service.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/storage_helper.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';

class UserService {
  static const String _profileEndpoint = '${AppConstants.userEndpoint}/profile';
  static const String _profilePictureEndpoint = '${AppConstants.userEndpoint}/profile-picture';
  static const String _privacySettingsEndpoint = '${AppConstants.userEndpoint}/privacy-settings';
  static const String _deleteAccountEndpoint = '${AppConstants.userEndpoint}/account';

  // Get User Profile
  static Future<ApiResponse> getUserProfile() async {
    try {
      final response = await ApiService.get(_profileEndpoint);

      if (response.success) {
        final user = UserModel.fromJson(response.data['user']);
        
        // Update local storage with latest user data
        await StorageHelper.saveUser(user.toJson());
        
        return ApiResponse.success(
          data: user,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get user profile: ${e.toString()}',
      );
    }
  }

  // Update User Profile
  static Future<ApiResponse> updateUserProfile({
    String? name,
    String? email,
    List<String>? examPreference,
    UserPreferences? preferences,
  }) async {
    try {
      final request = UpdateProfileRequest(
        name: name,
        email: email,
        examPreference: examPreference,
        preferences: preferences,
      );

      final response = await ApiService.put(
        _profileEndpoint,
        body: request.toJson(),
      );

      if (response.success) {
        final user = UserModel.fromJson(response.data['user']);
        
        // Update local storage with updated user data
        await StorageHelper.saveUser(user.toJson());
        
        return ApiResponse.success(
          data: user,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update profile: ${e.toString()}',
      );
    }
  }

  // Upload Profile Picture
  static Future<ApiResponse> uploadProfilePicture(String imagePath) async {
    try {
      final response = await ApiService.multipart(
        _profilePictureEndpoint,
        {},
        files: {'profilePicture': imagePath},
      );

      if (response.success) {
        // Update local user data with new profile picture URL
        final currentUser = await StorageHelper.getUser();
        if (currentUser != null) {
          currentUser['profilePicture'] = response.data['profilePictureUrl'];
          await StorageHelper.saveUser(currentUser);
        }
        
        return response;
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to upload profile picture: ${e.toString()}',
      );
    }
  }

  // Update Privacy Settings
  static Future<ApiResponse> updatePrivacySettings(Map<String, dynamic> settings) async {
    try {
      final response = await ApiService.put(
        _privacySettingsEndpoint,
        body: settings,
      );

      if (response.success) {
        // Update local user preferences
        final currentUser = await StorageHelper.getUser();
        if (currentUser != null) {
          currentUser['preferences'] = {
            ...currentUser['preferences'] ?? {},
            'privacy': settings,
          };
          await StorageHelper.saveUser(currentUser);
        }
        
        return response;
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update privacy settings: ${e.toString()}',
      );
    }
  }

  // Delete Account
  static Future<ApiResponse> deleteAccount() async {
    try {
      final response = await ApiService.delete(_deleteAccountEndpoint);

      if (response.success) {
        // Clear all local data
        await StorageHelper.clearAll();
        
        return response;
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to delete account: ${e.toString()}',
      );
    }
  }

  // Update Notification Preferences
  static Future<ApiResponse> updateNotificationPreferences(
    NotificationPreferences preferences,
  ) async {
    try {
      final response = await updateUserProfile(
        preferences: UserPreferences(
          language: (await getCurrentUserPreferences())?.language ?? 'hindi',
          notifications: preferences,
        ),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update notification preferences: ${e.toString()}',
      );
    }
  }

  // Update Language Preference
  static Future<ApiResponse> updateLanguagePreference(String language) async {
    try {
      final currentPrefs = await getCurrentUserPreferences();
      
      final response = await updateUserProfile(
        preferences: UserPreferences(
          language: language,
          notifications: currentPrefs?.notifications ?? 
              NotificationPreferences(
                newClass: true,
                liveClass: true,
                testReminder: true,
                announcements: true,
              ),
        ),
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update language preference: ${e.toString()}',
      );
    }
  }

  // Update Exam Preferences
  static Future<ApiResponse> updateExamPreferences(List<String> examPreference) async {
    try {
      final response = await updateUserProfile(examPreference: examPreference);
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update exam preferences: ${e.toString()}',
      );
    }
  }

  // Helper Methods
  static Future<UserModel?> getCurrentUser() async {
    try {
      final userData = await StorageHelper.getUser();
      if (userData != null) {
        return UserModel.fromJson(userData);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<UserPreferences?> getCurrentUserPreferences() async {
    try {
      final user = await getCurrentUser();
      return user?.preferences;
    } catch (e) {
      return null;
    }
  }

  static Future<List<String>> getCurrentUserExamPreferences() async {
    try {
      final user = await getCurrentUser();
      return user?.examPreference ?? [];
    } catch (e) {
      return [];
    }
  }

  static Future<bool> isPremiumUser() async {
    try {
      final user = await getCurrentUser();
      return user?.subscription.isActive ?? false;
    } catch (e) {
      return false;
    }
  }

  static Future<List<String>> getPurchasedCourses() async {
    try {
      final user = await getCurrentUser();
      return user?.subscription.purchasedCourses ?? [];
    } catch (e) {
      return [];
    }
  }

  // Sync user data from server
  static Future<ApiResponse> syncUserData() async {
    try {
      final response = await getUserProfile();
      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to sync user data: ${e.toString()}',
      );
    }
  }
}
