import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/custom_button.dart';
import 'test_screen.dart';

class TestResultScreen extends StatelessWidget {
  final Map<String, dynamic> test;
  final List<Map<String, dynamic>> questions;
  final Map<int, dynamic> answers;
  final int obtainedMarks;
  final int totalMarks;
  final int timeTaken;
  final String subjectName;
  final String courseName;

  const TestResultScreen({
    super.key,
    required this.test,
    required this.questions,
    required this.answers,
    required this.obtainedMarks,
    required this.totalMarks,
    required this.timeTaken,
    required this.subjectName,
    required this.courseName,
  });

  @override
  Widget build(BuildContext context) {
    final percentage = (obtainedMarks / totalMarks * 100).round();
    final grade = _getGrade(percentage);
    final gradeColor = _getGradeColor(percentage);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Results'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Score Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [gradeColor, gradeColor.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: gradeColor.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    _getGradeIcon(percentage),
                    size: 64,
                    color: AppColors.white,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    grade,
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$obtainedMarks / $totalMarks',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: AppColors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$percentage%',
                    style: const TextStyle(
                      fontSize: 18,
                      color: AppColors.white,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Test Details
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.greyLight),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Details',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('Test Name', test['title']),
                  _buildDetailRow('Subject', subjectName),
                  _buildDetailRow('Course', courseName),
                  _buildDetailRow('Total Questions', '${questions.length}'),
                  _buildDetailRow('Answered', '${answers.length}'),
                  _buildDetailRow('Correct', '${_getCorrectAnswersCount()}'),
                  _buildDetailRow('Wrong', '${_getWrongAnswersCount()}'),
                  _buildDetailRow('Unanswered', '${questions.length - answers.length}'),
                  _buildDetailRow('Time Taken', _formatTime(timeTaken)),
                  _buildDetailRow('Total Time', '${test['duration']} minutes'),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Performance Analysis
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.greyLight),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Performance Analysis',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildPerformanceChart(),
                  const SizedBox(height: 16),
                  _buildPerformanceInsights(),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Question-wise Analysis
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.greyLight),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Question-wise Analysis',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...questions.asMap().entries.map((entry) {
                    final index = entry.key;
                    final question = entry.value;
                    return _buildQuestionAnalysis(index, question);
                  }).toList(),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'Retake Test',
                    onPressed: () => _retakeTest(context),
                    backgroundColor: AppColors.primary,
                    icon: Icons.refresh,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: CustomButton(
                    text: 'Back to Tests',
                    onPressed: () => Navigator.of(context).popUntil(
                      (route) => route.settings.name == '/subject_detail',
                    ),
                    backgroundColor: AppColors.greyDark,
                    icon: Icons.arrow_back,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            CustomButton(
              text: 'Share Results',
              onPressed: () => _shareResults(context),
              backgroundColor: AppColors.success,
              icon: Icons.share,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart() {
    final correctCount = _getCorrectAnswersCount();
    final wrongCount = _getWrongAnswersCount();
    final unansweredCount = questions.length - answers.length;

    return Column(
      children: [
        // Progress bars
        _buildProgressBar('Correct', correctCount, questions.length, AppColors.success),
        const SizedBox(height: 8),
        _buildProgressBar('Wrong', wrongCount, questions.length, AppColors.error),
        const SizedBox(height: 8),
        _buildProgressBar('Unanswered', unansweredCount, questions.length, AppColors.grey),
      ],
    );
  }

  Widget _buildProgressBar(String label, int value, int total, Color color) {
    final percentage = total > 0 ? (value / total) : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            Text(
              '$value / $total',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: AppColors.greyLight,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
      ],
    );
  }

  Widget _buildPerformanceInsights() {
    final percentage = (obtainedMarks / totalMarks * 100).round();
    String insight;
    Color insightColor;
    IconData insightIcon;

    if (percentage >= 90) {
      insight = 'Excellent! You have mastered this topic.';
      insightColor = AppColors.success;
      insightIcon = Icons.star;
    } else if (percentage >= 75) {
      insight = 'Good job! Keep practicing to improve further.';
      insightColor = AppColors.primary;
      insightIcon = Icons.thumb_up;
    } else if (percentage >= 60) {
      insight = 'Fair performance. Focus on weak areas.';
      insightColor = AppColors.warning;
      insightIcon = Icons.trending_up;
    } else {
      insight = 'Needs improvement. Review the concepts again.';
      insightColor = AppColors.error;
      insightIcon = Icons.school;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: insightColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: insightColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(insightIcon, color: insightColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              insight,
              style: TextStyle(
                color: insightColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionAnalysis(int index, Map<String, dynamic> question) {
    final userAnswer = answers[index];
    final correctAnswer = question['correctAnswer'];
    bool isCorrect = false;
    bool isAnswered = answers.containsKey(index);

    if (isAnswered) {
      if (question['type'] == 'Fill in the Blanks') {
        isCorrect = userAnswer.toString().toLowerCase().trim() ==
                   correctAnswer.toString().toLowerCase().trim();
      } else {
        isCorrect = userAnswer == correctAnswer;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isAnswered
            ? (isCorrect ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1))
            : AppColors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isAnswered
              ? (isCorrect ? AppColors.success : AppColors.error)
              : AppColors.grey,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isAnswered
                      ? (isCorrect ? AppColors.success : AppColors.error)
                      : AppColors.grey,
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  question['question'],
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Icon(
                isAnswered
                    ? (isCorrect ? Icons.check_circle : Icons.cancel)
                    : Icons.help_outline,
                color: isAnswered
                    ? (isCorrect ? AppColors.success : AppColors.error)
                    : AppColors.grey,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (question['type'] == 'MCQ' || question['type'] == 'True/False') ...[
            Text(
              'Correct Answer: ${question['options'][correctAnswer]}',
              style: const TextStyle(
                color: AppColors.success,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (isAnswered && !isCorrect)
              Text(
                'Your Answer: ${question['options'][userAnswer]}',
                style: const TextStyle(
                  color: AppColors.error,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
          ] else if (question['type'] == 'Fill in the Blanks') ...[
            Text(
              'Correct Answer: ${correctAnswer}',
              style: const TextStyle(
                color: AppColors.success,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            if (isAnswered && !isCorrect)
              Text(
                'Your Answer: ${userAnswer}',
                style: const TextStyle(
                  color: AppColors.error,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
          ],
          if (!isAnswered)
            const Text(
              'Not Answered',
              style: TextStyle(
                color: AppColors.grey,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  int _getCorrectAnswersCount() {
    int count = 0;
    for (int i = 0; i < questions.length; i++) {
      if (answers.containsKey(i)) {
        final question = questions[i];
        final userAnswer = answers[i];
        final correctAnswer = question['correctAnswer'];

        bool isCorrect = false;
        if (question['type'] == 'Fill in the Blanks') {
          isCorrect = userAnswer.toString().toLowerCase().trim() ==
                     correctAnswer.toString().toLowerCase().trim();
        } else {
          isCorrect = userAnswer == correctAnswer;
        }

        if (isCorrect) count++;
      }
    }
    return count;
  }

  int _getWrongAnswersCount() {
    int count = 0;
    for (int i = 0; i < questions.length; i++) {
      if (answers.containsKey(i)) {
        final question = questions[i];
        final userAnswer = answers[i];
        final correctAnswer = question['correctAnswer'];

        bool isCorrect = false;
        if (question['type'] == 'Fill in the Blanks') {
          isCorrect = userAnswer.toString().toLowerCase().trim() ==
                     correctAnswer.toString().toLowerCase().trim();
        } else {
          isCorrect = userAnswer == correctAnswer;
        }

        if (!isCorrect) count++;
      }
    }
    return count;
  }

  String _getGrade(int percentage) {
    if (percentage >= 90) return 'A+';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 50) return 'C';
    return 'F';
  }

  Color _getGradeColor(int percentage) {
    if (percentage >= 90) return AppColors.success;
    if (percentage >= 80) return AppColors.primary;
    if (percentage >= 70) return AppColors.info;
    if (percentage >= 60) return AppColors.warning;
    return AppColors.error;
  }

  IconData _getGradeIcon(int percentage) {
    if (percentage >= 90) return Icons.star;
    if (percentage >= 80) return Icons.thumb_up;
    if (percentage >= 70) return Icons.trending_up;
    if (percentage >= 60) return Icons.school;
    return Icons.trending_down;
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes}m ${remainingSeconds}s';
  }

  void _retakeTest(BuildContext context) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => TestScreen(
          test: test,
          subjectName: subjectName,
          courseName: courseName,
        ),
      ),
    );
  }

  void _shareResults(BuildContext context) {
    final percentage = (obtainedMarks / totalMarks * 100).round();
    final grade = _getGrade(percentage);

    final shareText = '''
🎯 Test Results - ${test['title']}

📊 Score: $obtainedMarks/$totalMarks ($percentage%)
🏆 Grade: $grade
📚 Subject: $subjectName
⏱️ Time: ${_formatTime(timeTaken)}

✅ Correct: ${_getCorrectAnswersCount()}
❌ Wrong: ${_getWrongAnswersCount()}
⚪ Unanswered: ${questions.length - answers.length}

Keep learning! 📖
''';

    // In a real app, you would use share_plus package
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Results copied to clipboard: $shareText'),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
