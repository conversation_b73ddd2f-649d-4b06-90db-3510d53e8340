import '../../../core/services/api_service.dart';
import '../../../core/constants/app_constants.dart';

class ExamService {
  static const String _examsEndpoint = AppConstants.examsEndpoint;

  // Get All Exams
  static Future<ApiResponse> getAllExams({
    int page = 1,
    int limit = 20,
    String? search,
    bool activeOnly = true,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      
      if (activeOnly) {
        queryParams['status'] = 'active';
      }

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_examsEndpoint?$queryString',
        requiresAuth: false,
      );

      if (response.success) {
        final exams = (response.data['exams'] as List<dynamic>)
            .map((exam) => ExamModel.fromJson(exam))
            .toList();

        return ApiResponse.success(
          data: {
            'exams': exams,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch exams: ${e.toString()}',
      );
    }
  }

  // Get Exam by ID
  static Future<ApiResponse> getExamById(String examId) async {
    try {
      final response = await ApiService.get(
        '$_examsEndpoint/$examId',
        requiresAuth: false,
      );

      if (response.success) {
        final exam = ExamModel.fromJson(response.data['exam']);
        
        return ApiResponse.success(
          data: exam,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch exam details: ${e.toString()}',
      );
    }
  }

  // Get Popular Exams
  static Future<ApiResponse> getPopularExams({
    int limit = 10,
  }) async {
    try {
      final response = await ApiService.get(
        '$_examsEndpoint/popular?limit=$limit',
        requiresAuth: false,
      );

      if (response.success) {
        final exams = (response.data['exams'] as List<dynamic>)
            .map((exam) => ExamModel.fromJson(exam))
            .toList();

        return ApiResponse.success(
          data: exams,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch popular exams: ${e.toString()}',
      );
    }
  }

  // Search Exams
  static Future<ApiResponse> searchExams({
    required String query,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, String>{
        'q': query,
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '${AppConstants.searchEndpoint}/exams?$queryString',
        requiresAuth: false,
      );

      if (response.success) {
        final exams = (response.data['exams'] as List<dynamic>)
            .map((exam) => ExamModel.fromJson(exam))
            .toList();

        return ApiResponse.success(
          data: {
            'exams': exams,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to search exams: ${e.toString()}',
      );
    }
  }

  // Get Exam Categories
  static Future<ApiResponse> getExamCategories() async {
    try {
      final response = await ApiService.get(
        '$_examsEndpoint/categories',
        requiresAuth: false,
      );

      if (response.success) {
        final categories = (response.data['categories'] as List<dynamic>)
            .map((category) => ExamCategory.fromJson(category))
            .toList();

        return ApiResponse.success(
          data: categories,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch exam categories: ${e.toString()}',
      );
    }
  }

  // Get Exams by Category
  static Future<ApiResponse> getExamsByCategory({
    required String categoryId,
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final response = await ApiService.get(
        '$_examsEndpoint/category/$categoryId?$queryString',
        requiresAuth: false,
      );

      if (response.success) {
        final exams = (response.data['exams'] as List<dynamic>)
            .map((exam) => ExamModel.fromJson(exam))
            .toList();

        return ApiResponse.success(
          data: {
            'exams': exams,
            'pagination': response.data['pagination'],
            'totalCount': response.data['totalCount'],
          },
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to fetch exams by category: ${e.toString()}',
      );
    }
  }
}

// Exam Model
class ExamModel {
  final String id;
  final String name;
  final String description;
  final String? fullName;
  final String categoryId;
  final String categoryName;
  final String? logoUrl;
  final ExamType type;
  final ExamLevel level;
  final ExamStatus status;
  final ExamStats stats;
  final List<String> subjects;
  final ExamSchedule? schedule;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ExamModel({
    required this.id,
    required this.name,
    required this.description,
    this.fullName,
    required this.categoryId,
    required this.categoryName,
    this.logoUrl,
    required this.type,
    required this.level,
    required this.status,
    required this.stats,
    required this.subjects,
    this.schedule,
    required this.createdAt,
    this.updatedAt,
  });

  factory ExamModel.fromJson(Map<String, dynamic> json) {
    return ExamModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      fullName: json['fullName'],
      categoryId: json['categoryId'] ?? '',
      categoryName: json['categoryName'] ?? '',
      logoUrl: json['logoUrl'],
      type: ExamType.fromString(json['type'] ?? 'competitive'),
      level: ExamLevel.fromString(json['level'] ?? 'intermediate'),
      status: ExamStatus.fromString(json['status'] ?? 'active'),
      stats: ExamStats.fromJson(json['stats'] ?? {}),
      subjects: List<String>.from(json['subjects'] ?? []),
      schedule: json['schedule'] != null 
          ? ExamSchedule.fromJson(json['schedule']) 
          : null,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'fullName': fullName,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'logoUrl': logoUrl,
      'type': type.value,
      'level': level.value,
      'status': status.value,
      'stats': stats.toJson(),
      'subjects': subjects,
      'schedule': schedule?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

enum ExamType {
  competitive('competitive'),
  academic('academic'),
  certification('certification'),
  entrance('entrance');

  const ExamType(this.value);
  final String value;

  static ExamType fromString(String value) {
    return ExamType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ExamType.competitive,
    );
  }
}

enum ExamLevel {
  beginner('beginner'),
  intermediate('intermediate'),
  advanced('advanced'),
  expert('expert');

  const ExamLevel(this.value);
  final String value;

  static ExamLevel fromString(String value) {
    return ExamLevel.values.firstWhere(
      (level) => level.value == value,
      orElse: () => ExamLevel.intermediate,
    );
  }
}

enum ExamStatus {
  active('active'),
  inactive('inactive'),
  upcoming('upcoming'),
  archived('archived');

  const ExamStatus(this.value);
  final String value;

  static ExamStatus fromString(String value) {
    return ExamStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ExamStatus.active,
    );
  }
}

class ExamStats {
  final int totalCourses;
  final int totalStudents;
  final int totalTests;
  final double averageRating;

  ExamStats({
    required this.totalCourses,
    required this.totalStudents,
    required this.totalTests,
    required this.averageRating,
  });

  factory ExamStats.fromJson(Map<String, dynamic> json) {
    return ExamStats(
      totalCourses: json['totalCourses'] ?? 0,
      totalStudents: json['totalStudents'] ?? 0,
      totalTests: json['totalTests'] ?? 0,
      averageRating: (json['averageRating'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCourses': totalCourses,
      'totalStudents': totalStudents,
      'totalTests': totalTests,
      'averageRating': averageRating,
    };
  }
}

class ExamSchedule {
  final DateTime? registrationStart;
  final DateTime? registrationEnd;
  final DateTime? examDate;
  final DateTime? resultDate;

  ExamSchedule({
    this.registrationStart,
    this.registrationEnd,
    this.examDate,
    this.resultDate,
  });

  factory ExamSchedule.fromJson(Map<String, dynamic> json) {
    return ExamSchedule(
      registrationStart: json['registrationStart'] != null 
          ? DateTime.parse(json['registrationStart']) 
          : null,
      registrationEnd: json['registrationEnd'] != null 
          ? DateTime.parse(json['registrationEnd']) 
          : null,
      examDate: json['examDate'] != null 
          ? DateTime.parse(json['examDate']) 
          : null,
      resultDate: json['resultDate'] != null 
          ? DateTime.parse(json['resultDate']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'registrationStart': registrationStart?.toIso8601String(),
      'registrationEnd': registrationEnd?.toIso8601String(),
      'examDate': examDate?.toIso8601String(),
      'resultDate': resultDate?.toIso8601String(),
    };
  }
}

class ExamCategory {
  final String id;
  final String name;
  final String description;
  final String? iconUrl;
  final int examCount;
  final int order;

  ExamCategory({
    required this.id,
    required this.name,
    required this.description,
    this.iconUrl,
    required this.examCount,
    required this.order,
  });

  factory ExamCategory.fromJson(Map<String, dynamic> json) {
    return ExamCategory(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      iconUrl: json['iconUrl'],
      examCount: json['examCount'] ?? 0,
      order: json['order'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'examCount': examCount,
      'order': order,
    };
  }
}
