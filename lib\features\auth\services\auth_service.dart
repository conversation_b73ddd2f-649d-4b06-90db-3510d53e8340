import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import '../../../core/services/api_service.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/storage_helper.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';

class AuthService {
  static const String _sendOtpEndpoint = '${AppConstants.authEndpoint}/send-otp';
  static const String _verifyOtpEndpoint = '${AppConstants.authEndpoint}/verify-otp';
  static const String _refreshTokenEndpoint = '${AppConstants.authEndpoint}/refresh-token';
  static const String _logoutEndpoint = '${AppConstants.authEndpoint}/logout';
  static const String _forgotPasswordEndpoint = '${AppConstants.authEndpoint}/forgot-password';
  static const String _resetPasswordEndpoint = '${AppConstants.authEndpoint}/reset-password';
  static const String _googleLoginEndpoint = '${AppConstants.authEndpoint}/google-login';

  // Send OTP
  static Future<ApiResponse> sendOtp(String phone) async {
    try {
      final request = SendOtpRequest(phone: phone);
      
      final response = await ApiService.post(
        _sendOtpEndpoint,
        body: request.toJson(),
        requiresAuth: false,
      );

      if (response.success) {
        final otpResponse = SendOtpResponse.fromJson(response.data);
        return ApiResponse.success(
          data: otpResponse,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to send OTP: ${e.toString()}',
      );
    }
  }

  // Verify OTP
  static Future<ApiResponse> verifyOtp({
    required String phone,
    required String otp,
    String? name,
  }) async {
    try {
      final deviceInfo = await _getDeviceInfo();
      final request = VerifyOtpRequest(
        phone: phone,
        otp: otp,
        name: name,
        deviceInfo: deviceInfo,
      );

      final response = await ApiService.post(
        _verifyOtpEndpoint,
        body: request.toJson(),
        requiresAuth: false,
      );

      if (response.success) {
        final verifyResponse = VerifyOtpResponse.fromJson(response.data);
        
        // Save token and user data
        await StorageHelper.saveToken(verifyResponse.token);
        await StorageHelper.saveUser(verifyResponse.user.toJson());
        await StorageHelper.saveDeviceInfo(deviceInfo.toJson());

        return ApiResponse.success(
          data: verifyResponse,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to verify OTP: ${e.toString()}',
      );
    }
  }

  // Refresh Token
  static Future<ApiResponse> refreshToken(String refreshToken) async {
    try {
      final request = RefreshTokenRequest(refreshToken: refreshToken);
      
      final response = await ApiService.post(
        _refreshTokenEndpoint,
        body: request.toJson(),
        requiresAuth: false,
      );

      if (response.success) {
        final refreshResponse = RefreshTokenResponse.fromJson(response.data);
        
        // Save new token
        await StorageHelper.saveToken(refreshResponse.token);
        
        return ApiResponse.success(
          data: refreshResponse,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to refresh token: ${e.toString()}',
      );
    }
  }

  // Logout
  static Future<ApiResponse> logout() async {
    try {
      final deviceInfo = await StorageHelper.getDeviceInfo();
      final request = LogoutRequest(
        deviceId: deviceInfo?['deviceId'],
      );

      final response = await ApiService.post(
        _logoutEndpoint,
        body: request.toJson(),
        requiresAuth: true,
      );

      // Clear local storage regardless of API response
      await StorageHelper.clearAll();

      return response;
    } catch (e) {
      // Clear local storage even if API call fails
      await StorageHelper.clearAll();
      
      return ApiResponse.error(
        message: 'Logout completed with errors: ${e.toString()}',
      );
    }
  }

  // Forgot Password
  static Future<ApiResponse> forgotPassword(String email) async {
    try {
      final request = ForgotPasswordRequest(email: email);
      
      final response = await ApiService.post(
        _forgotPasswordEndpoint,
        body: request.toJson(),
        requiresAuth: false,
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to send reset email: ${e.toString()}',
      );
    }
  }

  // Reset Password
  static Future<ApiResponse> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    try {
      final request = ResetPasswordRequest(
        email: email,
        token: token,
        newPassword: newPassword,
      );
      
      final response = await ApiService.post(
        _resetPasswordEndpoint,
        body: request.toJson(),
        requiresAuth: false,
      );

      return response;
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to reset password: ${e.toString()}',
      );
    }
  }

  // Google Login
  static Future<ApiResponse> googleLogin(String accessToken) async {
    try {
      final deviceInfo = await _getDeviceInfo();
      final request = SocialLoginRequest(
        provider: 'google',
        accessToken: accessToken,
        deviceInfo: deviceInfo,
      );

      final response = await ApiService.post(
        _googleLoginEndpoint,
        body: request.toJson(),
        requiresAuth: false,
      );

      if (response.success) {
        final verifyResponse = VerifyOtpResponse.fromJson(response.data);
        
        // Save token and user data
        await StorageHelper.saveToken(verifyResponse.token);
        await StorageHelper.saveUser(verifyResponse.user.toJson());
        await StorageHelper.saveDeviceInfo(deviceInfo.toJson());

        return ApiResponse.success(
          data: verifyResponse,
          message: response.message,
        );
      } else {
        return response;
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to login with Google: ${e.toString()}',
      );
    }
  }

  // Get Current User
  static Future<UserModel?> getCurrentUser() async {
    try {
      final userData = await StorageHelper.getUser();
      if (userData != null) {
        return UserModel.fromJson(userData);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    try {
      final token = await StorageHelper.getToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Get Device Info
  static Future<DeviceInfo> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();
      String deviceId = '';
      String platform = '';
      String version = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.id;
        platform = 'android';
        version = androidInfo.version.release;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? '';
        platform = 'ios';
        version = iosInfo.systemVersion;
      } else {
        deviceId = 'web_${DateTime.now().millisecondsSinceEpoch}';
        platform = 'web';
        version = '1.0';
      }

      return DeviceInfo(
        deviceId: deviceId,
        deviceType: 'mobile',
        platform: platform,
        version: version,
      );
    } catch (e) {
      // Fallback device info
      return DeviceInfo(
        deviceId: 'unknown_${DateTime.now().millisecondsSinceEpoch}',
        deviceType: 'mobile',
        platform: 'unknown',
        version: '1.0',
      );
    }
  }
}
