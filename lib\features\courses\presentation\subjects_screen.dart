import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../content/presentation/class_content_screen.dart';

class SubjectsScreen extends StatelessWidget {
  final String courseName;
  final String examName;

  const SubjectsScreen({
    super.key,
    required this.courseName,
    required this.examName,
  });

  // Sample subjects data based on course
  List<Map<String, dynamic>> _getSubjects(String course) {
    if (course.contains('BPSC')) {
      return [
        {
          'name': 'Indian Polity',
          'description': 'Constitution, Fundamental Rights, Governance',
          'videos': 25,
          'pdfs': 8,
          'tests': 5,
          'progress': 0.7,
          'icon': Icons.account_balance,
          'color': AppColors.primary,
        },
        {
          'name': 'Indian History',
          'description': 'Ancient, Medieval, Modern History',
          'videos': 30,
          'pdfs': 12,
          'tests': 6,
          'progress': 0.4,
          'icon': Icons.history_edu,
          'color': AppColors.secondary,
        },
        {
          'name': 'Geography',
          'description': 'Physical, Human, Economic Geography',
          'videos': 22,
          'pdfs': 10,
          'tests': 4,
          'progress': 0.6,
          'icon': Icons.public,
          'color': AppColors.accent,
        },
        {
          'name': 'Economy',
          'description': 'Indian Economy, Budget, Banking',
          'videos': 28,
          'pdfs': 15,
          'tests': 7,
          'progress': 0.3,
          'icon': Icons.trending_up,
          'color': AppColors.primaryLight,
        },
        {
          'name': 'General Science',
          'description': 'Physics, Chemistry, Biology',
          'videos': 35,
          'pdfs': 18,
          'tests': 8,
          'progress': 0.5,
          'icon': Icons.science,
          'color': AppColors.secondaryLight,
        },
        {
          'name': 'Current Affairs',
          'description': 'National, International, Sports',
          'videos': 20,
          'pdfs': 25,
          'tests': 10,
          'progress': 0.8,
          'icon': Icons.newspaper,
          'color': AppColors.info,
        },
      ];
    } else if (course.contains('SSC')) {
      return [
        {
          'name': 'Quantitative Aptitude',
          'description': 'Mathematics, Data Interpretation',
          'videos': 40,
          'pdfs': 15,
          'tests': 12,
          'progress': 0.6,
          'icon': Icons.calculate,
          'color': AppColors.primary,
        },
        {
          'name': 'English Language',
          'description': 'Grammar, Comprehension, Vocabulary',
          'videos': 25,
          'pdfs': 10,
          'tests': 8,
          'progress': 0.7,
          'icon': Icons.language,
          'color': AppColors.secondary,
        },
        {
          'name': 'General Intelligence',
          'description': 'Reasoning, Logical Analysis',
          'videos': 30,
          'pdfs': 12,
          'tests': 10,
          'progress': 0.5,
          'icon': Icons.psychology,
          'color': AppColors.accent,
        },
        {
          'name': 'General Awareness',
          'description': 'Current Affairs, Static GK',
          'videos': 35,
          'pdfs': 20,
          'tests': 15,
          'progress': 0.4,
          'icon': Icons.lightbulb,
          'color': AppColors.warning,
        },
      ];
    } else {
      return [
        {
          'name': 'Subject 1',
          'description': 'Description for subject 1',
          'videos': 20,
          'pdfs': 8,
          'tests': 5,
          'progress': 0.5,
          'icon': Icons.book,
          'color': AppColors.primary,
        },
        {
          'name': 'Subject 2',
          'description': 'Description for subject 2',
          'videos': 25,
          'pdfs': 10,
          'tests': 6,
          'progress': 0.3,
          'icon': Icons.book,
          'color': AppColors.secondary,
        },
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final subjects = _getSubjects(courseName);

    return Scaffold(
      appBar: AppBar(
        title: Text(courseName),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    courseName,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Select a subject to start learning',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.accentLight,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Subjects List
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Subjects',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: subjects.length,
                    itemBuilder: (context, index) {
                      final subject = subjects[index];
                      return _buildSubjectCard(context, subject);
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectCard(BuildContext context, Map<String, dynamic> subject) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToClassContent(context, subject),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: (subject['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      subject['icon'] as IconData,
                      color: subject['color'] as Color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subject['name'],
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subject['description'],
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    color: AppColors.textHint,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progress',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${(subject['progress'] * 100).toInt()}%',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: subject['color'] as Color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: subject['progress'],
                    backgroundColor: AppColors.greyLight,
                    valueColor: AlwaysStoppedAnimation<Color>(subject['color'] as Color),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Stats Row
              Row(
                children: [
                  _buildStatChip(Icons.play_circle, '${subject['videos']} Videos'),
                  const SizedBox(width: 12),
                  _buildStatChip(Icons.picture_as_pdf, '${subject['pdfs']} PDFs'),
                  const SizedBox(width: 12),
                  _buildStatChip(Icons.quiz, '${subject['tests']} Tests'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.greyLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppColors.textSecondary),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToClassContent(BuildContext context, Map<String, dynamic> subject) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ClassContentScreen(
          subjectName: subject['name'],
          courseName: courseName,
        ),
      ),
    );
  }
}
