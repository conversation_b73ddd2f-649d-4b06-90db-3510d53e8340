// 🧪 Authentication API Integration Test
// This file tests the Flutter app's integration with the backend authentication APIs

import 'package:flutter_test/flutter_test.dart';
import 'package:new_utkrishta_app/features/auth/services/auth_service.dart';
import 'package:new_utkrishta_app/core/services/api_service.dart';

void main() {
  group('Authentication API Integration Tests', () {
    
    setUpAll(() {
      // Initialize API service for testing
      // Note: Make sure backend server is running on localhost:5000
    });

    test('Send OTP API Integration', () async {
      // Test phone number
      const testPhone = '+919876543210';
      
      // Call the send OTP API
      final response = await AuthService.sendOtp(testPhone);
      
      // Verify response structure
      expect(response.success, isTrue);
      expect(response.message, equals('OTP sent successfully'));
      expect(response.data, isNotNull);
      
      // Verify response data structure
      final otpResponse = response.data;
      expect(otpResponse.phone, equals(testPhone));
      expect(otpResponse.expiresIn, isNotNull);
      expect(otpResponse.attemptsLeft, isA<int>());
      
      print('✅ Send OTP API: Working correctly');
      print('📱 Phone: ${otpResponse.phone}');
      print('⏰ Expires In: ${otpResponse.expiresIn} seconds');
      print('🔄 Attempts Left: ${otpResponse.attemptsLeft}');
    });

    test('Verify OTP API Validation', () async {
      // Test with invalid OTP to verify validation
      const testPhone = '+919876543210';
      const invalidOtp = '123456';
      const testName = 'Test User';
      
      // Call the verify OTP API with invalid OTP
      final response = await AuthService.verifyOtp(
        phone: testPhone,
        otp: invalidOtp,
        name: testName,
      );
      
      // Should fail with invalid OTP
      expect(response.success, isFalse);
      expect(response.message, contains('Invalid OTP'));
      
      print('✅ Verify OTP API: Validation working correctly');
      print('🚫 Invalid OTP properly rejected');
      print('📝 Error Message: ${response.message}');
    });

    test('API Response Format Consistency', () async {
      // Test that all API responses follow the same format
      const testPhone = '+919876543210';
      
      final sendOtpResponse = await AuthService.sendOtp(testPhone);
      
      // Check response structure
      expect(sendOtpResponse.success, isA<bool>());
      expect(sendOtpResponse.message, isA<String>());
      expect(sendOtpResponse.data, isNotNull);
      
      print('✅ API Response Format: Consistent across all endpoints');
    });

    test('Error Handling Integration', () async {
      // Test with invalid phone number
      const invalidPhone = 'invalid-phone';
      
      final response = await AuthService.sendOtp(invalidPhone);
      
      // Should handle error gracefully
      expect(response.success, isFalse);
      expect(response.message, isNotNull);
      
      print('✅ Error Handling: Working correctly');
      print('🚫 Invalid input properly handled');
    });

    test('Device Information Integration', () async {
      // Test that device information is properly sent
      const testPhone = '+919876543210';
      const testOtp = '111111'; // Test OTP (will be rejected but tests the format)
      const testName = 'Test User';
      
      final response = await AuthService.verifyOtp(
        phone: testPhone,
        otp: testOtp,
        name: testName,
      );
      
      // Even though OTP is invalid, the request format should be correct
      expect(response.success, isFalse);
      expect(response.message, equals('Invalid OTP')); // Not a format error
      
      print('✅ Device Information: Properly integrated');
      print('📱 Device info sent correctly in requests');
    });
  });

  group('Backend Compatibility Tests', () {
    
    test('Response Format Matches Flutter Models', () async {
      const testPhone = '+919876543210';
      
      final response = await AuthService.sendOtp(testPhone);
      
      if (response.success) {
        final otpResponse = response.data;
        
        // Verify all expected fields are present
        expect(otpResponse.phone, isNotNull);
        expect(otpResponse.expiresIn, isNotNull);
        expect(otpResponse.attemptsLeft, isNotNull);
        
        print('✅ Backend Compatibility: Perfect match with Flutter models');
      }
    });

    test('Error Response Format Compatibility', () async {
      const invalidPhone = '';
      
      final response = await AuthService.sendOtp(invalidPhone);
      
      // Error response should be properly formatted
      expect(response.success, isFalse);
      expect(response.message, isNotNull);
      
      print('✅ Error Response Compatibility: Flutter can handle all error formats');
    });
  });
}

// 🚀 Test Runner Instructions:
// 
// To run these tests:
// 1. Make sure the backend server is running on localhost:5000
// 2. Run: flutter test test_auth_integration.dart
// 
// Expected Results:
// ✅ All tests should pass
// ✅ Console output should show API integration working
// ✅ Error handling should be robust
// 
// If any test fails, check:
// 1. Backend server is running
// 2. API endpoints are accessible
// 3. Response formats match the specification
