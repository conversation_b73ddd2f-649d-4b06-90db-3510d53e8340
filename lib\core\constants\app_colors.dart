import 'package:flutter/material.dart';

/// App color palette based on the specified colors:
/// 10002b-240046-3c096c-5a189a-7b2cbf-9d4edd-c77dff-e0aaff
class AppColors {
  AppColors._();

  // Primary color palette
  static const Color primary = Color(0xFF10002B);
  static const Color primaryDark = Color(0xFF240046);
  static const Color primaryMedium = Color(0xFF3C096C);
  static const Color primaryLight = Color(0xFF5A189A);
  
  // Secondary color palette
  static const Color secondary = Color(0xFF7B2CBF);
  static const Color secondaryLight = Color(0xFF9D4EDD);
  static const Color accent = Color(0xFFC77DFF);
  static const Color accentLight = Color(0xFFE0AAFF);

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);

  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE53E3E);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);

  // Background colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF3F3F3);

  // Text colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryMedium, secondary],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, accent, accentLight],
  );

  // Shadow colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
}
