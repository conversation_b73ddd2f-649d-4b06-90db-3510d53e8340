import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

class DownloadCenterScreen extends StatefulWidget {
  const DownloadCenterScreen({super.key});

  @override
  State<DownloadCenterScreen> createState() => _DownloadCenterScreenState();
}

class _DownloadCenterScreenState extends State<DownloadCenterScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Sample downloaded content
  List<Map<String, dynamic>> get downloadedVideos => [
    {
      'title': 'Indian Polity - Fundamental Rights',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Indian Polity',
      'duration': '45:30',
      'size': '125 MB',
      'downloadDate': DateTime.now().subtract(const Duration(days: 2)),
      'quality': 'HD',
      'progress': 1.0,
      'isDownloaded': true,
    },
    {
      'title': 'Geography - Physical Features',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Geography',
      'duration': '38:15',
      'size': '98 MB',
      'downloadDate': DateTime.now().subtract(const Duration(days: 1)),
      'quality': 'HD',
      'progress': 1.0,
      'isDownloaded': true,
    },
    {
      'title': 'History - Ancient India',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'History',
      'duration': '52:20',
      'size': '142 MB',
      'downloadDate': DateTime.now().subtract(const Duration(hours: 5)),
      'quality': 'Full HD',
      'progress': 0.75,
      'isDownloaded': false,
    },
  ];

  List<Map<String, dynamic>> get downloadedPDFs => [
    {
      'title': 'Indian Polity Complete Notes',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Indian Polity',
      'pages': 120,
      'size': '15.2 MB',
      'downloadDate': DateTime.now().subtract(const Duration(days: 3)),
      'isDownloaded': true,
    },
    {
      'title': 'Geography Quick Revision',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Geography',
      'pages': 45,
      'size': '8.5 MB',
      'downloadDate': DateTime.now().subtract(const Duration(days: 1)),
      'isDownloaded': true,
    },
  ];

  List<Map<String, dynamic>> get downloadQueue => [
    {
      'title': 'Economy - Basic Concepts',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Economy',
      'type': 'video',
      'size': '156 MB',
      'progress': 0.45,
      'speed': '2.1 MB/s',
    },
    {
      'title': 'Current Affairs Monthly PDF',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Current Affairs',
      'type': 'pdf',
      'size': '12.8 MB',
      'progress': 0.80,
      'speed': '1.8 MB/s',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Download Center'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.video_library), text: 'Videos'),
            Tab(icon: Icon(Icons.picture_as_pdf), text: 'PDFs'),
            Tab(icon: Icon(Icons.download_for_offline), text: 'Queue'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.storage),
            onPressed: _showStorageInfo,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildVideosTab(),
          _buildPDFsTab(),
          _buildQueueTab(),
        ],
      ),
    );
  }

  Widget _buildVideosTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Storage Summary
          _buildStorageSummary('Videos', downloadedVideos.length, _getTotalVideoSize()),

          const SizedBox(height: 24),

          // Downloaded Videos
          Text(
            'Downloaded Videos',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: downloadedVideos.length,
            itemBuilder: (context, index) {
              final video = downloadedVideos[index];
              return _buildVideoCard(video);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPDFsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Storage Summary
          _buildStorageSummary('PDFs', downloadedPDFs.length, _getTotalPDFSize()),

          const SizedBox(height: 24),

          // Downloaded PDFs
          Text(
            'Downloaded PDFs',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: downloadedPDFs.length,
            itemBuilder: (context, index) {
              final pdf = downloadedPDFs[index];
              return _buildPDFCard(pdf);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQueueTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Queue Summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.info, AppColors.info.withOpacity(0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.download_for_offline, color: AppColors.white, size: 32),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Download Queue',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${downloadQueue.length} items downloading',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          if (downloadQueue.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(Icons.download_done, size: 64, color: AppColors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'No downloads in queue',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'All downloads completed',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: downloadQueue.length,
              itemBuilder: (context, index) {
                final item = downloadQueue[index];
                return _buildQueueCard(item);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildStorageSummary(String type, int count, String totalSize) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.secondary, AppColors.secondary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              type == 'Videos' ? Icons.video_library : Icons.picture_as_pdf,
              color: AppColors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Downloaded $type',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$count items • $totalSize',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoCard(Map<String, dynamic> video) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.greyLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.play_circle, color: AppColors.primary, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      video['title'],
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      video['subject'],
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (video['isDownloaded'])
                Icon(Icons.download_done, color: AppColors.success, size: 20)
              else
                CircularProgressIndicator(
                  value: video['progress'],
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text('${video['duration']} • ${video['size']} • ${video['quality']}'),
              const Spacer(),
              if (video['isDownloaded'])
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.play_arrow, color: AppColors.primary),
                      onPressed: () => _playVideo(video),
                    ),
                    IconButton(
                      icon: Icon(Icons.delete, color: AppColors.error),
                      onPressed: () => _deleteDownload(video),
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPDFCard(Map<String, dynamic> pdf) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.greyLight),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.picture_as_pdf, color: AppColors.error, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  pdf['title'],
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${pdf['pages']} pages • ${pdf['size']}',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                icon: Icon(Icons.open_in_new, color: AppColors.primary),
                onPressed: () => _openPDF(pdf),
              ),
              IconButton(
                icon: Icon(Icons.delete, color: AppColors.error),
                onPressed: () => _deleteDownload(pdf),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQueueCard(Map<String, dynamic> item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.greyLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  item['type'] == 'video' ? Icons.video_library : Icons.picture_as_pdf,
                  color: AppColors.info,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['title'],
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${item['size']} • ${item['speed']}',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(Icons.pause, color: AppColors.warning),
                onPressed: () => _pauseDownload(item),
              ),
              IconButton(
                icon: Icon(Icons.cancel, color: AppColors.error),
                onPressed: () => _cancelDownload(item),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: item['progress'],
            backgroundColor: AppColors.greyLight,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.info),
          ),
          const SizedBox(height: 4),
          Text(
            '${(item['progress'] * 100).round()}% completed',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _getTotalVideoSize() => '365 MB';
  String _getTotalPDFSize() => '23.7 MB';

  void _showStorageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Information'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Downloads: 388.7 MB'),
            SizedBox(height: 8),
            Text('Available Space: 2.1 GB'),
            SizedBox(height: 8),
            Text('Videos: 365 MB'),
            Text('PDFs: 23.7 MB'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _playVideo(Map<String, dynamic> video) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Playing ${video['title']}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _openPDF(Map<String, dynamic> pdf) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${pdf['title']}'),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _deleteDownload(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Download'),
        content: Text('Are you sure you want to delete "${item['title']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Download deleted successfully'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _pauseDownload(Map<String, dynamic> item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Paused ${item['title']}'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _cancelDownload(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Download'),
        content: Text('Are you sure you want to cancel downloading "${item['title']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Download cancelled'),
                  backgroundColor: AppColors.error,
                ),
              );
            },
            child: const Text('Cancel Download'),
          ),
        ],
      ),
    );
  }
}
