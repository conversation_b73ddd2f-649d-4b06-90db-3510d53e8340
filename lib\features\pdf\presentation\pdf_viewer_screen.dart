import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/app_colors.dart';
import '../../../shared/widgets/custom_button.dart';

class PDFViewerScreen extends StatefulWidget {
  final Map<String, dynamic> pdf;
  final String subjectName;
  final String courseName;

  const PDFViewerScreen({
    super.key,
    required this.pdf,
    required this.subjectName,
    required this.courseName,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;
  bool _isLoading = true;
  bool _showControls = true;
  bool _isDownloaded = false;
  
  // Sample bookmarks for PDF
  List<Map<String, dynamic>> _bookmarks = [];

  @override
  void initState() {
    super.initState();
    _totalPages = widget.pdf['pages'] ?? 100;
    _isDownloaded = widget.pdf['isDownloaded'] ?? false;
    _loadPDF();
    _loadBookmarks();
  }

  void _loadPDF() {
    // Simulate PDF loading
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _loadBookmarks() {
    // Sample bookmarks data
    setState(() {
      _bookmarks = [
        {'page': 5, 'title': 'Important Definition'},
        {'page': 12, 'title': 'Key Formula'},
        {'page': 25, 'title': 'Practice Questions'},
      ];
    });
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages) {
      setState(() {
        _currentPage = page;
      });
    }
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel = (_zoomLevel + 0.25).clamp(0.5, 3.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = (_zoomLevel - 0.25).clamp(0.5, 3.0);
    });
  }

  void _addBookmark() {
    showDialog(
      context: context,
      builder: (context) => _PDFBookmarkDialog(
        currentPage: _currentPage,
        onSave: (title) {
          setState(() {
            _bookmarks.add({
              'page': _currentPage,
              'title': title,
            });
          });
        },
      ),
    );
  }

  void _downloadPDF() {
    setState(() {
      _isDownloaded = true;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${widget.pdf['title']} downloaded successfully!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _sharePDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing PDF...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.pdf['title']),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_add),
            onPressed: _addBookmark,
          ),
          IconButton(
            icon: Icon(_isDownloaded ? Icons.download_done : Icons.download),
            onPressed: _isDownloaded ? null : _downloadPDF,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _sharePDF,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'bookmarks':
                  _showBookmarksBottomSheet();
                  break;
                case 'goto':
                  _showGoToPageDialog();
                  break;
                case 'info':
                  _showPDFInfo();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'bookmarks',
                child: Row(
                  children: [
                    Icon(Icons.bookmarks),
                    SizedBox(width: 8),
                    Text('Bookmarks'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'goto',
                child: Row(
                  children: [
                    Icon(Icons.navigate_next),
                    SizedBox(width: 8),
                    Text('Go to Page'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'info',
                child: Row(
                  children: [
                    Icon(Icons.info),
                    SizedBox(width: 8),
                    Text('PDF Info'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // PDF Viewer Area
          Expanded(
            child: Stack(
              children: [
                // PDF Content Area
                Container(
                  width: double.infinity,
                  color: AppColors.greyLight,
                  child: _isLoading
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Loading PDF...'),
                            ],
                          ),
                        )
                      : _buildPDFContent(),
                ),
                
                // Zoom Controls
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.zoom_in),
                          onPressed: _zoomIn,
                        ),
                        Text(
                          '${(_zoomLevel * 100).toInt()}%',
                          style: const TextStyle(fontSize: 12),
                        ),
                        IconButton(
                          icon: const Icon(Icons.zoom_out),
                          onPressed: _zoomOut,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // PDF Controls
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              border: Border(
                top: BorderSide(color: AppColors.greyLight),
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.first_page),
                  onPressed: () => _goToPage(1),
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: () => _goToPage(_currentPage - 1),
                ),
                Expanded(
                  child: Text(
                    'Page $_currentPage of $_totalPages',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () => _goToPage(_currentPage + 1),
                ),
                IconButton(
                  icon: const Icon(Icons.last_page),
                  onPressed: () => _goToPage(_totalPages),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPDFContent() {
    return Center(
      child: Transform.scale(
        scale: _zoomLevel,
        child: Container(
          width: 300,
          height: 400,
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowMedium,
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // PDF Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: AppColors.greyLight,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.picture_as_pdf, color: AppColors.error),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        widget.pdf['title'],
                        style: Theme.of(context).textTheme.titleSmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              
              // PDF Content Simulation
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Page $_currentPage',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        widget.subjectName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'This is a sample PDF content. In a real implementation, this would show the actual PDF pages using a PDF viewer plugin like flutter_pdfview or syncfusion_flutter_pdfviewer.',
                        style: TextStyle(height: 1.5),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        height: 100,
                        decoration: BoxDecoration(
                          color: AppColors.greyLight,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: Text(
                            'PDF Content Area',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBookmarksBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bookmarks',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (_bookmarks.isEmpty)
              const Text('No bookmarks yet.')
            else
              ListView.builder(
                shrinkWrap: true,
                itemCount: _bookmarks.length,
                itemBuilder: (context, index) {
                  final bookmark = _bookmarks[index];
                  return ListTile(
                    leading: const Icon(Icons.bookmark, color: AppColors.primary),
                    title: Text(bookmark['title']),
                    subtitle: Text('Page ${bookmark['page']}'),
                    onTap: () {
                      _goToPage(bookmark['page']);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showGoToPageDialog() {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Go to Page'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Page Number',
            hintText: '1 - $_totalPages',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final page = int.tryParse(controller.text);
              if (page != null) {
                _goToPage(page);
                Navigator.pop(context);
              }
            },
            child: const Text('Go'),
          ),
        ],
      ),
    );
  }

  void _showPDFInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('PDF Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Title', widget.pdf['title']),
            _buildInfoRow('Subject', widget.subjectName),
            _buildInfoRow('Course', widget.courseName),
            _buildInfoRow('Pages', '${widget.pdf['pages']}'),
            _buildInfoRow('Size', widget.pdf['size']),
            _buildInfoRow('Downloads', '${widget.pdf['downloadCount']}'),
            _buildInfoRow('Last Updated', widget.pdf['lastUpdated']),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

class _PDFBookmarkDialog extends StatefulWidget {
  final int currentPage;
  final Function(String) onSave;

  const _PDFBookmarkDialog({
    required this.currentPage,
    required this.onSave,
  });

  @override
  State<_PDFBookmarkDialog> createState() => _PDFBookmarkDialogState();
}

class _PDFBookmarkDialogState extends State<_PDFBookmarkDialog> {
  final _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Bookmark'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Page: ${widget.currentPage}'),
          const SizedBox(height: 16),
          TextField(
            controller: _controller,
            decoration: const InputDecoration(
              labelText: 'Bookmark Title',
              hintText: 'Enter a description for this bookmark',
            ),
            autofocus: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_controller.text.isNotEmpty) {
              widget.onSave(_controller.text);
              Navigator.of(context).pop();
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
