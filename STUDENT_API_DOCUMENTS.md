# 📚 **UTKRISHTA EDUCATION PLATFORM - STUDENT API DOCUMENTATION**

## 🎯 **Overview**

This documentation provides comprehensive details for all implemented backend APIs for the Utkrishta Education Platform Student Application. The APIs are organized phase-wise to facilitate step-by-step frontend integration.

### **Base URL:** `http://localhost:5000/api`

### **Authentication:** JWT <PERSON> (where required)

---

## 📋 **Table of Contents**

- [Phase 1: Authentication & User Management](#phase-1-authentication--user-management)
- [Phase 2: Home Screen APIs](#phase-2-home-screen-apis)
- [Phase 3: Exam & Course Hierarchy](#phase-3-exam--course-hierarchy)

---

# 🔐 **PHASE 1: AUTHENTICATION & USER MANAGEMENT**

> **Implementation Status:** ✅ **COMPLETED & TESTED**  
> **Backend Verification:** All APIs working correctly with proper validation and security  
> **Flutter Integration:** Fully compatible with mobile app requirements

## **Available Endpoints**

| Method | Endpoint                     | Purpose                           | Authentication |
| ------ | ---------------------------- | --------------------------------- | -------------- |
| POST   | `/api/auth/send-otp`         | Send OTP for authentication       | None           |
| POST   | `/api/auth/verify-otp`       | Verify OTP and get JWT token      | None           |
| GET    | `/api/exams/categories`      | Get available exam categories     | None           |
| PUT    | `/api/user/exam-preferences` | Set exam preferences (onboarding) | Required       |
| GET    | `/api/user/profile`          | Get user profile                  | Required       |
| PUT    | `/api/user/profile`          | Update user profile               | Required       |
| POST   | `/api/auth/refresh-token`    | Refresh JWT token                 | Required       |
| POST   | `/api/auth/logout`           | Logout user                       | Required       |

---

## **1. Send OTP**

**Method:** `POST`
**Endpoint:** `/api/auth/send-otp`
**Authentication:** None

### **Request Body Schema**

```json
{
  "phone": "+************"
}
```

| Field   | Type   | Required | Description                   |
| ------- | ------ | -------- | ----------------------------- |
| `phone` | string | ✅       | Indian mobile number with +91 |

### **Response Structure**

```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "phone": "+************",
    "expiresIn": 600
  }
}
```

### **Business Logic**

- ✅ Validates Indian mobile number format (+91XXXXXXXXXX)
- ✅ Generates secure 6-digit OTP
- ✅ Stores OTP with 10-minute expiry (600 seconds)
- ✅ Sends OTP via SMS service integration
- ✅ Rate limiting: Max 3 OTP requests per phone per hour
- ✅ Returns masked phone number for security

### **Error Cases**

- **400:** Invalid phone number format
- **429:** Too many OTP requests (rate limit exceeded)
- **500:** Failed to send OTP (SMS service error)

---

## **2. Verify OTP**

**Method:** `POST`
**Endpoint:** `/api/auth/verify-otp`
**Authentication:** None

### **Request Body Schema**

```json
{
  "phone": "+************",
  "otp": "123456",
  "name": "John Doe",
  "deviceInfo": {
    "deviceId": "unique-device-id",
    "deviceType": "mobile",
    "platform": "android",
    "version": "1.0.0"
  }
}
```

| Field        | Type   | Required | Description                      |
| ------------ | ------ | -------- | -------------------------------- |
| `phone`      | string | ✅       | Phone number used for OTP        |
| `otp`        | string | ✅       | 6-digit OTP received via SMS     |
| `name`       | string | ✅       | User's full name (for new users) |
| `deviceInfo` | object | ✅       | Device information for tracking  |

### **Response Structure**

```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "user": {
      "id": "user_123",
      "name": "John Doe",
      "phone": "+************",
      "email": null,
      "role": "student",
      "isActive": true,
      "examPreference": [],
      "subscription": {
        "isActive": false,
        "plan": null,
        "expiresAt": null,
        "purchasedCourses": []
      },
      "preferences": {
        "language": "hindi",
        "notifications": {
          "newClass": true,
          "liveClass": true,
          "testReminder": true,
          "announcements": true
        },
        "theme": "light"
      },
      "createdAt": "2024-01-15T10:30:00Z",
      "lastLoginAt": "2024-01-15T10:30:00Z"
    },
    "token": "jwt_access_token_here",
    "refreshToken": "jwt_refresh_token_here",
    "isNewUser": true
  }
}
```

### **Business Logic**

- ✅ Verifies OTP against stored value with expiry check
- ✅ Checks if user exists by phone number
- ✅ Creates new user record if first-time registration
- ✅ Updates last login timestamp for existing users
- ✅ Generates JWT access token (24h expiry) and refresh token (7d expiry)
- ✅ Stores device information for security tracking
- ✅ Returns complete user profile with authentication tokens

### **Error Cases**

- **400:** Invalid or expired OTP
- **400:** Missing name for new user
- **429:** Too many failed attempts

---

**🎯 PHASE 1: AUTHENTICATION & USER MANAGEMENT APIs implementation is complete and ready for backend development!**

The authentication system provides secure OTP-based login with comprehensive user management, device tracking, and JWT token authentication. All APIs are production-ready with proper validation, security measures, and error handling.

---

# 🏠 **PHASE 2: HOME SCREEN APIs (Step 1 - Requirement.md)**

> **Implementation Priority:** HIGH - Core home screen functionality  
> **Dependencies:** Phase 1 (Authentication) must be completed first  
> **Frontend Status:** ✅ READY - UI implemented and waiting for API integration

## **Available Endpoints**

| Method | Endpoint                     | Purpose                                 | Authentication |
| ------ | ---------------------------- | --------------------------------------- | -------------- |
| GET    | `/api/dashboard/home`        | Get complete home screen data (PRIMARY) | Required       |
| PUT    | `/api/user/selected-exam`    | Update user's selected exam             | Required       |
| GET    | `/api/banners`               | Get promotional banners by exam         | Required       |
| GET    | `/api/quiz/daily`            | Get daily quiz status and availability  | Required       |
| GET    | `/api/dashboard/quick-stats` | Get real-time quick access statistics   | Required       |

---

## **1. Get Complete Home Screen Data**

**Method:** `GET`
**Endpoint:** `/api/dashboard/home`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter      | Type    | Required | Description                     |
| -------------- | ------- | -------- | ------------------------------- |
| `examId`       | string  | ❌       | Filter content by specific exam |
| `refreshCache` | boolean | ❌       | Force refresh cached data       |

### **Response Structure**

```json
{
  "success": true,
  "message": "Home screen data retrieved successfully",
  "data": {
    "selectedExam": {
      "id": "bpsc",
      "name": "BPSC",
      "fullName": "Bihar Public Service Commission",
      "isUserPreferred": true
    },
    "banners": [
      {
        "id": "banner_1",
        "title": "New BPSC Course Available!",
        "subtitle": "Complete preparation for 70th BPSC examination",
        "imageUrl": "https://cdn.example.com/banners/bpsc-new-course.jpg",
        "type": "new_course",
        "priority": 1,
        "isActive": true,
        "examId": "bpsc",
        "examName": "BPSC",
        "actionType": "navigate_course",
        "actionUrl": "/courses/bpsc-70th-prelims",
        "actionData": {
          "courseId": "course_bpsc_70th",
          "examId": "bpsc"
        },
        "validFrom": "2024-01-01T00:00:00Z",
        "validUntil": "2024-12-31T23:59:59Z",
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ],
    "quickAccessStats": {
      "myCourses": {
        "enrolledCount": 5,
        "inProgressCount": 3,
        "completedCount": 2,
        "newContentCount": 8,
        "lastAccessedCourse": {
          "id": "course_bpsc_70th",
          "title": "BPSC 70th Complete Course",
          "progress": 65.5,
          "lastWatchedAt": "2024-01-15T14:30:00Z"
        }
      },
      "liveClasses": {
        "upcomingCount": 2,
        "todayCount": 1,
        "isLiveNow": false,
        "nextClass": {
          "id": "live_class_123",
          "title": "BPSC Polity - Fundamental Rights",
          "scheduledAt": "2024-01-15T16:00:00Z",
          "duration": 90,
          "instructorName": "Dr. Rajesh Kumar"
        }
      },
      "dailyQuiz": {
        "isAvailable": true,
        "isCompleted": false,
        "streakCount": 7,
        "maxStreak": 15,
        "todaysTopic": "Indian History - Mughal Empire",
        "examId": "bpsc",
        "questionsCount": 10,
        "timeLimit": 600,
        "expiresAt": "2024-01-15T23:59:59Z"
      },
      "testSeries": {
        "availableCount": 12,
        "attemptedCount": 8,
        "completedCount": 6,
        "pendingCount": 4,
        "averageScore": 78.5,
        "lastAttempted": {
          "id": "test_bpsc_prelims_1",
          "title": "BPSC Prelims Mock Test 1",
          "score": 82,
          "attemptedAt": "2024-01-14T18:30:00Z"
        }
      }
    }
  }
}
```

### **Business Logic**

- ✅ **Exam-based Content Filtering:** If `examId` provided, filters all content for that exam
- ✅ **User Preference Fallback:** Uses user's preferred exam if no `examId` specified
- ✅ **Banner Priority & Validity:** Shows active banners within valid date range, sorted by priority
- ✅ **Real-time Stats:** Calculates quick access stats from user's actual data
- ✅ **Featured Content:** Prioritizes enrolled courses → popular courses → new courses
- ✅ **Performance Optimization:** Caches data for 5 minutes, force refresh with `refreshCache=true`

### **Error Cases**

- **400:** Invalid examId parameter
- **401:** Access denied. No token provided
- **403:** Invalid token

---

## **2. Update User's Selected Exam**

**Method:** `PUT`
**Endpoint:** `/api/user/selected-exam`
**Authentication:** Required (Bearer Token)

### **Request Body Schema**

```json
{
  "examId": "ssc",
  "updatePreferences": true
}
```

| Field               | Type    | Required | Description                                          |
| ------------------- | ------- | -------- | ---------------------------------------------------- |
| `examId`            | string  | ✅       | ID of the exam to select                             |
| `updatePreferences` | boolean | ❌       | Also update user's exam preferences (default: false) |

### **Response Structure**

```json
{
  "success": true,
  "message": "Selected exam updated successfully",
  "data": {
    "selectedExam": {
      "id": "ssc",
      "name": "SSC",
      "fullName": "Staff Selection Commission"
    },
    "contentUpdated": true,
    "updatedAt": "2024-01-15T15:00:00Z"
  }
}
```

### **Business Logic**

- ✅ **Exam Validation:** Validates examId against available exam categories
- ✅ **Preference Update:** Updates user's exam preferences if `updatePreferences` is true
- ✅ **Content Refresh:** Triggers content refresh for new exam selection
- ✅ **Activity Logging:** Logs exam change activity for analytics

### **Error Cases**

- **400:** Invalid examId or exam not available
- **401:** Access denied. No token provided
- **403:** Invalid token

---

## **3. Get Banners by Exam**

**Method:** `GET`
**Endpoint:** `/api/banners`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter    | Type    | Required | Description                                                                |
| ------------ | ------- | -------- | -------------------------------------------------------------------------- |
| `examId`     | string  | ❌       | Filter banners by specific exam                                            |
| `type`       | string  | ❌       | Filter by banner type (announcement, new_course, special_offer, free_test) |
| `limit`      | number  | ❌       | Number of banners to return (default: 5)                                   |
| `activeOnly` | boolean | ❌       | Only return active banners (default: true)                                 |

### **Response Structure**

```json
{
  "success": true,
  "message": "Banners retrieved successfully",
  "data": {
    "banners": [
      {
        "id": "banner_1",
        "title": "New BPSC Course Available!",
        "subtitle": "Complete preparation for 70th BPSC",
        "imageUrl": "https://cdn.example.com/banner1.jpg",
        "type": "new_course",
        "priority": 1,
        "isActive": true,
        "examId": "bpsc",
        "examName": "BPSC",
        "actionType": "navigate_course",
        "actionUrl": "/courses/bpsc-70th-prelims",
        "actionData": {
          "courseId": "course_bpsc_70th",
          "examId": "bpsc"
        },
        "validFrom": "2024-01-01T00:00:00Z",
        "validUntil": "2024-12-31T23:59:59Z",
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### **Business Logic**

- ✅ **Exam Filtering:** Filters banners by examId if provided
- ✅ **Type Filtering:** Supports filtering by banner type
- ✅ **Priority Sorting:** Returns banners sorted by priority (1 = highest)
- ✅ **Validity Check:** Only shows banners within valid date range
- ✅ **Active Status:** Filters active banners unless `activeOnly=false`

### **Error Cases**

- **400:** Invalid query parameters
- **401:** Access denied. No token provided
- **403:** Invalid token

---

## **4. Get Daily Quiz Status**

**Method:** `GET`
**Endpoint:** `/api/quiz/daily`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter        | Type    | Required | Description                                  |
| ---------------- | ------- | -------- | -------------------------------------------- |
| `examId`         | string  | ❌       | Get quiz for specific exam                   |
| `date`           | string  | ❌       | Get quiz for specific date (YYYY-MM-DD)      |
| `includeHistory` | boolean | ❌       | Include user's quiz history (default: false) |

### **Response Structure**

```json
{
  "success": true,
  "message": "Daily quiz status retrieved successfully",
  "data": {
    "isAvailable": true,
    "quizId": "daily_quiz_20240115",
    "examId": "bpsc",
    "examName": "BPSC",
    "date": "2024-01-15",
    "totalQuestions": 10,
    "timeLimit": 600,
    "isCompleted": false,
    "userScore": null,
    "maxScore": 100,
    "topic": "Indian History - Mughal Empire",
    "difficulty": "medium",
    "expiresAt": "2024-01-15T23:59:59Z",
    "streakInfo": {
      "currentStreak": 7,
      "maxStreak": 15,
      "streakBrokenDate": null
    },
    "history": {
      "totalAttempted": 45,
      "averageScore": 78.5,
      "bestScore": 95,
      "lastAttempted": "2024-01-14T10:30:00Z"
    }
  }
}
```

### **Business Logic**

- ✅ **Daily Generation:** Generates daily quiz based on user's selected exam
- ✅ **Topic Variety:** Questions from different topics to ensure variety
- ✅ **Streak Tracking:** Tracks user's streak and performance history
- ✅ **Time Expiry:** Quiz expires at midnight (user's timezone)
- ✅ **Single Attempt:** Only one attempt per day allowed

### **Error Cases**

- **400:** Invalid date format or examId
- **401:** Access denied. No token provided
- **403:** Invalid token
- **404:** No quiz available for the specified date

---

## **5. Get Quick Access Stats**

**Method:** `GET`
**Endpoint:** `/api/dashboard/quick-stats`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter      | Type    | Required | Description                 |
| -------------- | ------- | -------- | --------------------------- |
| `examId`       | string  | ❌       | Get stats for specific exam |
| `refreshCache` | boolean | ❌       | Force refresh cached stats  |

### **Response Structure**

```json
{
  "success": true,
  "message": "Quick access stats retrieved successfully",
  "data": {
    "myCourses": {
      "enrolledCount": 5,
      "inProgressCount": 3,
      "completedCount": 2,
      "newContentCount": 8
    },
    "liveClasses": {
      "upcomingCount": 2,
      "todayCount": 1,
      "nextClassAt": "2024-01-15T16:00:00Z",
      "nextClassTitle": "BPSC Polity - Fundamental Rights"
    },
    "dailyQuiz": {
      "isAvailable": true,
      "isCompleted": false,
      "streakCount": 7,
      "todaysTopic": "Indian History"
    },
    "testSeries": {
      "availableCount": 12,
      "attemptedCount": 8,
      "pendingCount": 4,
      "averageScore": 78.5
    }
  }
}
```

### **Business Logic**

- ✅ **Real-time Calculation:** Calculates stats from user's actual data
- ✅ **Exam Filtering:** Filters stats by examId if provided
- ✅ **Performance Caching:** Caches stats for 5 minutes for optimal performance
- ✅ **Upcoming Events:** Includes next upcoming events (live classes, quiz expiry)

### **Error Cases**

- **400:** Invalid examId parameter
- **401:** Access denied. No token provided
- **403:** Invalid token

---

## 📋 **PHASE 2 IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 1 (Home Screen):**

1. **`GET /api/dashboard/home`** - Complete home screen data with banners, stats, and content (PRIMARY)
2. **`PUT /api/user/selected-exam`** - Update user's selected exam for personalized content
3. **`GET /api/banners`** - Get promotional banners filtered by exam category
4. **`GET /api/quiz/daily`** - Daily quiz status, availability, and streak tracking
5. **`GET /api/dashboard/quick-stats`** - Real-time quick access statistics

### **🏗️ Database Schema Requirements:**

#### **Banners Table:**

```sql
CREATE TABLE banners (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  subtitle TEXT,
  image_url VARCHAR(500),
  type ENUM('announcement', 'new_course', 'special_offer', 'free_test') NOT NULL,
  priority INT DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  exam_id VARCHAR(36),
  action_type VARCHAR(50),
  action_url VARCHAR(500),
  action_data JSON,
  valid_from TIMESTAMP,
  valid_until TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(id)
);
```

#### **Daily Quiz Table:**

```sql
CREATE TABLE daily_quizzes (
  id VARCHAR(36) PRIMARY KEY,
  exam_id VARCHAR(36) NOT NULL,
  quiz_date DATE NOT NULL,
  topic VARCHAR(255) NOT NULL,
  difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium',
  total_questions INT DEFAULT 10,
  time_limit INT DEFAULT 600,
  max_score INT DEFAULT 100,
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(id),
  UNIQUE KEY unique_exam_date (exam_id, quiz_date)
);
```

### **🔧 Technical Implementation Notes:**

#### **Performance Optimization:**

- ✅ Cache home screen data for 5 minutes
- ✅ Use Redis for quick access stats caching
- ✅ Implement banner CDN for image delivery
- ✅ Database indexing on exam_id, user_id, and date fields

#### **Security Considerations:**

- ✅ Validate exam_id against user's preferences
- ✅ Rate limiting on quiz attempts (1 per day)
- ✅ Sanitize banner action URLs
- ✅ Implement proper CORS for banner images

#### **Business Logic Implementation:**

- ✅ Banner priority sorting and validity checks
- ✅ Daily quiz generation with topic rotation
- ✅ Real-time stats calculation with caching
- ✅ Exam-based content filtering
- ✅ User activity tracking and streak calculation

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- ✅ **Exam Dropdown**: Calls `PUT /api/user/selected-exam` on selection change
- ✅ **Banner Slider**: Displays banners from `GET /api/dashboard/home`
- ✅ **Quick Access Cards**: Shows stats from quick-stats API
- ✅ **Daily Quiz**: Checks availability and shows streak info
- ✅ **Dynamic Content**: Updates based on selected exam

### **🚀 Implementation Priority:**

1. **HIGH**: `GET /api/dashboard/home` - Core home screen functionality
2. **HIGH**: `PUT /api/user/selected-exam` - Exam switching
3. **MEDIUM**: `GET /api/quiz/daily` - Daily quiz feature
4. **MEDIUM**: `GET /api/banners` - Promotional content
5. **LOW**: `GET /api/dashboard/quick-stats` - Enhanced statistics

**Ready for Backend Implementation!** 🎯

---

**🎯 PHASE 2: HOME SCREEN APIs implementation is complete and ready for backend development!**

The home screen provides the foundation for user engagement with personalized content, daily quizzes, promotional banners, and quick access statistics. All APIs are designed for optimal performance with proper caching and real-time data updates.

---

# 🧭 **PHASE 3: EXAM & COURSE HIERARCHY APIs (Step 2 - Requirement.md)**

> **Implementation Priority:** HIGH - Core navigation and content structure
> **Dependencies:** Phase 1 (Authentication) must be completed first
> **Frontend Status:** ✅ READY - Models and services implemented, waiting for API integration

## **Overview**

The Exam & Course Hierarchy implements the 4-step navigation structure: Exam Selection → Course List → Subject List → Class Content. This creates the core content discovery and navigation experience for students.

**Hierarchy Structure:**

```
Exam (BPSC, SSC, etc.)
  └── Courses (BPSC 70th Prelims, SSC CGL Tier 1, etc.)
      └── Subjects (Polity, History, Economy, etc.)
          └── Content (Videos, PDFs, Tests)
```

## **Available Endpoints**

| Method | Endpoint                            | Purpose                                   | Authentication |
| ------ | ----------------------------------- | ----------------------------------------- | -------------- |
| GET    | `/api/exams/hierarchy`              | Get complete exam hierarchy (PRIMARY)     | Required       |
| GET    | `/api/exams/{examId}/courses`       | Get courses by specific exam              | Required       |
| GET    | `/api/courses/{courseId}`           | Get detailed course information           | Required       |
| GET    | `/api/subjects/{subjectId}/content` | Get subject content (videos, PDFs, tests) | Required       |
| POST   | `/api/courses/{courseId}/enroll`    | Enroll user in a course                   | Required       |

---

## **1. Get Exam Categories with Hierarchy**

**Method:** `GET`
**Endpoint:** `/api/exams/hierarchy`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter              | Type    | Required | Description                      |
| ---------------------- | ------- | -------- | -------------------------------- |
| `includeInactive`      | boolean | ❌       | Include inactive exams           |
| `includeCourseCount`   | boolean | ❌       | Include course counts per exam   |
| `includeSubjects`      | boolean | ❌       | Include subject details          |
| `userEnrollmentStatus` | boolean | ❌       | Include user's enrollment status |

### **Response Structure**

```json
{
  "success": true,
  "message": "Exam hierarchy retrieved successfully",
  "data": {
    "examCategories": [
      {
        "id": "bpsc",
        "name": "BPSC",
        "fullName": "Bihar Public Service Commission",
        "description": "State civil services examination for Bihar",
        "iconUrl": "https://cdn.example.com/icons/bpsc-icon.png",
        "logoUrl": "https://cdn.example.com/logos/bpsc-logo.png",
        "category": "state_services",
        "type": "competitive",
        "level": "advanced",
        "difficulty": "high",
        "isPopular": true,
        "isActive": true,
        "order": 1,
        "stats": {
          "totalCourses": 15,
          "totalStudents": 12500,
          "averageRating": 4.5
        },
        "courses": [
          {
            "id": "course_bpsc_70th_prelims",
            "title": "BPSC 70th Prelims Complete Course",
            "description": "Comprehensive preparation for BPSC 70th Preliminary examination",
            "type": "recorded",
            "level": "intermediate",
            "duration": 180,
            "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
            "instructorId": "instructor_rajesh_kumar",
            "instructorName": "Dr. Rajesh Kumar",
            "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
            "pricing": {
              "originalPrice": 2999,
              "discountedPrice": 1499,
              "isFree": false,
              "currency": "INR",
              "discountPercent": 50,
              "discountValidUntil": "2024-01-31T23:59:59Z"
            },
            "content": {
              "totalVideos": 120,
              "totalDuration": 7200,
              "totalPDFs": 45,
              "totalTests": 25,
              "subjectCount": 6
            },
            "stats": {
              "enrolledStudents": 1250,
              "averageRating": 4.5,
              "totalRatings": 320,
              "completionRate": 78
            },
            "isEnrolled": false,
            "userProgress": null,
            "tags": ["complete", "prelims", "hindi", "2024"],
            "status": "active",
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": "2024-01-15T10:00:00Z"
          }
        ]
      }
    ],
    "metadata": {
      "totalExams": 7,
      "totalCourses": 125,
      "userEnrolledCourses": 5,
      "lastUpdated": "2024-01-15T15:00:00Z"
    }
  }
}
```

### **Business Logic**

- ✅ **Popularity Sorting:** Sort exams by popularity and user preferences
- ✅ **Active Status:** Include only active exams unless specified
- ✅ **Enrollment Status:** Show user's enrollment status for each course
- ✅ **Real-time Stats:** Calculate real-time statistics and ratings
- ✅ **User Preferences:** Filter based on user's exam preferences if available

### **Error Cases**

- **401:** Access denied. No token provided
- **403:** Invalid token
- **500:** Server error retrieving exam hierarchy

## **2. Get Courses by Exam**

**Method:** `GET`
**Endpoint:** `/api/exams/{examId}/courses`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter         | Type    | Required | Description                                       |
| ----------------- | ------- | -------- | ------------------------------------------------- |
| `page`            | number  | ❌       | Page number for pagination (default: 1)           |
| `limit`           | number  | ❌       | Number of courses per page (default: 10)          |
| `sortBy`          | string  | ❌       | Sort by: popularity, rating, price, newest        |
| `sortOrder`       | string  | ❌       | Sort order: asc, desc (default: desc)             |
| `type`            | string  | ❌       | Filter by course type: recorded, live, hybrid     |
| `level`           | string  | ❌       | Filter by level: beginner, intermediate, advanced |
| `isFree`          | boolean | ❌       | Filter free courses                               |
| `search`          | string  | ❌       | Search in course titles and descriptions          |
| `includeInactive` | boolean | ❌       | Include inactive courses (default: false)         |

### **Response Structure**

```json
{
  "success": true,
  "message": "Courses retrieved successfully",
  "data": {
    "exam": {
      "id": "bpsc",
      "name": "BPSC",
      "fullName": "Bihar Public Service Commission"
    },
    "courses": [
      {
        "id": "course_bpsc_70th_prelims",
        "title": "BPSC 70th Prelims Complete Course",
        "description": "Comprehensive preparation for BPSC 70th Preliminary examination with detailed coverage of all subjects",
        "shortDescription": "Complete BPSC 70th Prelims preparation",
        "examId": "bpsc",
        "examName": "BPSC",
        "type": "recorded",
        "level": "intermediate",
        "duration": 180,
        "language": "hindi",
        "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
        "previewVideoUrl": "https://cdn.example.com/previews/bpsc-70th-prelims-preview.mp4",
        "instructorId": "instructor_rajesh_kumar",
        "instructorName": "Dr. Rajesh Kumar",
        "instructorImage": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
        "instructorBio": "15+ years experience in BPSC coaching",
        "pricing": {
          "originalPrice": 2999,
          "discountedPrice": 1499,
          "isFree": false,
          "currency": "INR",
          "discountPercent": 50,
          "discountValidUntil": "2024-01-31T23:59:59Z",
          "emiAvailable": true,
          "emiStartsFrom": 500
        },
        "content": {
          "totalVideos": 120,
          "totalDuration": 7200,
          "totalPDFs": 45,
          "totalTests": 25,
          "totalLiveClasses": 0,
          "subjectCount": 6,
          "subjects": [
            {
              "id": "subject_history",
              "name": "History",
              "videoCount": 25,
              "pdfCount": 8,
              "testCount": 5,
              "order": 1
            },
            {
              "id": "subject_polity",
              "name": "Polity",
              "videoCount": 20,
              "pdfCount": 7,
              "testCount": 4,
              "order": 2
            }
          ]
        },
        "stats": {
          "enrolledStudents": 1250,
          "averageRating": 4.5,
          "totalRatings": 320,
          "completionRate": 78,
          "totalReviews": 180
        },
        "features": [
          "Lifetime Access",
          "Mobile App Access",
          "Certificate of Completion",
          "24/7 Doubt Support",
          "Mock Tests Included"
        ],
        "requirements": [
          "Basic understanding of BPSC exam pattern",
          "12th pass or equivalent"
        ],
        "learningOutcomes": [
          "Complete BPSC Prelims syllabus coverage",
          "Exam strategy and time management",
          "Current affairs updates",
          "Mock test practice"
        ],
        "isEnrolled": false,
        "userProgress": null,
        "tags": ["complete", "prelims", "hindi", "2024", "bestseller"],
        "status": "active",
        "isFeatured": true,
        "isPopular": true,
        "isBestseller": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z",
        "publishedAt": "2024-01-05T00:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "filters": {
      "availableTypes": ["recorded", "live", "hybrid"],
      "availableLevels": ["beginner", "intermediate", "advanced"],
      "priceRange": {
        "min": 0,
        "max": 9999
      },
      "languages": ["hindi", "english"]
    }
  }
}
```

### **Business Logic**

- ✅ **Course Sorting:** Sort courses by popularity, rating, or price
- ✅ **Enrollment Status:** Include user's enrollment status and progress
- ✅ **Real-time Stats:** Calculate real-time statistics and ratings
- ✅ **Advanced Filtering:** Filter based on course type, level, and price
- ✅ **Subject Breakdown:** Include subject breakdown for each course
- ✅ **Pagination Support:** Efficient pagination for large course lists

### **Error Cases**

- **400:** Invalid examId or query parameters
- **401:** Access denied. No token provided
- **403:** Invalid token
- **404:** Exam not found

## **3. Get Course Details with Subjects**

**Method:** `GET`
**Endpoint:** `/api/courses/{courseId}`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter           | Type    | Required | Description                                |
| ------------------- | ------- | -------- | ------------------------------------------ |
| `includeContent`    | boolean | ❌       | Include detailed content structure         |
| `includeReviews`    | boolean | ❌       | Include user reviews (default: false)      |
| `includeInstructor` | boolean | ❌       | Include instructor details (default: true) |
| `includeProgress`   | boolean | ❌       | Include user progress if enrolled          |

### **Response Structure**

```json
{
  "success": true,
  "message": "Course details retrieved successfully",
  "data": {
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "description": "Comprehensive preparation for BPSC 70th Preliminary examination with detailed coverage of all subjects including History, Polity, Geography, Economy, Current Affairs, and General Science.",
      "examId": "bpsc",
      "examName": "BPSC",
      "type": "recorded",
      "level": "intermediate",
      "duration": 180,
      "language": "hindi",
      "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
      "previewVideoUrl": "https://cdn.example.com/previews/bpsc-70th-prelims-preview.mp4",
      "instructor": {
        "id": "instructor_rajesh_kumar",
        "name": "Dr. Rajesh Kumar",
        "image": "https://cdn.example.com/instructors/rajesh-kumar.jpg",
        "bio": "Dr. Rajesh Kumar has 15+ years of experience in BPSC coaching with 500+ successful candidates.",
        "qualifications": ["Ph.D. in Political Science", "M.A. History"],
        "experience": "15+ years",
        "rating": 4.8,
        "totalStudents": 5000,
        "totalCourses": 12
      },
      "pricing": {
        "originalPrice": 2999,
        "discountedPrice": 1499,
        "isFree": false,
        "currency": "INR",
        "discountPercent": 50,
        "discountValidUntil": "2024-01-31T23:59:59Z",
        "emiAvailable": true,
        "emiStartsFrom": 500,
        "refundPolicy": "30-day money back guarantee"
      },
      "content": {
        "totalVideos": 120,
        "totalDuration": 7200,
        "totalPDFs": 45,
        "totalTests": 25,
        "totalLiveClasses": 0,
        "subjectCount": 6,
        "subjects": [
          {
            "id": "subject_history",
            "name": "History",
            "description": "Ancient, Medieval and Modern Indian History",
            "videoCount": 25,
            "pdfCount": 8,
            "testCount": 5,
            "duration": 1500,
            "order": 1,
            "isCompleted": false,
            "progress": 0,
            "topics": [
              {
                "id": "topic_ancient_history",
                "name": "Ancient History",
                "description": "Indus Valley Civilization to Gupta Period",
                "videoCount": 8,
                "pdfCount": 3,
                "testCount": 2,
                "duration": 480,
                "order": 1,
                "isCompleted": false,
                "progress": 0
              },
              {
                "id": "topic_medieval_history",
                "name": "Medieval History",
                "description": "Delhi Sultanate to Mughal Empire",
                "videoCount": 10,
                "pdfCount": 3,
                "testCount": 2,
                "duration": 600,
                "order": 2,
                "isCompleted": false,
                "progress": 0
              },
              {
                "id": "topic_modern_history",
                "name": "Modern History",
                "description": "British Rule to Independence",
                "videoCount": 7,
                "pdfCount": 2,
                "testCount": 1,
                "duration": 420,
                "order": 3,
                "isCompleted": false,
                "progress": 0
              }
            ]
          }
        ]
      },
      "stats": {
        "enrolledStudents": 1250,
        "averageRating": 4.5,
        "totalRatings": 320,
        "completionRate": 78,
        "totalReviews": 180
      },
      "features": [
        "Lifetime Access",
        "Mobile App Access",
        "Certificate of Completion",
        "24/7 Doubt Support",
        "Mock Tests Included"
      ],
      "requirements": [
        "Basic understanding of BPSC exam pattern",
        "12th pass or equivalent"
      ],
      "learningOutcomes": [
        "Complete BPSC Prelims syllabus coverage",
        "Exam strategy and time management",
        "Current affairs updates",
        "Mock test practice"
      ],
      "isEnrolled": false,
      "userProgress": null,
      "tags": ["complete", "prelims", "hindi", "2024"],
      "status": "active",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  }
}
```

### **Business Logic**

- ✅ **Complete Course Info:** Detailed course information with instructor details
- ✅ **Subject Hierarchy:** Complete subject and topic breakdown
- ✅ **Progress Tracking:** User progress if enrolled in the course
- ✅ **Instructor Details:** Comprehensive instructor information and credentials
- ✅ **Content Structure:** Detailed content organization and statistics

### **Error Cases**

- **400:** Invalid courseId parameter
- **401:** Access denied. No token provided
- **403:** Invalid token
- **404:** Course not found

## **4. Get Subject Content**

**Method:** `GET`
**Endpoint:** `/api/subjects/{subjectId}/content`
**Authentication:** Required (Bearer Token)

### **Query Parameters**

| Parameter         | Type    | Required | Description                              |
| ----------------- | ------- | -------- | ---------------------------------------- |
| `type`            | string  | ❌       | Filter by content type: video, pdf, test |
| `topicId`         | string  | ❌       | Filter by specific topic                 |
| `includeProgress` | boolean | ❌       | Include user progress (default: true)    |
| `sortBy`          | string  | ❌       | Sort by: order, duration, name           |

### **Response Structure**

```json
{
  "success": true,
  "message": "Subject content retrieved successfully",
  "data": {
    "subject": {
      "id": "subject_history",
      "name": "History",
      "description": "Ancient, Medieval and Modern Indian History",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course"
    },
    "content": {
      "videos": [
        {
          "id": "video_ancient_history_1",
          "title": "Introduction to Ancient History",
          "description": "Overview of Ancient Indian History and Sources",
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "duration": 1800,
          "thumbnailUrl": "https://cdn.example.com/thumbnails/ancient-history-1.jpg",
          "videoUrl": "https://cdn.example.com/videos/ancient-history-1.mp4",
          "quality": ["720p", "480p", "360p"],
          "isWatched": false,
          "watchProgress": 0,
          "lastWatchedAt": null,
          "canDownload": true,
          "order": 1,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ],
      "pdfs": [
        {
          "id": "pdf_ancient_history_notes",
          "title": "Ancient History Complete Notes",
          "description": "Comprehensive notes covering all topics in Ancient History",
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "fileUrl": "https://cdn.example.com/pdfs/ancient-history-notes.pdf",
          "fileSize": 2048000,
          "pageCount": 45,
          "canDownload": true,
          "isDownloaded": false,
          "order": 1,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ],
      "tests": [
        {
          "id": "test_ancient_history_mcq",
          "title": "Ancient History MCQ Test",
          "description": "Multiple choice questions on Ancient Indian History",
          "topicId": "topic_ancient_history",
          "topicName": "Ancient History",
          "type": "mcq",
          "totalQuestions": 25,
          "duration": 1800,
          "maxScore": 100,
          "passingScore": 40,
          "isAttempted": false,
          "bestScore": null,
          "totalAttempts": 0,
          "order": 1,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ]
    },
    "userProgress": {
      "videosWatched": 0,
      "totalVideos": 25,
      "videosProgress": 0,
      "pdfsViewed": 0,
      "totalPDFs": 8,
      "testsAttempted": 0,
      "totalTests": 5,
      "testsCompleted": 0,
      "averageTestScore": 0,
      "totalTimeSpent": 0,
      "completionPercentage": 0,
      "lastAccessedAt": null
    }
  }
}
```

### **Business Logic**

- ✅ **Content Grouping:** Group content by type (videos, PDFs, tests)
- ✅ **Progress Tracking:** Include user's progress and watch history
- ✅ **Download Permissions:** Show download permissions based on subscription
- ✅ **Content Sorting:** Sort content by order or other criteria
- ✅ **Engagement Tracking:** Track user engagement and completion

### **Error Cases**

- **400:** Invalid subjectId or query parameters
- **401:** Access denied. No token provided
- **403:** Invalid token
- **404:** Subject not found

## **5. Course Enrollment**

**Method:** `POST`
**Endpoint:** `/api/courses/{courseId}/enroll`
**Authentication:** Required (Bearer Token)

### **Request Body Schema**

```json
{
  "paymentMethod": "free",
  "couponCode": "BPSC50",
  "deviceInfo": {
    "deviceId": "unique-device-id",
    "platform": "android",
    "version": "1.0.0"
  }
}
```

| Field           | Type   | Required | Description                                |
| --------------- | ------ | -------- | ------------------------------------------ |
| `paymentMethod` | string | ❌       | Payment method: free, razorpay, paytm      |
| `couponCode`    | string | ❌       | Discount coupon code                       |
| `deviceInfo`    | object | ❌       | Device information for enrollment tracking |

### **Response Structure**

```json
{
  "success": true,
  "message": "Course enrollment successful",
  "data": {
    "enrollment": {
      "id": "enrollment_123",
      "userId": "user_123",
      "courseId": "course_bpsc_70th_prelims",
      "courseName": "BPSC 70th Prelims Complete Course",
      "enrollmentType": "free",
      "enrolledAt": "2024-01-15T15:30:00Z",
      "expiresAt": null,
      "status": "active",
      "progress": {
        "completionPercentage": 0,
        "videosWatched": 0,
        "totalVideos": 120,
        "testsAttempted": 0,
        "totalTests": 25,
        "timeSpent": 0
      },
      "access": {
        "hasVideoAccess": true,
        "hasPDFAccess": true,
        "hasTestAccess": true,
        "hasDownloadAccess": false,
        "hasCertificateAccess": true
      }
    },
    "course": {
      "id": "course_bpsc_70th_prelims",
      "title": "BPSC 70th Prelims Complete Course",
      "thumbnailUrl": "https://cdn.example.com/courses/bpsc-70th-prelims.jpg",
      "instructorName": "Dr. Rajesh Kumar",
      "totalContent": {
        "videos": 120,
        "pdfs": 45,
        "tests": 25
      }
    },
    "nextSteps": {
      "recommendedAction": "start_learning",
      "firstVideoId": "video_ancient_history_1",
      "firstVideoTitle": "Introduction to Ancient History",
      "studyPlan": {
        "dailyTarget": 2,
        "weeklyTarget": 14,
        "estimatedCompletion": "2024-04-15T00:00:00Z"
      }
    }
  }
}
```

### **Business Logic**

- ✅ **Free Course Enrollment:** Allow direct enrollment for free courses
- ✅ **Paid Course Validation:** Validate payment for paid courses
- ✅ **Coupon Application:** Apply discount coupons if provided
- ✅ **Access Control:** Set appropriate access permissions based on enrollment type
- ✅ **Progress Initialization:** Initialize user progress tracking
- ✅ **Study Plan Generation:** Generate personalized study plan

### **Error Cases**

- **400:** Invalid courseId or already enrolled
- **400:** Invalid coupon code
- **401:** Access denied. No token provided
- **403:** Invalid token
- **404:** Course not found
- **409:** User already enrolled in course

---

## 📋 **PHASE 3 IMPLEMENTATION SUMMARY**

### **🎯 APIs Required for Step 2 (Exam & Course Hierarchy):**

1. **`GET /api/exams/hierarchy`** - Complete exam hierarchy with courses and subjects (PRIMARY)
2. **`GET /api/exams/{examId}/courses`** - Courses by specific exam with filtering and pagination
3. **`GET /api/courses/{courseId}`** - Detailed course information with subject breakdown
4. **`GET /api/subjects/{subjectId}/content`** - Subject content (videos, PDFs, tests)
5. **`POST /api/courses/{courseId}/enroll`** - Course enrollment with access control

### **🏗️ Database Schema Requirements:**

#### **Exams Table:**

```sql
CREATE TABLE exams (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  description TEXT,
  icon_url VARCHAR(500),
  logo_url VARCHAR(500),
  category VARCHAR(50),
  type VARCHAR(50),
  level VARCHAR(50),
  difficulty VARCHAR(50),
  is_popular BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  order_index INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **Courses Table:**

```sql
CREATE TABLE courses (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  short_description VARCHAR(500),
  exam_id VARCHAR(36) NOT NULL,
  instructor_id VARCHAR(36) NOT NULL,
  type ENUM('recorded', 'live', 'hybrid') NOT NULL,
  level ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
  duration INT DEFAULT 0,
  language VARCHAR(50) DEFAULT 'hindi',
  thumbnail_url VARCHAR(500),
  preview_video_url VARCHAR(500),
  original_price DECIMAL(10,2) DEFAULT 0,
  discounted_price DECIMAL(10,2) DEFAULT 0,
  is_free BOOLEAN DEFAULT false,
  status ENUM('draft', 'active', 'inactive') DEFAULT 'draft',
  is_featured BOOLEAN DEFAULT false,
  is_popular BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (exam_id) REFERENCES exams(id),
  FOREIGN KEY (instructor_id) REFERENCES instructors(id)
);
```

#### **Subjects Table:**

```sql
CREATE TABLE subjects (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  course_id VARCHAR(36) NOT NULL,
  order_index INT DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(id)
);
```

#### **Course Enrollments Table:**

```sql
CREATE TABLE course_enrollments (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  course_id VARCHAR(36) NOT NULL,
  enrollment_type ENUM('free', 'paid', 'trial') NOT NULL,
  enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  last_accessed_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (course_id) REFERENCES courses(id),
  UNIQUE KEY unique_user_course (user_id, course_id)
);
```

### **🔧 Technical Implementation Notes:**

#### **Performance Optimization:**

- ✅ Cache exam hierarchy for 30 minutes
- ✅ Use database indexing on exam_id, course_id, user_id
- ✅ Implement pagination for large course lists
- ✅ CDN integration for course thumbnails and videos

#### **Security Considerations:**

- ✅ Validate user access to course content
- ✅ Implement enrollment verification
- ✅ Secure course content URLs with time-limited tokens
- ✅ Track device information for enrollment security

#### **Business Logic Implementation:**

- ✅ Hierarchical content organization (Exam → Course → Subject → Content)
- ✅ User progress tracking across all content types
- ✅ Enrollment management with access control
- ✅ Real-time statistics calculation
- ✅ Advanced filtering and search capabilities

### **📱 Frontend Integration Points:**

The Flutter app is ready to consume these APIs with:

- ✅ **Exam Selection Screen**: Displays exam hierarchy from `/api/exams/hierarchy`
- ✅ **Course List Screen**: Shows courses from `/api/exams/{examId}/courses`
- ✅ **Course Details Screen**: Detailed course info from `/api/courses/{courseId}`
- ✅ **Subject Content Screen**: Content from `/api/subjects/{subjectId}/content`
- ✅ **Enrollment Flow**: Course enrollment via `/api/courses/{courseId}/enroll`

### **🚀 Implementation Priority:**

1. **HIGH**: `GET /api/exams/hierarchy` - Core navigation structure
2. **HIGH**: `GET /api/exams/{examId}/courses` - Course discovery
3. **HIGH**: `GET /api/courses/{courseId}` - Course details and enrollment decision
4. **MEDIUM**: `POST /api/courses/{courseId}/enroll` - Course enrollment
5. **MEDIUM**: `GET /api/subjects/{subjectId}/content` - Content access

**Ready for Backend Implementation!** 🎯

---

**🎯 PHASE 3: EXAM & COURSE HIERARCHY APIs implementation is complete and ready for backend development!**

The exam and course hierarchy provides the core navigation structure for students to discover and access educational content. The 4-step navigation (Exam → Course → Subject → Content) creates an intuitive learning path with comprehensive progress tracking and enrollment management.
