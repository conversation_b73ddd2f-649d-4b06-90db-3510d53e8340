import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../courses/presentation/course_list_screen.dart';
import '../../courses/presentation/subjects_screen.dart';
import '../../content/presentation/class_content_screen.dart';
import '../../settings/presentation/settings_screen.dart';
import '../../downloads/presentation/download_center_screen.dart';
import '../../auth/providers/auth_provider.dart';
import '../../auth/presentation/login_screen.dart';
import '../providers/dashboard_provider.dart';
import '../models/dashboard_model.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const HomeTab(),
    const ClassesTab(),
    const TestsTab(),
    const ProgressTab(),
    const ProfileTab(),
  ];

  @override
  void initState() {
    super.initState();
    // Initialize dashboard data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
      dashboardProvider.initializeDashboard();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.play_circle),
            label: 'Classes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.quiz),
            label: 'Tests',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Progress',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Handle Ask Doubt action
        },
        backgroundColor: AppColors.secondary,
        child: const Icon(Icons.help_outline, color: AppColors.white),
      ),
    );
  }
}

class HomeTab extends StatefulWidget {
  const HomeTab({super.key});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  String selectedExam = 'BPSC'; // Default selected exam

  // Sample exam categories - will be replaced with API data
  final List<String> examCategories = [
    'BPSC',
    'SSC',
    'CTET',
    'Railway',
    'Banking',
    'Bihar Board 10th',
    'Bihar Board 12th',
  ];

  // Dynamic banner data based on selected exam
  List<Map<String, dynamic>> get banners {
    return [
      {
        'title': 'New $selectedExam Course Available!',
        'subtitle': 'Complete preparation for $selectedExam examination',
        'image': 'assets/images/banner1.jpg',
        'color': AppColors.primary,
        'type': 'new_course',
      },
      {
        'title': 'Important Announcement',
        'subtitle': '$selectedExam exam dates announced - Check details',
        'image': 'assets/images/banner2.jpg',
        'color': AppColors.info,
        'type': 'announcement',
      },
      {
        'title': 'Special Offer - 50% Off',
        'subtitle': 'Limited time offer on all $selectedExam courses',
        'image': 'assets/images/banner3.jpg',
        'color': AppColors.accent,
        'type': 'special_offer',
      },
      {
        'title': 'Free Mock Test Series',
        'subtitle': 'Test your $selectedExam preparation level',
        'image': 'assets/images/banner4.jpg',
        'color': AppColors.secondary,
        'type': 'free_test',
      },
    ];
  }

  // Handle exam selection change
  void _onExamChanged(String newExam) {
    setState(() {
      selectedExam = newExam;
    });

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Switched to $newExam - Content updated!'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );

    // TODO: Trigger API calls to fetch exam-specific content
    // This would typically refresh courses, tests, and other content
    // based on the selected exam
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DashboardProvider>(
      builder: (context, dashboardProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(AppConstants.appName),
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            actions: [
              IconButton(
                icon: Stack(
                  children: [
                    const Icon(Icons.notifications),
                    if (dashboardProvider.unreadAnnouncementsCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: AppColors.error,
                            shape: BoxShape.circle,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            '${dashboardProvider.unreadAnnouncementsCount}',
                            style: const TextStyle(
                              color: AppColors.white,
                              fontSize: 10,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                onPressed: () => _showNotifications(context, dashboardProvider),
              ),
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () => _navigateToSettings(context),
              ),
            ],
          ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exam Selection Dropdown Section
            _buildExamDropdownSection(context),

            // Banner Slider Section
            _buildBannerSlider(context),

            const SizedBox(height: 24),

            // Quick Access Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildQuickAccess(context),
            ),

            const SizedBox(height: 24),

            // Newly Launched Courses Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildNewlyLaunchedCourses(context),
            ),

            const SizedBox(height: 24),

            // Top Rated Courses Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildTopRatedCourses(context),
            ),

            const SizedBox(height: 24),

            // My Courses Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildMyCoursesSection(context),
            ),

            const SizedBox(height: 24),

            // Recent Activities
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildRecentActivities(context),
            ),

            const SizedBox(height: 100), // Bottom padding for FAB
          ],
        ),
      ),
        );
      },
    );
  }

  // Exam Dropdown Section
  Widget _buildExamDropdownSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.secondary.withOpacity(0.05),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.school,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Select Your Exam Category',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Choose your exam to see personalized courses and content',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primary.withOpacity(0.3)),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: selectedExam,
                isExpanded: true,
                icon: const Icon(Icons.keyboard_arrow_down, color: AppColors.primary),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    _onExamChanged(newValue);
                  }
                },
                items: examCategories.map<DropdownMenuItem<String>>((String exam) {
                  return DropdownMenuItem<String>(
                    value: exam,
                    child: Row(
                      children: [
                        Icon(
                          _getExamIcon(exam),
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Text(exam),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _navigateToExamCourses(context, selectedExam),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('View All Courses'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getExamIcon(String exam) {
    switch (exam) {
      case 'BPSC':
        return Icons.account_balance;
      case 'SSC':
        return Icons.work;
      case 'CTET':
        return Icons.school;
      case 'Railway':
        return Icons.train;
      case 'Banking':
        return Icons.account_balance_wallet;
      case 'Bihar Board 10th':
        return Icons.class_;
      case 'Bihar Board 12th':
        return Icons.school_outlined;
      default:
        return Icons.book;
    }
  }

  // Banner Slider Widget
  Widget _buildBannerSlider(BuildContext context) {
    return SizedBox(
      height: 180,
      child: PageView.builder(
        itemCount: banners.length,
        itemBuilder: (context, index) {
          final banner = banners[index];
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  banner['color'] as Color,
                  (banner['color'] as Color).withOpacity(0.7),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowMedium,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    banner['title'] as String,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    banner['subtitle'] as String,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.white.withOpacity(0.9),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.white,
                      foregroundColor: banner['color'] as Color,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Learn More'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Quick Access Widget
  Widget _buildQuickAccess(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Access',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickAccessCard(
                context,
                'My Courses',
                Icons.school,
                AppColors.primary,
                () => _navigateToMyCourses(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildQuickAccessCard(
                context,
                'Live Class',
                Icons.live_tv,
                AppColors.error,
                () => _navigateToLiveClasses(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildQuickAccessCard(
                context,
                'Daily Quiz',
                Icons.quiz,
                AppColors.secondary,
                () => _navigateToDailyQuiz(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildQuickAccessCard(
                context,
                'Test Series',
                Icons.assignment,
                AppColors.accent,
                () => _navigateToTestSeries(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAccessCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontSize: 11,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Newly Launched Courses Section
  Widget _buildNewlyLaunchedCourses(BuildContext context) {
    final newCourses = _getNewlyLaunchedCourses(selectedExam);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Newly Launched',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToExamCourses(context, selectedExam),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: newCourses.length,
            itemBuilder: (context, index) {
              final course = newCourses[index];
              return _buildCourseCard(context, course, isHorizontal: true);
            },
          ),
        ),
      ],
    );
  }

  // Top Rated Courses Section
  Widget _buildTopRatedCourses(BuildContext context) {
    final topCourses = _getTopRatedCourses(selectedExam);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Top Rated Courses',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToExamCourses(context, selectedExam),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: topCourses.length,
            itemBuilder: (context, index) {
              final course = topCourses[index];
              return _buildCourseCard(context, course, isHorizontal: true);
            },
          ),
        ),
      ],
    );
  }

  // Course Card Widget for horizontal lists
  Widget _buildCourseCard(BuildContext context, Map<String, dynamic> course, {bool isHorizontal = false}) {
    return Container(
      width: isHorizontal ? 280 : double.infinity,
      margin: EdgeInsets.only(right: isHorizontal ? 16 : 0, bottom: isHorizontal ? 0 : 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course Header with gradient
          Container(
            height: 80,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withOpacity(0.8),
                  AppColors.secondary.withOpacity(0.6),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        course['title'],
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const Spacer(),
                      if (course['isNew'] == true)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.warning,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'NEW',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Course Details
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.star, color: AppColors.warning, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${course['rating']}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Icon(Icons.people, color: AppColors.textSecondary, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${course['students']}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (!course['isFree']) ...[
                        Text(
                          '₹${course['price']}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                        if (course['originalPrice'] > course['price']) ...[
                          const SizedBox(width: 8),
                          Text(
                            '₹${course['originalPrice']}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              decoration: TextDecoration.lineThrough,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ] else
                        Text(
                          'FREE',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                    ],
                  ),
                  const Spacer(),
                  SizedBox(
                    width: double.infinity,
                    height: 32,
                    child: ElevatedButton(
                      onPressed: () => _navigateToCourseDetails(context, course),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: course['isFree'] ? AppColors.success : AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        course['isFree'] ? 'Start Free' : 'Enroll Now',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // My Courses Section
  Widget _buildMyCoursesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'My Courses',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () {},
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                width: 200,
                margin: const EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary,
                      AppColors.primaryMedium,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'BPSC 70th Prelims',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Progress: 65%',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.accentLight,
                        ),
                      ),
                      const Spacer(),
                      LinearProgressIndicator(
                        value: 0.65,
                        backgroundColor: AppColors.white.withOpacity(0.3),
                        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.accentLight),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // Recent Activities Section
  Widget _buildRecentActivities(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activities',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        _buildActivityCard(
          context,
          'Indian Polity - Fundamental Rights',
          'Video completed • 2 hours ago',
          Icons.play_circle,
          AppColors.success,
        ),
        _buildActivityCard(
          context,
          'History Mock Test',
          'Score: 85/100 • Yesterday',
          Icons.quiz,
          AppColors.info,
        ),
        _buildActivityCard(
          context,
          'Economy Notes',
          'PDF downloaded • 3 days ago',
          Icons.picture_as_pdf,
          AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildActivityCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          const Icon(Icons.chevron_right, color: AppColors.textHint),
        ],
      ),
    );
  }

  // Get newly launched courses based on selected exam
  List<Map<String, dynamic>> _getNewlyLaunchedCourses(String exam) {
    switch (exam) {
      case 'BPSC':
        return [
          {
            'title': '71st BPSC Prelims 2025',
            'price': 2999,
            'originalPrice': 4999,
            'rating': 4.9,
            'students': 250,
            'isFree': false,
            'isNew': true,
          },
          {
            'title': 'BPSC Interview Preparation',
            'price': 1999,
            'originalPrice': 2999,
            'rating': 4.8,
            'students': 180,
            'isFree': false,
            'isNew': true,
          },
        ];
      case 'SSC':
        return [
          {
            'title': 'SSC CGL 2025 Complete',
            'price': 2499,
            'originalPrice': 3999,
            'rating': 4.7,
            'students': 500,
            'isFree': false,
            'isNew': true,
          },
        ];
      default:
        return [
          {
            'title': '$exam Foundation 2025',
            'price': 1999,
            'originalPrice': 2999,
            'rating': 4.6,
            'students': 300,
            'isFree': false,
            'isNew': true,
          },
        ];
    }
  }

  // Get top rated courses based on selected exam
  List<Map<String, dynamic>> _getTopRatedCourses(String exam) {
    switch (exam) {
      case 'BPSC':
        return [
          {
            'title': '70th BPSC Prelims',
            'price': 2999,
            'originalPrice': 4999,
            'rating': 4.9,
            'students': 1250,
            'isFree': false,
            'isNew': false,
          },
          {
            'title': 'BPSC Free Course',
            'price': 0,
            'originalPrice': 0,
            'rating': 4.5,
            'students': 5000,
            'isFree': true,
            'isNew': false,
          },
        ];
      case 'SSC':
        return [
          {
            'title': 'SSC CGL Tier 1',
            'price': 2499,
            'originalPrice': 3999,
            'rating': 4.8,
            'students': 2100,
            'isFree': false,
            'isNew': false,
          },
        ];
      default:
        return [
          {
            'title': '$exam Complete Course',
            'price': 2999,
            'originalPrice': 4999,
            'rating': 4.7,
            'students': 1500,
            'isFree': false,
            'isNew': false,
          },
        ];
    }
  }

  // Navigation methods
  void _navigateToExamCourses(BuildContext context, String examName) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CourseListScreen(examName: examName),
      ),
    );
  }

  void _navigateToCourseDetails(BuildContext context, Map<String, dynamic> course) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubjectsScreen(
          courseName: course['title'],
          examName: course['exam'] ?? 'BPSC',
        ),
      ),
    );
  }

  void _navigateToQuickAccess(BuildContext context, String type) {
    switch (type) {
      case 'Continue Learning':
        // Navigate to last watched content
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const CourseListScreen(examName: 'All'),
          ),
        );
        break;
      case 'Take Test':
        // Navigate to tests tab - find parent DashboardScreen
        final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
        dashboardState?.setState(() {
          dashboardState._currentIndex = 2; // Tests tab
        });
        break;
      case 'Live Classes':
        // Navigate to classes tab - live classes
        final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
        dashboardState?.setState(() {
          dashboardState._currentIndex = 1; // Classes tab
        });
        break;
      case 'My Progress':
        // Navigate to progress tab
        final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
        dashboardState?.setState(() {
          dashboardState._currentIndex = 3; // Progress tab
        });
        break;
      case 'Downloads':
        _showDownloads(context);
        break;
      case 'Bookmarks':
        // Navigate to classes tab - bookmarks
        final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
        dashboardState?.setState(() {
          dashboardState._currentIndex = 1; // Classes tab
        });
        break;
    }
  }

  void _showDownloads(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DownloadCenterScreen(),
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SettingsScreen(),
      ),
    );
  }

  // Quick Access Navigation Methods
  void _navigateToMyCourses(BuildContext context) {
    // Navigate to Classes tab (enrolled courses)
    final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
    dashboardState?.setState(() {
      dashboardState._currentIndex = 1; // Classes tab
    });
  }

  void _navigateToLiveClasses(BuildContext context) {
    // Navigate to Classes tab and show live classes
    final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
    dashboardState?.setState(() {
      dashboardState._currentIndex = 1; // Classes tab
    });

    // Show info about live classes
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Live Classes - Check schedule in Classes tab'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _navigateToDailyQuiz(BuildContext context) {
    // Navigate to Tests tab for daily quiz
    final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
    dashboardState?.setState(() {
      dashboardState._currentIndex = 2; // Tests tab
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Daily Quiz - Available in Tests section'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _navigateToTestSeries(BuildContext context) {
    // Navigate to Tests tab for test series
    final dashboardState = context.findAncestorStateOfType<_DashboardScreenState>();
    dashboardState?.setState(() {
      dashboardState._currentIndex = 2; // Tests tab
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test Series - Available in Tests section'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showNotifications(BuildContext context, DashboardProvider dashboardProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Notifications',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: dashboardProvider.announcements.isEmpty
                  ? const Center(
                      child: Text('No notifications available'),
                    )
                  : ListView.builder(
                      itemCount: dashboardProvider.announcements.length,
                      itemBuilder: (context, index) {
                        final announcement = dashboardProvider.announcements[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: announcement.isRead
                                    ? AppColors.grey.withOpacity(0.3)
                                    : AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                _getAnnouncementIcon(announcement.type),
                                color: announcement.isRead
                                    ? AppColors.grey
                                    : AppColors.primary,
                              ),
                            ),
                            title: Text(
                              announcement.title,
                              style: TextStyle(
                                fontWeight: announcement.isRead
                                    ? FontWeight.normal
                                    : FontWeight.bold,
                              ),
                            ),
                            subtitle: Text(
                              announcement.content,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: Text(
                              _formatDate(announcement.createdAt),
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            onTap: () {
                              if (!announcement.isRead) {
                                dashboardProvider.markAnnouncementAsRead(announcement.id);
                              }
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getAnnouncementIcon(AnnouncementType type) {
    switch (type) {
      case AnnouncementType.courseUpdate:
        return Icons.school;
      case AnnouncementType.examNotification:
        return Icons.quiz;
      case AnnouncementType.maintenance:
        return Icons.build;
      case AnnouncementType.newFeature:
        return Icons.new_releases;
      default:
        return Icons.notifications;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }
}

// Classes Tab - My Courses & Live Classes
class ClassesTab extends StatefulWidget {
  const ClassesTab({super.key});

  @override
  State<ClassesTab> createState() => _ClassesTabState();
}

class _ClassesTabState extends State<ClassesTab> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Sample enrolled courses data
  List<Map<String, dynamic>> get enrolledCourses => [
    {
      'title': 'BPSC Prelims Complete Course',
      'instructor': 'Dr. Rajesh Kumar',
      'progress': 0.65,
      'totalVideos': 120,
      'completedVideos': 78,
      'totalTests': 15,
      'completedTests': 9,
      'lastWatched': 'Indian Polity - Fundamental Rights',
      'nextClass': 'Geography - Physical Features',
      'thumbnail': 'assets/images/course1.jpg',
      'subjects': ['Indian Polity', 'History', 'Geography', 'Economy'],
      'enrolledDate': '2024-01-15',
      'validTill': '2024-12-31',
      'isPaid': true,
    },
    {
      'title': 'SSC CGL Foundation Course',
      'instructor': 'Prof. Anita Sharma',
      'progress': 0.35,
      'totalVideos': 95,
      'completedVideos': 33,
      'totalTests': 12,
      'completedTests': 4,
      'lastWatched': 'Quantitative Aptitude - Algebra',
      'nextClass': 'English - Grammar Basics',
      'thumbnail': 'assets/images/course2.jpg',
      'subjects': ['Quantitative Aptitude', 'English', 'Reasoning', 'GK'],
      'enrolledDate': '2024-02-01',
      'validTill': '2024-12-31',
      'isPaid': true,
    },
    {
      'title': 'CTET Paper 1 Free Course',
      'instructor': 'Ms. Priya Singh',
      'progress': 0.80,
      'totalVideos': 45,
      'completedVideos': 36,
      'totalTests': 8,
      'completedTests': 6,
      'lastWatched': 'Child Development - Learning Theories',
      'nextClass': 'Mathematics - Number System',
      'thumbnail': 'assets/images/course3.jpg',
      'subjects': ['Child Development', 'Mathematics', 'EVS', 'Hindi'],
      'enrolledDate': '2024-01-20',
      'validTill': '2024-12-31',
      'isPaid': false,
    },
  ];

  // Sample live classes data
  List<Map<String, dynamic>> get liveClasses => [
    {
      'title': 'Indian Polity - Constitutional Framework',
      'instructor': 'Dr. Rajesh Kumar',
      'course': 'BPSC Prelims Complete Course',
      'scheduledTime': DateTime.now().add(const Duration(hours: 2)),
      'duration': 90,
      'isLive': false,
      'isUpcoming': true,
      'meetingLink': 'https://zoom.us/j/123456789',
      'description': 'Detailed discussion on constitutional framework and its key features',
      'subject': 'Indian Polity',
    },
    {
      'title': 'Mathematics - Algebra Fundamentals',
      'instructor': 'Prof. Anita Sharma',
      'course': 'SSC CGL Foundation Course',
      'scheduledTime': DateTime.now().add(const Duration(minutes: 30)),
      'duration': 60,
      'isLive': true,
      'isUpcoming': false,
      'meetingLink': 'https://zoom.us/j/987654321',
      'description': 'Basic concepts of algebra with problem-solving techniques',
      'subject': 'Quantitative Aptitude',
    },
    {
      'title': 'Child Development - Learning Theories',
      'instructor': 'Ms. Priya Singh',
      'course': 'CTET Paper 1 Free Course',
      'scheduledTime': DateTime.now().subtract(const Duration(hours: 1)),
      'duration': 75,
      'isLive': false,
      'isUpcoming': false,
      'meetingLink': 'https://zoom.us/j/456789123',
      'description': 'Understanding different learning theories and their applications',
      'subject': 'Child Development',
    },
  ];

  // Sample bookmarked content
  List<Map<String, dynamic>> get bookmarkedContent => [
    {
      'type': 'video',
      'title': 'Fundamental Rights - Article 14-18',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Indian Polity',
      'duration': '45:30',
      'bookmarkedAt': '12:45',
      'addedDate': DateTime.now().subtract(const Duration(days: 2)),
    },
    {
      'type': 'pdf',
      'title': 'Indian History Complete Notes',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Indian History',
      'pages': 120,
      'bookmarkedPage': 45,
      'addedDate': DateTime.now().subtract(const Duration(days: 5)),
    },
    {
      'type': 'test',
      'title': 'Geography Mock Test 1',
      'course': 'BPSC Prelims Complete Course',
      'subject': 'Geography',
      'questions': 25,
      'bestScore': '18/25',
      'addedDate': DateTime.now().subtract(const Duration(days: 1)),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Classes'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.school), text: 'My Courses'),
            Tab(icon: Icon(Icons.live_tv), text: 'Live Classes'),
            Tab(icon: Icon(Icons.bookmark), text: 'Bookmarks'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMyCoursesTab(),
          _buildLiveClassesTab(),
          _buildBookmarksTab(),
        ],
      ),
    );
  }

  Widget _buildMyCoursesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course Stats
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.primaryLight],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'My Learning Progress',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${enrolledCourses.length} courses enrolled',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.white.withOpacity(0.9),
                        ),
                      ),
                      const SizedBox(height: 12),
                      LinearProgressIndicator(
                        value: _getOverallCourseProgress(),
                        backgroundColor: AppColors.white.withOpacity(0.3),
                        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.white),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Icon(Icons.school, color: AppColors.white, size: 32),
                      const SizedBox(height: 4),
                      Text(
                        '${(_getOverallCourseProgress() * 100).round()}%',
                        style: const TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Enrolled Courses
          Text(
            'Enrolled Courses',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: enrolledCourses.length,
            itemBuilder: (context, index) {
              final course = enrolledCourses[index];
              return _buildCourseCard(course);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCourseCard(Map<String, dynamic> course) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: course['isPaid'] ? AppColors.primary : AppColors.success,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        course['title'],
                        style: const TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'by ${course['instructor']}',
                        style: TextStyle(
                          color: AppColors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    course['isPaid'] ? 'PAID' : 'FREE',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Course Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Progress Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${(course['progress'] * 100).round()}%',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: course['progress'],
                  backgroundColor: AppColors.greyLight,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),

                const SizedBox(height: 16),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _continueLearning(course),
                        icon: const Icon(Icons.play_arrow, size: 18),
                        label: const Text('Continue'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _viewCourseDetails(course),
                        icon: const Icon(Icons.info_outline, size: 18),
                        label: const Text('Details'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: const BorderSide(color: AppColors.primary),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  double _getOverallCourseProgress() {
    if (enrolledCourses.isEmpty) return 0.0;
    double totalProgress = enrolledCourses.fold(0.0, (sum, course) => sum + course['progress']);
    return totalProgress / enrolledCourses.length;
  }

  void _continueLearning(Map<String, dynamic> course) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Continuing ${course['title']}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _viewCourseDetails(Map<String, dynamic> course) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for ${course['title']}'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  // Placeholder methods for other tabs
  Widget _buildLiveClassesTab() {
    return const Center(child: Text('Live Classes - Coming Soon!'));
  }

  Widget _buildBookmarksTab() {
    return const Center(child: Text('Bookmarks - Coming Soon!'));
  }
}

class TestsTab extends StatefulWidget {
  const TestsTab({super.key});

  @override
  State<TestsTab> createState() => _TestsTabState();
}

class _TestsTabState extends State<TestsTab> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Sample test data
  List<Map<String, dynamic>> get availableTests => [
    {
      'title': 'BPSC Prelims Mock Test 1',
      'subject': 'General Studies',
      'course': 'BPSC Prelims Complete Course',
      'questions': 100,
      'duration': 120,
      'maxMarks': 200,
      'difficulty': 'Medium',
      'attempts': 0,
      'bestScore': 0,
      'isAttempted': false,
      'type': 'Mock Test',
      'description': 'Comprehensive mock test covering all subjects',
      'topics': ['Indian Polity', 'History', 'Geography', 'Economy'],
    },
    {
      'title': 'Indian Polity - Fundamental Rights',
      'subject': 'Indian Polity',
      'course': 'BPSC Prelims Complete Course',
      'questions': 25,
      'duration': 30,
      'maxMarks': 50,
      'difficulty': 'Easy',
      'attempts': 2,
      'bestScore': 42,
      'isAttempted': true,
      'type': 'Topic Test',
      'description': 'Test on Fundamental Rights (Article 12-35)',
      'topics': ['Fundamental Rights'],
    },
    {
      'title': 'SSC CGL Quantitative Aptitude',
      'subject': 'Mathematics',
      'course': 'SSC CGL Foundation Course',
      'questions': 50,
      'duration': 60,
      'maxMarks': 100,
      'difficulty': 'Hard',
      'attempts': 1,
      'bestScore': 65,
      'isAttempted': true,
      'type': 'Subject Test',
      'description': 'Advanced mathematics problems for SSC CGL',
      'topics': ['Algebra', 'Geometry', 'Trigonometry'],
    },
  ];

  List<Map<String, dynamic>> get attemptedTests => availableTests.where((test) => test['isAttempted']).toList();

  List<Map<String, dynamic>> get testHistory => [
    {
      'testTitle': 'Indian Polity - Fundamental Rights',
      'attemptDate': DateTime.now().subtract(const Duration(days: 2)),
      'score': 42,
      'maxScore': 50,
      'percentage': 84,
      'timeTaken': 25,
      'totalTime': 30,
      'rank': 15,
      'totalParticipants': 1250,
      'grade': 'A',
    },
    {
      'testTitle': 'SSC CGL Quantitative Aptitude',
      'attemptDate': DateTime.now().subtract(const Duration(days: 5)),
      'score': 65,
      'maxScore': 100,
      'percentage': 65,
      'timeTaken': 55,
      'totalTime': 60,
      'rank': 45,
      'totalParticipants': 890,
      'grade': 'B+',
    },
    {
      'testTitle': 'Geography Mock Test',
      'attemptDate': DateTime.now().subtract(const Duration(days: 7)),
      'score': 38,
      'maxScore': 50,
      'percentage': 76,
      'timeTaken': 28,
      'totalTime': 30,
      'rank': 25,
      'totalParticipants': 750,
      'grade': 'B+',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tests'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.quiz), text: 'All Tests'),
            Tab(icon: Icon(Icons.assignment_turned_in), text: 'Attempted'),
            Tab(icon: Icon(Icons.history), text: 'History'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllTestsTab(),
          _buildAttemptedTestsTab(),
          _buildTestHistoryTab(),
        ],
      ),
    );
  }

  Widget _buildAllTestsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Stats
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.secondary, AppColors.secondaryLight],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Performance',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${attemptedTests.length}/${availableTests.length} tests attempted',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.white.withOpacity(0.9),
                        ),
                      ),
                      const SizedBox(height: 12),
                      LinearProgressIndicator(
                        value: attemptedTests.length / availableTests.length,
                        backgroundColor: AppColors.white.withOpacity(0.3),
                        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.white),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Icon(Icons.quiz, color: AppColors.white, size: 32),
                      const SizedBox(height: 4),
                      Text(
                        '${_getAverageScore().round()}%',
                        style: const TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'Avg Score',
                        style: TextStyle(
                          color: AppColors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Filter Chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('All Tests', true),
                const SizedBox(width: 8),
                _buildFilterChip('Mock Tests', false),
                const SizedBox(width: 8),
                _buildFilterChip('Subject Tests', false),
                const SizedBox(width: 8),
                _buildFilterChip('Topic Tests', false),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Available Tests
          Text(
            'Available Tests',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: availableTests.length,
            itemBuilder: (context, index) {
              final test = availableTests[index];
              return _buildTestCard(test);
            },
          ),
        ],
      ),
    );
  }

  // Helper methods for Tests tab
  double _getAverageScore() {
    if (attemptedTests.isEmpty) return 0.0;
    double totalPercentage = 0.0;
    for (var test in attemptedTests) {
      totalPercentage += (test['bestScore'] / test['maxMarks']) * 100;
    }
    return totalPercentage / attemptedTests.length;
  }

  Color _getTestTypeColor(String type) {
    switch (type) {
      case 'Mock Test':
        return AppColors.primary;
      case 'Subject Test':
        return AppColors.secondary;
      case 'Topic Test':
        return AppColors.accent;
      default:
        return AppColors.grey;
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return AppColors.success;
      case 'medium':
        return AppColors.warning;
      case 'hard':
        return AppColors.error;
      default:
        return AppColors.primary;
    }
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // Handle filter selection
      },
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  Widget _buildTestStatChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.greyLight,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppColors.textSecondary),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestCard(Map<String, dynamic> test) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getTestTypeColor(test['type']),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        test['title'],
                        style: const TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        test['subject'],
                        style: TextStyle(
                          color: AppColors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    test['type'],
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Test Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Test Stats
                Row(
                  children: [
                    _buildTestStatChip(Icons.quiz, '${test['questions']} Questions'),
                    const SizedBox(width: 12),
                    _buildTestStatChip(Icons.timer, '${test['duration']} min'),
                    const SizedBox(width: 12),
                    _buildTestStatChip(Icons.star, '${test['maxMarks']} marks'),
                  ],
                ),

                const SizedBox(height: 12),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _startTest(test),
                        icon: Icon(test['isAttempted'] ? Icons.refresh : Icons.play_arrow, size: 18),
                        label: Text(test['isAttempted'] ? 'Retake' : 'Start Test'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    if (test['isAttempted']) ...[
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _viewTestResults(test),
                          icon: const Icon(Icons.analytics, size: 18),
                          label: const Text('Results'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.secondary,
                            side: const BorderSide(color: AppColors.secondary),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttemptedTestsTab() {
    return const Center(child: Text('Attempted Tests - Coming Soon!'));
  }

  Widget _buildTestHistoryTab() {
    return const Center(child: Text('Test History - Coming Soon!'));
  }

  void _startTest(Map<String, dynamic> test) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting ${test['title']}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _viewTestResults(Map<String, dynamic> test) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing results for ${test['title']}'),
        backgroundColor: AppColors.secondary,
      ),
    );
  }
}

class ProgressTab extends StatefulWidget {
  const ProgressTab({super.key});

  @override
  State<ProgressTab> createState() => _ProgressTabState();
}

class _ProgressTabState extends State<ProgressTab> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Sample progress data
  Map<String, dynamic> get overallProgress => {
    'totalCourses': 3,
    'completedCourses': 1,
    'totalVideos': 260,
    'watchedVideos': 147,
    'totalTests': 35,
    'attemptedTests': 19,
    'totalStudyHours': 156.5,
    'currentStreak': 7,
    'longestStreak': 15,
    'averageScore': 78.5,
  };

  List<Map<String, dynamic>> get courseProgress => [
    {
      'courseName': 'BPSC Prelims Complete Course',
      'totalSubjects': 6,
      'completedSubjects': 4,
      'totalVideos': 120,
      'watchedVideos': 78,
      'totalTests': 15,
      'attemptedTests': 9,
      'averageScore': 82.3,
      'timeSpent': 89.5,
      'progress': 0.65,
      'subjects': [
        {'name': 'Indian Polity', 'progress': 0.85, 'score': 88},
        {'name': 'History', 'progress': 0.70, 'score': 76},
        {'name': 'Geography', 'progress': 0.60, 'score': 82},
        {'name': 'Economy', 'progress': 0.45, 'score': 74},
        {'name': 'General Science', 'progress': 0.30, 'score': 0},
        {'name': 'Current Affairs', 'progress': 0.80, 'score': 85},
      ],
    },
    {
      'courseName': 'SSC CGL Foundation Course',
      'totalSubjects': 4,
      'completedSubjects': 1,
      'totalVideos': 95,
      'watchedVideos': 33,
      'totalTests': 12,
      'attemptedTests': 4,
      'averageScore': 68.5,
      'timeSpent': 45.2,
      'progress': 0.35,
      'subjects': [
        {'name': 'Quantitative Aptitude', 'progress': 0.50, 'score': 72},
        {'name': 'English', 'progress': 0.40, 'score': 65},
        {'name': 'Reasoning', 'progress': 0.25, 'score': 0},
        {'name': 'General Knowledge', 'progress': 0.25, 'score': 0},
      ],
    },
  ];

  List<Map<String, dynamic>> get weeklyActivity => [
    {'day': 'Mon', 'hours': 2.5, 'videos': 3, 'tests': 1},
    {'day': 'Tue', 'hours': 3.0, 'videos': 4, 'tests': 0},
    {'day': 'Wed', 'hours': 1.5, 'videos': 2, 'tests': 2},
    {'day': 'Thu', 'hours': 2.8, 'videos': 3, 'tests': 1},
    {'day': 'Fri', 'hours': 4.0, 'videos': 5, 'tests': 1},
    {'day': 'Sat', 'hours': 3.5, 'videos': 4, 'tests': 2},
    {'day': 'Sun', 'hours': 2.0, 'videos': 2, 'tests': 1},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Progress'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.analytics), text: 'Overview'),
            Tab(icon: Icon(Icons.school), text: 'Courses'),
            Tab(icon: Icon(Icons.calendar_today), text: 'Activity'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildCoursesTab(),
          _buildActivityTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Stats Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Study Hours',
                  '${overallProgress['totalStudyHours']}h',
                  Icons.access_time,
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Current Streak',
                  '${overallProgress['currentStreak']} days',
                  Icons.local_fire_department,
                  AppColors.warning,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Avg Score',
                  '${overallProgress['averageScore']}%',
                  Icons.star,
                  AppColors.success,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Tests Taken',
                  '${overallProgress['attemptedTests']}',
                  Icons.quiz,
                  AppColors.secondary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Progress Overview
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Learning Progress',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // Videos Progress
                _buildProgressItem(
                  'Videos Watched',
                  overallProgress['watchedVideos'],
                  overallProgress['totalVideos'],
                  AppColors.primary,
                  Icons.play_circle,
                ),

                const SizedBox(height: 16),

                // Tests Progress
                _buildProgressItem(
                  'Tests Attempted',
                  overallProgress['attemptedTests'],
                  overallProgress['totalTests'],
                  AppColors.secondary,
                  Icons.quiz,
                ),

                const SizedBox(height: 16),

                // Courses Progress
                _buildProgressItem(
                  'Courses Completed',
                  overallProgress['completedCourses'],
                  overallProgress['totalCourses'],
                  AppColors.success,
                  Icons.school,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Achievement Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.accent, AppColors.accentLight],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.emoji_events, color: AppColors.white, size: 32),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Achievements',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Keep up the great work!',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.white.withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    _buildAchievementBadge('7 Day Streak', Icons.local_fire_department),
                    const SizedBox(width: 12),
                    _buildAchievementBadge('50+ Videos', Icons.play_circle),
                    const SizedBox(width: 12),
                    _buildAchievementBadge('High Scorer', Icons.star),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(String title, int current, int total, Color color, IconData icon) {
    double progress = total > 0 ? current / total : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Text(
              '$current/$total',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: AppColors.greyLight,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
        const SizedBox(height: 4),
        Text(
          '${(progress * 100).round()}% Complete',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementBadge(String title, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, color: AppColors.white, size: 20),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                color: AppColors.white,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoursesTab() {
    return const Center(child: Text('Course Progress - Coming Soon!'));
  }

  Widget _buildActivityTab() {
    return const Center(child: Text('Activity Tracking - Coming Soon!'));
  }
}

class ProfileTab extends StatefulWidget {
  const ProfileTab({super.key});

  @override
  State<ProfileTab> createState() => _ProfileTabState();
}

class _ProfileTabState extends State<ProfileTab> {
  // Sample user data
  Map<String, dynamic> get userProfile => {
    'name': 'Rajesh Kumar',
    'email': '<EMAIL>',
    'phone': '+91 9876543210',
    'joinDate': DateTime(2024, 1, 15),
    'location': 'Patna, Bihar',
    'targetExam': 'BPSC Prelims 2024',
    'profileImage': null,
    'isPremium': true,
    'premiumExpiry': DateTime(2024, 12, 31),
  };

  Map<String, dynamic> get userStats => {
    'totalCourses': 3,
    'completedCourses': 1,
    'totalHours': 156.5,
    'testsAttempted': 19,
    'averageScore': 78.5,
    'currentStreak': 7,
    'certificates': 2,
    'rank': 45,
  };

  List<Map<String, dynamic>> get recentActivity => [
    {
      'type': 'test',
      'title': 'Completed Indian Polity Test',
      'score': '42/50',
      'date': DateTime.now().subtract(const Duration(hours: 2)),
    },
    {
      'type': 'video',
      'title': 'Watched Geography - Physical Features',
      'duration': '45 min',
      'date': DateTime.now().subtract(const Duration(hours: 5)),
    },
    {
      'type': 'achievement',
      'title': 'Earned 7-Day Streak Badge',
      'description': 'Consistent learning',
      'date': DateTime.now().subtract(const Duration(days: 1)),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettings(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primaryLight],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // Profile Picture & Basic Info
                  Row(
                    children: [
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.white.withOpacity(0.2),
                          border: Border.all(color: AppColors.white, width: 3),
                        ),
                        child: userProfile['profileImage'] != null
                            ? ClipOval(
                                child: Image.network(
                                  userProfile['profileImage'],
                                  fit: BoxFit.cover,
                                ),
                              )
                            : Icon(
                                Icons.person,
                                size: 40,
                                color: AppColors.white,
                              ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              userProfile['name'],
                              style: const TextStyle(
                                color: AppColors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              userProfile['email'],
                              style: TextStyle(
                                color: AppColors.white.withOpacity(0.9),
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: AppColors.white.withOpacity(0.9),
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  userProfile['location'],
                                  style: TextStyle(
                                    color: AppColors.white.withOpacity(0.9),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      if (userProfile['isPremium'])
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColors.warning,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.star, color: AppColors.white, size: 16),
                              const SizedBox(width: 4),
                              const Text(
                                'PREMIUM',
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Target Exam
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.flag, color: AppColors.white, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Target: ${userProfile['targetExam']}',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Quick Stats
            Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Study Hours',
                    '${userStats['totalHours']}h',
                    Icons.access_time,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickStat(
                    'Avg Score',
                    '${userStats['averageScore']}%',
                    Icons.star,
                    AppColors.success,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickStat(
                    'Rank',
                    '#${userStats['rank']}',
                    Icons.emoji_events,
                    AppColors.warning,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Menu Options
            Text(
              'Account',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            _buildMenuSection([
              _buildMenuItem(
                'Edit Profile',
                'Update your personal information',
                Icons.edit,
                () => _editProfile(context),
              ),
              _buildMenuItem(
                'My Certificates',
                '${userStats['certificates']} certificates earned',
                Icons.card_membership,
                () => _viewCertificates(context),
              ),
              _buildMenuItem(
                'Download Center',
                'Manage your downloaded content',
                Icons.download,
                () => _openDownloadCenter(context),
              ),
              _buildMenuItem(
                'Notifications',
                'Manage notification preferences',
                Icons.notifications,
                () => _manageNotifications(context),
              ),
            ]),

            const SizedBox(height: 24),

            Text(
              'Support',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            _buildMenuSection([
              _buildMenuItem(
                'Help & FAQ',
                'Get answers to common questions',
                Icons.help,
                () => _openHelp(context),
              ),
              _buildMenuItem(
                'Contact Support',
                'Get help from our support team',
                Icons.support_agent,
                () => _contactSupport(context),
              ),
              _buildMenuItem(
                'Rate App',
                'Share your feedback',
                Icons.star_rate,
                () => _rateApp(context),
              ),
            ]),

            const SizedBox(height: 24),

            Text(
              'App',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            _buildMenuSection([
              _buildMenuItem(
                'Privacy Policy',
                'Read our privacy policy',
                Icons.privacy_tip,
                () => _openPrivacyPolicy(context),
              ),
              _buildMenuItem(
                'Terms of Service',
                'Read our terms of service',
                Icons.description,
                () => _openTerms(context),
              ),
              _buildMenuItem(
                'About',
                'App version and information',
                Icons.info,
                () => _showAbout(context),
              ),
            ]),

            const SizedBox(height: 24),

            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _logout(context),
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStat(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: items,
      ),
    );
  }

  Widget _buildMenuItem(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: AppColors.primary, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
        ),
      ),
      trailing: const Icon(Icons.chevron_right, color: AppColors.textSecondary),
      onTap: onTap,
    );
  }

  // Action methods
  void _showSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _editProfile(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit Profile - Coming Soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _viewCertificates(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Certificates - Coming Soon!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _openDownloadCenter(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Download Center - Coming Soon!'),
        backgroundColor: AppColors.secondary,
      ),
    );
  }

  void _manageNotifications(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notifications - Coming Soon!'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _openHelp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Help & FAQ - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _contactSupport(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Contact Support - Coming Soon!'),
        backgroundColor: AppColors.accent,
      ),
    );
  }

  void _rateApp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rate App - Coming Soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _openPrivacyPolicy(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Privacy Policy - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _openTerms(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Terms of Service - Coming Soon!'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showAbout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Student Learning App'),
            SizedBox(height: 8),
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text('Developed by: Augment Code'),
            SizedBox(height: 8),
            Text('© 2024 All rights reserved'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              // Show loading
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Logging out...'),
                  backgroundColor: AppColors.info,
                ),
              );

              // Logout using AuthProvider
              final authProvider = Provider.of<AuthProvider>(context, listen: false);
              await authProvider.logout();

              // Navigate to login screen
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const LoginScreen(),
                ),
                (route) => false,
              );

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Logged out successfully'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
