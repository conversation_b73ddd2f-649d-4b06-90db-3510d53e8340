import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';
import '../utils/storage_helper.dart';

class ApiService {
  static const String _baseUrl = AppConstants.baseUrl;
  static const Duration _timeout = Duration(seconds: 30);

  // HTTP Client with timeout
  static final http.Client _client = http.Client();

  // Common headers
  static Map<String, String> get _baseHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers with authentication
  static Future<Map<String, String>> get _authHeaders async {
    final token = await StorageHelper.getToken();
    final headers = Map<String, String>.from(_baseHeaders);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  // GET Request
  static Future<ApiResponse> get(String endpoint, {bool requiresAuth = true}) async {
    try {
      final headers = requiresAuth ? await _authHeaders : _baseHeaders;
      final uri = Uri.parse('$_baseUrl$endpoint');
      
      final response = await _client.get(uri, headers: headers).timeout(_timeout);
      
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  // POST Request
  static Future<ApiResponse> post(
    String endpoint, {
    Map<String, dynamic>? body,
    bool requiresAuth = true,
  }) async {
    try {
      final headers = requiresAuth ? await _authHeaders : _baseHeaders;
      final uri = Uri.parse('$_baseUrl$endpoint');
      
      final response = await _client.post(
        uri,
        headers: headers,
        body: body != null ? json.encode(body) : null,
      ).timeout(_timeout);
      
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  // PUT Request
  static Future<ApiResponse> put(
    String endpoint, {
    Map<String, dynamic>? body,
    bool requiresAuth = true,
  }) async {
    try {
      final headers = requiresAuth ? await _authHeaders : _baseHeaders;
      final uri = Uri.parse('$_baseUrl$endpoint');
      
      final response = await _client.put(
        uri,
        headers: headers,
        body: body != null ? json.encode(body) : null,
      ).timeout(_timeout);
      
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  // DELETE Request
  static Future<ApiResponse> delete(String endpoint, {bool requiresAuth = true}) async {
    try {
      final headers = requiresAuth ? await _authHeaders : _baseHeaders;
      final uri = Uri.parse('$_baseUrl$endpoint');
      
      final response = await _client.delete(uri, headers: headers).timeout(_timeout);
      
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  // Handle HTTP Response
  static ApiResponse _handleResponse(http.Response response) {
    try {
      final Map<String, dynamic> data = json.decode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse.error(
          message: data['message'] ?? 'An error occurred',
          statusCode: response.statusCode,
          errorCode: data['error'],
        );
      }
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to parse response',
        statusCode: response.statusCode,
      );
    }
  }

  // Handle Errors
  static ApiResponse _handleError(dynamic error) {
    if (error is SocketException) {
      return ApiResponse.error(
        message: 'No internet connection',
        statusCode: 0,
      );
    } else if (error is HttpException) {
      return ApiResponse.error(
        message: 'Network error occurred',
        statusCode: 0,
      );
    } else {
      return ApiResponse.error(
        message: 'An unexpected error occurred',
        statusCode: 0,
      );
    }
  }

  // Multipart Request for file uploads
  static Future<ApiResponse> multipart(
    String endpoint,
    Map<String, String> fields, {
    Map<String, String>? files,
    bool requiresAuth = true,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final request = http.MultipartRequest('POST', uri);
      
      // Add headers
      if (requiresAuth) {
        final token = await StorageHelper.getToken();
        if (token != null) {
          request.headers['Authorization'] = 'Bearer $token';
        }
      }
      
      // Add fields
      request.fields.addAll(fields);
      
      // Add files
      if (files != null) {
        for (final entry in files.entries) {
          final file = await http.MultipartFile.fromPath(entry.key, entry.value);
          request.files.add(file);
        }
      }
      
      final streamedResponse = await request.send().timeout(_timeout);
      final response = await http.Response.fromStream(streamedResponse);
      
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  // Dispose client
  static void dispose() {
    _client.close();
  }
}

// API Response Model
class ApiResponse {
  final bool success;
  final String message;
  final dynamic data;
  final int? statusCode;
  final String? errorCode;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.statusCode,
    this.errorCode,
  });

  factory ApiResponse.success({
    required dynamic data,
    required String message,
  }) {
    return ApiResponse(
      success: true,
      message: message,
      data: data,
    );
  }

  factory ApiResponse.error({
    required String message,
    int? statusCode,
    String? errorCode,
  }) {
    return ApiResponse(
      success: false,
      message: message,
      statusCode: statusCode,
      errorCode: errorCode,
    );
  }

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data)';
  }
}
