import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/app_utils.dart';
import '../../../shared/widgets/custom_button.dart';
import '../providers/auth_provider.dart';
import '../../dashboard/presentation/dashboard_screen.dart';
import '../../exams/services/exam_service.dart';

class ExamPreferenceScreen extends StatefulWidget {
  final bool isOnboarding;

  const ExamPreferenceScreen({
    super.key,
    this.isOnboarding = true,
  });

  @override
  State<ExamPreferenceScreen> createState() => _ExamPreferenceScreenState();
}

class _ExamPreferenceScreenState extends State<ExamPreferenceScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<ExamCategory> _examCategories = [];
  List<String> _selectedExams = [];
  bool _isLoading = false;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadExamCategories();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  Future<void> _loadExamCategories() async {
    setState(() => _isLoading = true);
    
    try {
      final response = await ExamService.getExamCategories();
      
      if (response.success) {
        setState(() {
          _examCategories = response.data as List<ExamCategory>;
        });
      } else {
        AppUtils.showSnackBar(
          context,
          'Failed to load exam categories',
          backgroundColor: AppColors.error,
          textColor: AppColors.white,
        );
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Error loading exam categories',
        backgroundColor: AppColors.error,
        textColor: AppColors.white,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _toggleExamSelection(String examId) {
    setState(() {
      if (_selectedExams.contains(examId)) {
        _selectedExams.remove(examId);
      } else {
        _selectedExams.add(examId);
      }
    });
  }

  Future<void> _savePreferences() async {
    if (_selectedExams.isEmpty) {
      AppUtils.showSnackBar(
        context,
        'Please select at least one exam',
        backgroundColor: AppColors.warning,
        textColor: AppColors.white,
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.updateExamPreferences(_selectedExams);

      if (success) {
        if (widget.isOnboarding) {
          // Navigate to dashboard for new users
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const DashboardScreen(),
            ),
            (route) => false,
          );
        } else {
          // Go back for existing users updating preferences
          Navigator.of(context).pop();
        }

        AppUtils.showSnackBar(
          context,
          'Exam preferences saved successfully',
          backgroundColor: AppColors.success,
          textColor: AppColors.white,
        );
      } else {
        AppUtils.showSnackBar(
          context,
          authProvider.error ?? 'Failed to save preferences',
          backgroundColor: AppColors.error,
          textColor: AppColors.white,
        );
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Error saving preferences',
        backgroundColor: AppColors.error,
        textColor: AppColors.white,
      );
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  children: [
                    // Header
                    _buildHeader(),
                    
                    // Content
                    Expanded(
                      child: _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : _buildExamSelection(),
                    ),
                    
                    // Continue Button
                    _buildContinueButton(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          if (!widget.isOnboarding)
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Navigator.pop(context),
                ),
                const Spacer(),
              ],
            ),
          
          Icon(
            Icons.school,
            size: 64,
            color: AppColors.primary,
          ),
          
          const SizedBox(height: 16),
          
          Text(
            widget.isOnboarding 
                ? 'Choose Your Exams'
                : 'Update Exam Preferences',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Select the exams you\'re preparing for to get personalized content',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExamSelection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Exams',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
            ),
            itemCount: _examCategories.length,
            itemBuilder: (context, index) {
              final exam = _examCategories[index];
              final isSelected = _selectedExams.contains(exam.id);
              
              return GestureDetector(
                onTap: () => _toggleExamSelection(exam.id),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primary.withOpacity(0.1)
                        : AppColors.white,
                    border: Border.all(
                      color: isSelected 
                          ? AppColors.primary
                          : AppColors.grey.withOpacity(0.3),
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (exam.iconUrl != null)
                        Image.network(
                          exam.iconUrl!,
                          height: 32,
                          width: 32,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.school,
                              size: 32,
                              color: isSelected 
                                  ? AppColors.primary
                                  : AppColors.grey,
                            );
                          },
                        )
                      else
                        Icon(
                          Icons.school,
                          size: 32,
                          color: isSelected 
                              ? AppColors.primary
                              : AppColors.grey,
                        ),
                      
                      const SizedBox(height: 8),
                      
                      Text(
                        exam.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? AppColors.primary
                              : AppColors.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      Text(
                        '${exam.examCount} courses',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      
                      if (isSelected)
                        Container(
                          margin: const EdgeInsets.only(top: 8),
                          child: Icon(
                            Icons.check_circle,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildContinueButton() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: CustomButton(
        text: widget.isOnboarding ? 'Continue' : 'Save Preferences',
        onPressed: _isSubmitting ? null : _savePreferences,
        isLoading: _isSubmitting,
        gradient: AppColors.primaryGradient,
      ),
    );
  }
}
