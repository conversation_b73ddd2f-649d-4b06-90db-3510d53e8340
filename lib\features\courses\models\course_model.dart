class CourseModel {
  final String id;
  final String title;
  final String description;
  final String examId;
  final String examName;
  final String instructorId;
  final String instructorName;
  final String? instructorImage;
  final String thumbnailUrl;
  final CourseType type;
  final CoursePricing pricing;
  final CourseContent content;
  final CourseStats stats;
  final List<String> tags;
  final CourseStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isEnrolled;
  final UserProgress? userProgress;

  CourseModel({
    required this.id,
    required this.title,
    required this.description,
    required this.examId,
    required this.examName,
    required this.instructorId,
    required this.instructorName,
    this.instructorImage,
    required this.thumbnailUrl,
    required this.type,
    required this.pricing,
    required this.content,
    required this.stats,
    required this.tags,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    required this.isEnrolled,
    this.userProgress,
  });

  factory CourseModel.fromJson(Map<String, dynamic> json) {
    return CourseModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      examId: json['examId'] ?? '',
      examName: json['examName'] ?? '',
      instructorId: json['instructorId'] ?? '',
      instructorName: json['instructorName'] ?? '',
      instructorImage: json['instructorImage'],
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      type: CourseType.fromString(json['type'] ?? 'recorded'),
      pricing: CoursePricing.fromJson(json['pricing'] ?? {}),
      content: CourseContent.fromJson(json['content'] ?? {}),
      stats: CourseStats.fromJson(json['stats'] ?? {}),
      tags: List<String>.from(json['tags'] ?? []),
      status: CourseStatus.fromString(json['status'] ?? 'active'),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      isEnrolled: json['isEnrolled'] ?? false,
      userProgress: json['userProgress'] != null 
          ? UserProgress.fromJson(json['userProgress']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'examId': examId,
      'examName': examName,
      'instructorId': instructorId,
      'instructorName': instructorName,
      'instructorImage': instructorImage,
      'thumbnailUrl': thumbnailUrl,
      'type': type.value,
      'pricing': pricing.toJson(),
      'content': content.toJson(),
      'stats': stats.toJson(),
      'tags': tags,
      'status': status.value,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isEnrolled': isEnrolled,
      'userProgress': userProgress?.toJson(),
    };
  }
}

enum CourseType {
  recorded('recorded'),
  live('live'),
  hybrid('hybrid');

  const CourseType(this.value);
  final String value;

  static CourseType fromString(String value) {
    return CourseType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => CourseType.recorded,
    );
  }
}

enum CourseStatus {
  active('active'),
  inactive('inactive'),
  draft('draft'),
  archived('archived');

  const CourseStatus(this.value);
  final String value;

  static CourseStatus fromString(String value) {
    return CourseStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => CourseStatus.active,
    );
  }
}

class CoursePricing {
  final double originalPrice;
  final double? discountedPrice;
  final bool isFree;
  final String currency;
  final DateTime? discountValidUntil;

  CoursePricing({
    required this.originalPrice,
    this.discountedPrice,
    required this.isFree,
    required this.currency,
    this.discountValidUntil,
  });

  factory CoursePricing.fromJson(Map<String, dynamic> json) {
    return CoursePricing(
      originalPrice: (json['originalPrice'] ?? 0).toDouble(),
      discountedPrice: json['discountedPrice']?.toDouble(),
      isFree: json['isFree'] ?? false,
      currency: json['currency'] ?? 'INR',
      discountValidUntil: json['discountValidUntil'] != null 
          ? DateTime.parse(json['discountValidUntil']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'originalPrice': originalPrice,
      'discountedPrice': discountedPrice,
      'isFree': isFree,
      'currency': currency,
      'discountValidUntil': discountValidUntil?.toIso8601String(),
    };
  }

  double get effectivePrice => discountedPrice ?? originalPrice;
  bool get hasDiscount => discountedPrice != null && discountedPrice! < originalPrice;
}

class CourseContent {
  final int totalVideos;
  final int totalDuration; // in minutes
  final int totalPDFs;
  final int totalTests;
  final List<SubjectModel> subjects;

  CourseContent({
    required this.totalVideos,
    required this.totalDuration,
    required this.totalPDFs,
    required this.totalTests,
    required this.subjects,
  });

  factory CourseContent.fromJson(Map<String, dynamic> json) {
    return CourseContent(
      totalVideos: json['totalVideos'] ?? 0,
      totalDuration: json['totalDuration'] ?? 0,
      totalPDFs: json['totalPDFs'] ?? 0,
      totalTests: json['totalTests'] ?? 0,
      subjects: (json['subjects'] as List<dynamic>?)
          ?.map((subject) => SubjectModel.fromJson(subject))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalVideos': totalVideos,
      'totalDuration': totalDuration,
      'totalPDFs': totalPDFs,
      'totalTests': totalTests,
      'subjects': subjects.map((subject) => subject.toJson()).toList(),
    };
  }
}

class CourseStats {
  final int enrolledStudents;
  final double averageRating;
  final int totalRatings;
  final int completionRate; // percentage

  CourseStats({
    required this.enrolledStudents,
    required this.averageRating,
    required this.totalRatings,
    required this.completionRate,
  });

  factory CourseStats.fromJson(Map<String, dynamic> json) {
    return CourseStats(
      enrolledStudents: json['enrolledStudents'] ?? 0,
      averageRating: (json['averageRating'] ?? 0).toDouble(),
      totalRatings: json['totalRatings'] ?? 0,
      completionRate: json['completionRate'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enrolledStudents': enrolledStudents,
      'averageRating': averageRating,
      'totalRatings': totalRatings,
      'completionRate': completionRate,
    };
  }
}

class UserProgress {
  final double completionPercentage;
  final int videosWatched;
  final int testsAttempted;
  final int testsCompleted;
  final DateTime? lastAccessedAt;
  final String? lastWatchedVideoId;
  final int totalTimeSpent; // in minutes

  UserProgress({
    required this.completionPercentage,
    required this.videosWatched,
    required this.testsAttempted,
    required this.testsCompleted,
    this.lastAccessedAt,
    this.lastWatchedVideoId,
    required this.totalTimeSpent,
  });

  factory UserProgress.fromJson(Map<String, dynamic> json) {
    return UserProgress(
      completionPercentage: (json['completionPercentage'] ?? 0).toDouble(),
      videosWatched: json['videosWatched'] ?? 0,
      testsAttempted: json['testsAttempted'] ?? 0,
      testsCompleted: json['testsCompleted'] ?? 0,
      lastAccessedAt: json['lastAccessedAt'] != null 
          ? DateTime.parse(json['lastAccessedAt']) 
          : null,
      lastWatchedVideoId: json['lastWatchedVideoId'],
      totalTimeSpent: json['totalTimeSpent'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'completionPercentage': completionPercentage,
      'videosWatched': videosWatched,
      'testsAttempted': testsAttempted,
      'testsCompleted': testsCompleted,
      'lastAccessedAt': lastAccessedAt?.toIso8601String(),
      'lastWatchedVideoId': lastWatchedVideoId,
      'totalTimeSpent': totalTimeSpent,
    };
  }
}

class SubjectModel {
  final String id;
  final String name;
  final String description;
  final int videoCount;
  final int pdfCount;
  final int testCount;
  final int order;

  SubjectModel({
    required this.id,
    required this.name,
    required this.description,
    required this.videoCount,
    required this.pdfCount,
    required this.testCount,
    required this.order,
  });

  factory SubjectModel.fromJson(Map<String, dynamic> json) {
    return SubjectModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      videoCount: json['videoCount'] ?? 0,
      pdfCount: json['pdfCount'] ?? 0,
      testCount: json['testCount'] ?? 0,
      order: json['order'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'videoCount': videoCount,
      'pdfCount': pdfCount,
      'testCount': testCount,
      'order': order,
    };
  }
}
