import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/app_utils.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../providers/auth_provider.dart';
import '../../dashboard/presentation/dashboard_screen.dart';
import 'exam_preference_screen.dart';

class OTPVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  final bool isRegistration;

  const OTPVerificationScreen({
    super.key,
    required this.phoneNumber,
    this.isRegistration = false,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen>
    with TickerProviderStateMixin {
  final List<TextEditingController> _otpControllers = 
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _otpFocusNodes = 
      List.generate(6, (index) => FocusNode());
  
  late AnimationController _animationController;
  late AnimationController _timerController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isLoading = false;
  bool _canResend = false;
  int _resendTimer = 120; // 2 minutes
  Timer? _timer;
  String _otp = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _startResendTimer();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _timerController = AnimationController(
      duration: const Duration(seconds: 120),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));
  }

  void _startAnimations() {
    _animationController.forward();
    _timerController.forward();
  }

  void _startResendTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimer > 0) {
        setState(() {
          _resendTimer--;
        });
      } else {
        setState(() {
          _canResend = true;
        });
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _timerController.dispose();
    _timer?.cancel();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _otpFocusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _onOTPChanged(String value, int index) {
    setState(() {
      _otp = _otpControllers.map((controller) => controller.text).join();
    });

    if (value.isNotEmpty && index < 5) {
      _otpFocusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _otpFocusNodes[index - 1].requestFocus();
    }

    // Auto-verify when all digits are entered
    if (_otp.length == 6) {
      _handleVerifyOTP();
    }
  }

  void _handleVerifyOTP() async {
    if (_otp.length != 6) {
      AppUtils.showSnackBar(
        context,
        'Please enter complete OTP',
        backgroundColor: AppColors.warning,
        textColor: AppColors.white,
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.verifyOtp(
        phone: widget.phoneNumber,
        otp: _otp,
        name: widget.isRegistration ? 'User' : null, // You can add name field if needed
      );

      if (success) {
        final verifyResponse = authProvider.user;
        final isNewUser = verifyResponse?.examPreference.isEmpty ?? true;

        if (widget.isRegistration && isNewUser) {
          // Navigate to exam preference selection for new users
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const ExamPreferenceScreen(isOnboarding: true),
            ),
            (route) => false,
          );
        } else {
          // Navigate to dashboard for existing users
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const DashboardScreen(),
            ),
            (route) => false,
          );
        }

        AppUtils.showSnackBar(
          context,
          widget.isRegistration
              ? AppConstants.registrationSuccess
              : AppConstants.loginSuccess,
          backgroundColor: AppColors.success,
          textColor: AppColors.white,
        );
      } else {
        AppUtils.showSnackBar(
          context,
          authProvider.error ?? 'Invalid OTP. Please try again.',
          backgroundColor: AppColors.error,
          textColor: AppColors.white,
        );
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Invalid OTP. Please try again.',
        backgroundColor: AppColors.error,
        textColor: AppColors.white,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _handleResendOTP() async {
    if (!_canResend) return;

    setState(() {
      _canResend = false;
      _resendTimer = 120;
    });

    _startResendTimer();

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.sendOtp(widget.phoneNumber);

      if (success) {
        AppUtils.showSnackBar(
          context,
          AppConstants.otpSent,
          backgroundColor: AppColors.success,
          textColor: AppColors.white,
        );
      } else {
        AppUtils.showSnackBar(
          context,
          authProvider.error ?? 'Failed to resend OTP. Please try again.',
          backgroundColor: AppColors.error,
          textColor: AppColors.white,
        );
      }
    } catch (e) {
      AppUtils.showSnackBar(
        context,
        'Failed to resend OTP. Please try again.',
        backgroundColor: AppColors.error,
        textColor: AppColors.white,
      );
    }
  }

  String _formatTimer(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary,
              AppColors.primaryMedium,
            ],
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 40),
                        
                        // Header Section
                        _buildHeader(),
                        
                        const SizedBox(height: 48),
                        
                        // OTP Input Section
                        _buildOTPSection(),
                        
                        const SizedBox(height: 32),
                        
                        // Verify Button
                        _buildVerifyButton(),
                        
                        const SizedBox(height: 24),
                        
                        // Resend Section
                        _buildResendSection(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.sms,
            size: 40,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Verify Phone Number',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Enter the 6-digit code sent to',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.accentLight,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          AppUtils.formatPhoneNumber(widget.phoneNumber),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOTPSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Enter OTP',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // OTP Input Fields
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(6, (index) {
              return SizedBox(
                width: 45,
                height: 55,
                child: TextField(
                  controller: _otpControllers[index],
                  focusNode: _otpFocusNodes[index],
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  maxLength: 1,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  decoration: InputDecoration(
                    counterText: '',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.primary, width: 2),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (value) => _onOTPChanged(value, index),
                ),
              );
            }),
          ),
          
          const SizedBox(height: 16),
          
          // Timer
          if (!_canResend)
            Text(
              'Resend OTP in ${_formatTimer(_resendTimer)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVerifyButton() {
    return CustomButton(
      text: 'Verify OTP',
      onPressed: _isLoading || _otp.length != 6 ? null : _handleVerifyOTP,
      isLoading: _isLoading,
      gradient: AppColors.primaryGradient,
    );
  }

  Widget _buildResendSection() {
    return Column(
      children: [
        Text(
          "Didn't receive the code?",
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.accentLight,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _canResend ? _handleResendOTP : null,
          child: Text(
            'Resend OTP',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: _canResend ? AppColors.white : AppColors.greyLight,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
              decorationColor: _canResend ? AppColors.white : AppColors.greyLight,
            ),
          ),
        ),
      ],
    );
  }
}
